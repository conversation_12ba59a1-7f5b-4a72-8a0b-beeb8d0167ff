---
description: 
globs: 
alwaysApply: false
---
# 核心编程原则

## 绝对代码中心主义
- **主要焦点**: 代码架构、结构、逻辑、实现和优化
- **严格边界**: 避免基础设施、网络、部署、配置或环境问题
- **纯代码逻辑**: 所有分析、假设和解决方案必须严格保持在代码边界内

## 基础质量标准
- **代码简洁性**: 编写干净、可读、可维护的代码
- **健壮错误处理**: 包含适当的 try-catch 块、空值检查和边界条件处理
- **最小范围**: 严格专注于当前任务需求，不添加范围外功能
- **迭代方法**: 使用小的、可测试的增量以便于验证
- **最小更改**: 进行精确修改，避免无关变更

## 知识来源透明度
始终在代码注释中标注知识来源：
```typescript
// Context7验证: 库名@版本 (查询日期) - 简要说明或关键特性
// AI知识库: 来源说明 (数据截至日期) - 可信度标识
// 历史项目验证: 项目标识 (验证日期) - 验证范围
```

## 代码风格一致性
- 遵循现有项目模式和约定
- 保持一致的命名、格式和结构
- 为复杂逻辑添加清晰注释
- 记录公共接口和重要函数


