from typing import Dict, List, <PERSON>ple
import pandas as pd
import numpy as np
from sklearn.linear_model import TheilSenRegressor
from scipy.stats import gaussian_kde
from scipy.signal import argrelextrema
import logging
from utils.time_utils import timeit

# 设置日志
logger = logging.getLogger(__name__)

class PatternAnalyzer:
    """
    技术形态分析器 - 平台无关的核心分析逻辑
    
    该类实现了各种技术分析形态的识别，包括：
    - 趋势线突破
    - 底部箱体突破
    - 成交量放大
    - 收敛三角形
    - 头肩底形态
    - K线组合
    - 回调买点
    - 老鸭头形态
    - 茶杯带柄形态
    """
    
    def __init__(self):
        self.min_pattern_bars = 15
        self.error_margin = 0.001
        self.pullback_bars = 3

    def analyze_trend_line_breakout(self, data: pd.DataFrame) -> Tuple[bool, str]:
        """趋势线突破分析"""
        try:
            if len(data) < 20:
                return False, ""
            
            # 使用最近的数据进行分析
            recent_data = data.tail(20).copy()
            
            # 保持价格精度
            current_price = recent_data['close'].iloc[-1]
            
            # 使用TheilSenRegressor进行趋势线拟合
            X = np.arange(len(recent_data)).reshape(-1, 1)
            y = recent_data['low'].values
            
            model = TheilSenRegressor(random_state=42)
            model.fit(X, y)
            
            trend_line = model.predict(X)
            trend_value = trend_line[-1]
            slope = model.coef_[0]
            score = model.score(X, y)
            
            # 格式化输出时保持完整精度
            info = (
                f"最新收盘价: {current_price:.8f}, "  # 增加小数位数
                f"趋势线值: {trend_value:.8f}, "      # 增加小数位数
                f"斜率: {slope:.8f}, "               # 增加小数位数
                f"R方: {score:.4f}, "
                f"使用方法: Theil-Sen"
            )
            
            # 其他逻辑保持不变
            is_breakout = (
                current_price > trend_value and  # 价格在趋势线上方
                slope > 0 and                    # 趋势向上
                score > -0.5                     # R方值合理
            )
            
            return is_breakout, info
            
        except Exception as e:
            logger.error(f"趋势线突破分析出错: {str(e)}")
            return False, ""

    def analyze_support_box_breakout(self, data: pd.DataFrame) -> Tuple[bool, str]:
        """
        底部箱体突破分析 - 使用KDE优化
        """
        try:
            # 获取最近80根K线的收盘价
            recent_data = data['close'].values[-80:]
            if len(recent_data) < 5:
                return False, "数据点数不足"

            # 使用KDE识别价格密集区域
            kde = gaussian_kde(recent_data)
            price_range = np.linspace(min(recent_data), max(recent_data), 100)
            density = kde(price_range)
            
            # 找到密度最高的区域作为箱体范围
            high_density_mask = density > np.percentile(density, 70)  # 取密度前30%的区域
            box_bottom = price_range[high_density_mask][0]
            box_top = price_range[high_density_mask][-1]
            
            # 使用局部极值点计算有效触点
            extrema_idx = np.concatenate([
                argrelextrema(recent_data, np.less)[0],    # 局部最小值
                argrelextrema(recent_data, np.greater)[0]  # 局部最大值
            ])
            touch_points = sum(1 for idx in extrema_idx 
                            if box_bottom * (1 - self.error_margin) <= recent_data[idx] <= box_top * (1 + self.error_margin))

            latest_close = data['close'].iloc[-1]
            
            # 判断是否突破
            result = latest_close > box_top * (1 + self.error_margin) and touch_points >= 3
            
            # 计算当前价格的密度得分
            density_score = kde(latest_close)[0] / np.max(density)
            
            # 格式化输出时保持完整精度
            info = (
                f"箱体范围: {box_bottom:.8f} - {box_top:.8f}, "  # 增加小数位数
                f"触点数量: {touch_points}, "
                f"价格密度: {density_score:.2f}"
            )
            
            return result, info
            
        except Exception as e:
            logger.error(f"底部箱体突破分析出错: {str(e)}")
            return False, ""

    def analyze_volume_surge(self, data: pd.DataFrame) -> Tuple[bool, str]:
        """
        成交量放大分析
        
        检查是否出现放量阳线，通过比较当前成交量与前期平均成交量。
        
        参数:
            data (pd.DataFrame): 包含OHLCV数据的DataFrame
                必须包含的列：'open', 'close', 'tick_volume'
        
        返回:
            Tuple[bool, str]: (是否放量, 详细信息)
                - 第一个元素表示是否出现放量
                - 第二个元素包含详细的分析信息
        
        分析逻辑:
        1. 检查最新K线是否为阳线
        2. 比较当前成交量与前5根K线的平均成交量
        3. 当前成交量超过1.5倍平均值视为放量
        """
        try:
            # 获取最近6根K线数据
            recent_data = data[-6:]
            if len(recent_data) < 6:
                return False, ""

            # 检查是否是阳线
            latest_close = recent_data['close'].iloc[-1]
            latest_open = recent_data['open'].iloc[-1]
            if latest_close <= latest_open:
                return False, ""

            # 计算成交量变化
            latest_volume = recent_data['tick_volume'].iloc[-1]
            previous_volumes = recent_data['tick_volume'].iloc[-6:-1]
            average_previous_volume = previous_volumes.mean()

            # 判断是否放量
            result = latest_volume > 1.5 * average_previous_volume
            info = (f"当前成交量: {latest_volume}, "
                   f"平均成交量: {average_previous_volume:.2f}, "
                   f"放大倍数: {latest_volume/average_previous_volume:.2f}")
            return result, info

        except Exception as e:
            logger.error(f"成交量分析出错: {str(e)}")
            return False, ""

    def analyze_converging_triangle(self, data: pd.DataFrame) -> Tuple[bool, str]:
        """
        收敛三角形形态分析
        
        使用改进的算法检测收敛三角形形态，包括更严格的判断条件。
        
        参数:
            data (pd.DataFrame): 包含OHLCV数据的DataFrame
                必须包含的列：'high', 'low'
        
        返回:
            Tuple[bool, str]: (是否形成收敛三角形, 详细信息)
                - 第一个元素表示是否形成有效的收敛三角形
                - 第二个元素包含详细的分析信息
        
        分析逻辑:
        1. 使用扩展窗口识别关键高低点
        2. 验证点位的时间分布均匀性
        3. 使用Theil-Sen回归计算趋势线
        4. 验证收敛特征和拟合度
        """
        try:
            recent_data = data[-60:]  # 保持60根K线窗口
            if len(recent_data) < self.min_pattern_bars:
                return False, ""

            highs = recent_data['high'].values
            lows = recent_data['low'].values
            
            # 1. 高低点识别
            high_peaks = []
            low_troughs = []
            window_size = 5
            
            for i in range(window_size, len(recent_data)-window_size):
                # 识别高点
                local_high_window = highs[i-window_size:i+window_size+1]
                if highs[i] == max(local_high_window):
                    high_peaks.append((i, highs[i]))
                
                # 识别低点
                local_low_window = lows[i-window_size:i+window_size+1]
                if lows[i] == min(local_low_window):
                    low_troughs.append((i, lows[i]))

            if len(high_peaks) >= 4 and len(low_troughs) >= 4:
                # 2. 选取最近的点位
                recent_highs = high_peaks[-4:]
                recent_lows = low_troughs[-4:]
                
                # 3. 验证高低点趋势
                high_prices = [h[1] for h in recent_highs]
                low_prices = [l[1] for l in recent_lows]
                if not (all(high_prices[i] > high_prices[i+1] for i in range(len(high_prices)-1)) and
                        all(low_prices[i] < low_prices[i+1] for i in range(len(low_prices)-1))):
                    return False, ""
                
                # 4. 验证时间间隔均匀性
                high_intervals = [recent_highs[i+1][0] - recent_highs[i][0] for i in range(len(recent_highs)-1)]
                low_intervals = [recent_lows[i+1][0] - recent_lows[i][0] for i in range(len(recent_lows)-1)]
                avg_interval = (sum(high_intervals) + sum(low_intervals)) / (len(high_intervals) + len(low_intervals))
                
                if any(abs(interval - avg_interval) > avg_interval * 0.5 for interval in high_intervals + low_intervals):
                    return False, ""
                
                # 5. 计算趋势线
                high_x = np.array([p[0] for p in recent_highs]).reshape(-1, 1)
                high_y = np.array([p[1] for p in recent_highs])
                low_x = np.array([p[0] for p in recent_lows]).reshape(-1, 1)
                low_y = np.array([p[1] for p in recent_lows])
                
                high_reg = TheilSenRegressor(random_state=0)
                low_reg = TheilSenRegressor(random_state=0)
                high_reg.fit(high_x, high_y)
                low_reg.fit(low_x, low_y)
                
                high_slope = high_reg.coef_[0]
                low_slope = low_reg.coef_[0]
                
                # 6. 计算形态特征
                high_score = high_reg.score(high_x, high_y)
                low_score = low_reg.score(low_x, low_y)
                
                start_width = abs(recent_highs[0][1] - recent_lows[0][1])
                end_width = abs(recent_highs[-1][1] - recent_lows[-1][1])
                width_ratio = end_width / start_width
                
                # 7. 判断条件
                result = (
                    high_slope < -0.0002 and
                    low_slope > 0.0002 and
                    width_ratio < 0.6 and
                    high_score > 0.7 and
                    low_score > 0.7 and
                    abs(high_slope) > abs(low_slope) * 0.5 and
                    abs(high_slope) < abs(low_slope) * 2
                )
                
                if result:
                    info = (
                        f"收敛比: {width_ratio:.2f}, "
                        f"趋势线拟合度: {min(high_score, low_score):.2f}, "
                        f"点位间隔均匀度: {min(avg_interval/max(high_intervals + low_intervals), max(high_intervals + low_intervals)/avg_interval):.2f}"
                    )
                    return True, info

            return False, ""
            
        except Exception as e:
            logger.error(f"收敛三角形分析出错: {str(e)}")
            return False, ""

    def analyze_head_shoulders(self, data: pd.DataFrame) -> Tuple[bool, str]:
        """
        头肩底形态分析 - 简化版
        
        主要判断:
        1. 有三个低点
        2. 中间低点最低
        3. 左右肩大致对称
        4. 有基本的反弹确认
        """
        try:
            recent_data = data[-30:]  # 只看最近30根K线
            if len(recent_data) < 10:  # 至少需要10根K线
                return False, ""

            lows = recent_data['low'].values
            highs = recent_data['high'].values
            volumes = recent_data['tick_volume'].values
            low_peaks = []
            
            # 寻找低点 - 使用较小的窗口
            window_size = 3  # 左右各看3根K线
            for i in range(window_size, len(lows)-window_size):
                local_window = lows[i-window_size:i+window_size+1]
                if lows[i] == min(local_window):
                    # 记录低点信息
                    low_peaks.append({
                        'index': i,
                        'price': lows[i],
                        'volume': volumes[i],
                        'high_after': max(highs[i:i+window_size])  # 记录反弹高点
                    })

            if len(low_peaks) >= 3:
                # 选取最近三个低点
                last_three = low_peaks[-3:]
                left_shoulder = last_three[0]
                head = last_three[1]
                right_shoulder = last_three[2]
                
                # 基本特征判断
                head_to_left = abs(head['price'] - left_shoulder['price'])
                head_to_right = abs(head['price'] - right_shoulder['price'])
                shoulders_diff = abs(left_shoulder['price'] - right_shoulder['price'])
                pattern_depth = min(head_to_left, head_to_right) / head['price']
                
                # 计算反弹
                left_rebound = left_shoulder['high_after'] / left_shoulder['price'] - 1
                right_rebound = right_shoulder['high_after'] / right_shoulder['price'] - 1
                
                # 简化的判断条件
                is_valid = (
                    head['price'] < min(left_shoulder['price'], right_shoulder['price']) and  # 头部最低
                    shoulders_diff < min(head_to_left, head_to_right) * 0.8 and  # 肩部差异放宽到80%
                    pattern_depth > 0.003 and  # 形态深度降低到0.3%
                    min(left_rebound, right_rebound) > 0.001  # 确保有最小反弹(0.1%)
                )
                
                if is_valid:
                    info = (
                        f"形态深度: {pattern_depth:.3f}, "
                        f"肩部差异率: {shoulders_diff/min(head_to_left, head_to_right):.2f}, "
                        f"左肩反弹: {left_rebound:.3f}, "
                        f"右肩反弹: {right_rebound:.3f}"
                    )
                    return True, info

            return False, ""
            
        except Exception as e:
            logger.error(f"头肩底分析出错: {str(e)}")
            return False, ""

    #以下是旧的分析方法，暂时保留
    # def analyze_head_shoulders(self, data: pd.DataFrame) -> Tuple[bool, str]:
    #     """
    #     头肩底形态分析
        
    #     使用改进的算法检测头肩底形态，包括成交量确认。
        
    #     参数:
    #         data (pd.DataFrame): 包含OHLCV数据的DataFrame
    #             必须包含的列：'low', 'tick_volume'
        
    #     返回:
    #         Tuple[bool, str]: (是否形成头肩底, 详细信息)
    #             - 第一个元素表示是否形成有效的头肩底
    #             - 第二个元素包含详细的分析信息
        
    #     分析逻辑:
    #     1. 识别关键低点
    #     2. 验证时间间隔均匀性
    #     3. 验证形态特征和深度
    #     4. 确认成交量特征
    #     """
    #     try:
    #         recent_data = data[-50:]
    #         if len(recent_data) < self.min_pattern_bars:
    #             return False, ""

    #         lows = recent_data['low'].values
    #         volumes = recent_data['tick_volume'].values
    #         low_peaks = []
            
    #         # 1. 识别低点
    #         window_size = 3
    #         for i in range(window_size, len(lows)-window_size):
    #             local_window = lows[i-window_size:i+window_size+1]
    #             if lows[i] == min(local_window):
    #                 low_peaks.append((i, lows[i]))

    #         if len(low_peaks) >= 3:
    #             # 2. 选取最近三个低点
    #             last_three = low_peaks[-3:]
    #             time_indexes = [p[0] for p in last_three]
    #             left_shoulder, head, right_shoulder = [p[1] for p in last_three]
                
    #             # 3. 验证时间隔
    #             left_span = time_indexes[1] - time_indexes[0]
    #             right_span = time_indexes[2] - time_indexes[1]
    #             if abs(left_span - right_span) > min(left_span, right_span) * 0.5:
    #                 return False, ""
                
    #             # 4. 计算形态特征
    #             head_to_left = abs(head - left_shoulder)
    #             head_to_right = abs(head - right_shoulder)
    #             shoulders_diff = abs(left_shoulder - right_shoulder)
    #             pattern_depth = min(head_to_left, head_to_right) / head
                
    #             # 5. 验证成交量
    #             head_volume = volumes[time_indexes[1]]
    #             shoulders_volume = (volumes[time_indexes[0]] + volumes[time_indexes[2]]) / 2
    #             volume_pattern = head_volume > shoulders_volume * 1.2
                
    #             # 6. 综合判断
    #             is_valid = (
    #                 head < left_shoulder and 
    #                 head < right_shoulder and 
    #                 shoulders_diff < min(head_to_left, head_to_right) * 0.3 and
    #                 pattern_depth > 0.005 and
    #                 volume_pattern
    #             )
                
    #             if is_valid:
    #                 info = (
    #                     f"形态深度: {pattern_depth:.3f}, "
    #                     f"肩部差异: {shoulders_diff/min(head_to_left, head_to_right):.2f}, "
    #                     f"时间均匀性: {min(left_span, right_span)/max(left_span, right_span):.2f}"
    #                 )
    #                 return True, info

    #         return False, ""
            
    #     except Exception as e:
    #         logger.error(f"头肩底分析出错: {str(e)}")
    #         return False, ""

    def analyze_k_line_pattern(self, data: pd.DataFrame) -> Tuple[bool, str]:
        """
        K线组合形态分析
        
        分析K线组合形态，特别关注大阳线及其特征。
        
        参数:
            data (pd.DataFrame): 包含OHLCV数据的DataFrame
                必须包含的列：'open', 'close', 'high', 'low', 'tick_volume'
        
        返回:
            Tuple[bool, str]: (是否形成有效K线组合, 详细信息)
                - 第一个元素表示是否形成有效的K线组合
                - 第二个元素包含详细的分析信息
        
        分析逻辑:
        1. 分析最近8根K线的走势
        2. 验证当前K线的实体和影线特征
        3. 确认成交量放大情况
        4. 考虑前期走势背景
        """
        try:
            recent_data = data[-8:]  # 增加到8根K线以便分析前期走势
            if len(recent_data) < 8:
                return False, ""

            # 1. 基础数据准备
            latest = recent_data.iloc[-1]
            latest_open = latest['open']
            latest_close = latest['close']
            latest_high = latest['high']
            latest_low = latest['low']
            latest_volume = latest['tick_volume']

            # 2. 计算K线特征
            current_body = latest_close - latest_open
            if current_body <= 0:
                return False, ""  # 不是阳线

            body_size = abs(current_body)
            upper_shadow = latest_high - max(latest_open, latest_close)
            lower_shadow = min(latest_open, latest_close) - latest_low
            
            # 3. 分析前期走势
            previous_closes = recent_data['close'].iloc[-8:-1]
            previous_trend = (previous_closes.iloc[-1] - previous_closes.iloc[0]) / previous_closes.iloc[0]
            
            # 4. 计算相对大小
            previous_bodies = abs(recent_data['close'].iloc[-8:-1] - recent_data['open'].iloc[-8:-1])
            avg_body = previous_bodies.mean()
            body_ratio = body_size / (latest_high - latest_low)
            
            # 5. 成交量分析
            previous_volumes = recent_data['tick_volume'].iloc[-8:-1]
            avg_volume = previous_volumes.mean()
            volume_ratio = latest_volume / avg_volume
            
            # 6. 综合判断
            is_valid = (
                body_ratio >= 0.6 and                # 实体比例要求降低到60%
                body_size > 1.5 * avg_body and       # 相对大小要求降低到1.5倍
                upper_shadow < body_size * 0.3 and   # 上影线不能太长
                lower_shadow < body_size * 0.3 and   # 下影线不能太长
                volume_ratio > 1.2 and               # 要求放量
                previous_trend <= 0                  # 前期应该是下跌或盘整
            )
            
            if is_valid:
                info = (
                    f"实体比例: {body_ratio:.2f}, "
                    f"量比: {volume_ratio:.2f}, "
                    f"影线比例: {max(upper_shadow, lower_shadow)/body_size:.2f}"
                )
                return True, info

            return False, ""
            
        except Exception as e:
            logger.error(f"K线组合分析出错: {str(e)}")
            return False, ""

    def analyze_pullback_entry(self, data: pd.DataFrame) -> Tuple[bool, str]:
        """
        回调买点分析
        
        分析价格回调到斐波那契回调位的买点机会。
        
        参数:
            data (pd.DataFrame): 包含OHLCV数据的DataFrame
                必须包含的列：'high', 'low', 'close', 'open'
        
        返回:
            Tuple[bool, str]: (是否形成回调买点, 详细信息)
                - 第一个元素表示是否形成有效的回调买点
                - 第二个元素包含详细的分析信息
        
        分析逻辑:
        1. 确定前期上涨趋势
        2. 计算关键斐波那契回调位
        3. 验证价格回调到位
        4. 确认企稳反弹
        """
        try:
            if len(data) < 20:
                return False, ""
            
            recent_data = data.tail(20)
            
            # 1. 找到起涨点（前期低点）
            start_point = recent_data['low'].iloc[:-5].min()
            
            # 2. 找到突破后的最高点
            breakout_high = recent_data['high'].iloc[-5:-1].max()
            
            # 确保有上涨趋势
            if breakout_high <= start_point:
                return False, ""
            
            # 计算上涨幅度
            uptrend_range = breakout_high - start_point
            
            # 计算斐波那契回调位
            fib_618 = breakout_high - uptrend_range * 0.618
            fib_500 = breakout_high - uptrend_range * 0.500
            fib_382 = breakout_high - uptrend_range * 0.382
            
            # 获取最新K线
            current_bar = recent_data.iloc[-1]
            prev_bar = recent_data.iloc[-2]
            
            # 检查是否在回调区域
            in_fib_zone = (
                (current_bar['low'] >= fib_618 * 0.99 and current_bar['low'] <= fib_382 * 1.01) or
                (current_bar['open'] >= fib_618 * 0.99 and current_bar['open'] <= fib_382 * 1.01)
            )
            
            # 检查是否站稳（收盘价高于前一根K线的高点）
            stands_firm = current_bar['close'] > prev_bar['high']
            
            # 检查是否有放量或K线组合
            has_volume = self.analyze_volume_surge(recent_data.tail(6))[0]
            has_k_pattern = self.analyze_k_line_pattern(recent_data.tail(6))[0]
            
            if in_fib_zone and stands_firm and (has_volume or has_k_pattern):
                info = (
                    f"回调位置: {(breakout_high - current_bar['low'])/uptrend_range:.2f}, "
                    f"站稳确认: {'是' if stands_firm else '否'}, "
                    f"成交量确认: {'是' if has_volume else '否'}, "
                    f"K线确认: {'是' if has_k_pattern else '否'}"
                )
                return True, info
            
            return False, ""
            
        except Exception as e:
            logger.error(f"回调买点分析出错: {str(e)}")
            return False, ""

    def analyze_duck_head(self, data: pd.DataFrame) -> Tuple[bool, str]:
        """
        老鸭头形态分析
        
        分析老鸭头形态，包括嘴巴、头部和颈部的位置关系。
        
        参数:
            data (pd.DataFrame): 包含OHLCV数据的DataFrame
                必须包含的列：'high', 'low'
        
        返回:
            Tuple[bool, str]: (是否形成老鸭头形态, 详细信息)
                - 第一个元素表示是否形成有效的老鸭头形态
                - 第二个元素包含详细的分析信息
        
        分析逻辑:
        1. 识别起涨点（嘴巴位置）
        2. 确认突破高点（头部位置）
        3. 验证回调低点（颈部位置）
        4. 检查形态的比例关系
        """
        try:
            if len(data) < 30:
                return False, ""
            
            recent_data = data.tail(30)
            
            # 1. 找到起涨点（鸭子的嘴巴位置）
            start_point = recent_data['low'].iloc[:-15].min()
            start_index = recent_data['low'].iloc[:-15].idxmin()
            
            # 2. 找到突破高点（鸭子头顶）
            head_high = recent_data['high'].iloc[-15:-5].max()
            head_index = recent_data['high'].iloc[-15:-5].idxmax()
            
            # 3. 找到回调低点（鸭子脖子）
            neck_low = recent_data['low'].iloc[-5:].min()
            neck_index = recent_data['low'].iloc[-5:].idxmin()
            
            # 4. 检查形态
            # 确保点位的先后顺序：嘴巴 -> 头顶 -> 脖子
            if not (start_index < head_index < neck_index):
                return False, ""
            
            # 5. 检查高度关系
            head_height = head_high - start_point
            neck_height = head_high - neck_low
            
            # 验证老鸭头特征
            is_duck_head = (
                head_height > 0 and 
                0.5 <= neck_height/head_height <= 0.7  # 脖子回调在头部高度的50-70%位置
            )
            
            # 6. 确认信号
            has_volume = self.analyze_volume_surge(recent_data.tail(6))[0]
            has_k_pattern = self.analyze_k_line_pattern(recent_data.tail(6))[0]
            
            if is_duck_head and (has_volume or has_k_pattern):
                info = (
                    f"起点: {start_point:.2f}, "
                    f"头部: {head_high:.2f}, "
                    f"颈部: {neck_low:.2f}, "
                    f"回调比例: {(neck_height/head_height):.2f}"
                )
                return True, info
            
            return False, ""
            
        except Exception as e:
            logger.error(f"老鸭头形态分析出错: {str(e)}")
            return False, ""

    def analyze_cup_handle(self, data: pd.DataFrame) -> Tuple[bool, str]:
        """
        茶杯带柄形态分析
        
        分析茶杯带柄形态，包括杯口、杯底和手柄的位置关系。
        
        参数:
            data (pd.DataFrame): 包含OHLCV数据的DataFrame
                必须包含的列：'high', 'low'
        
        返回:
            Tuple[bool, str]: (是否形成茶杯带柄形态, 详细信息)
                - 第一个元素表示是否形成有效的茶杯带柄形态
                - 第二个元素包含详细的分析信息
        
        分析逻辑:
        1. 识别左侧杯口
        2. 确认杯底位置
        3. 验证右侧杯口
        4. 检查手柄特征
        5. 验证整体形态的比例关系
        """
        try:
            if len(data) < 40:
                return False, ""
            
            recent_data = data.tail(40)
            
            # 1. 找到关键点位
            left_high = recent_data['high'].iloc[:-25].max()
            left_index = recent_data['high'].iloc[:-25].idxmax()
            
            cup_low = recent_data['low'].iloc[-25:-10].min()
            cup_index = recent_data['low'].iloc[-25:-10].idxmin()
            
            right_high = recent_data['high'].iloc[-10:-5].max()
            right_index = recent_data['high'].iloc[-10:-5].idxmax()
            
            handle_low = recent_data['low'].iloc[-5:].min()
            handle_index = recent_data['low'].iloc[-5:].idxmin()
            
            # 2. 验证点位顺序
            if not (left_index < cup_index < right_index < handle_index):
                return False, ""
            
            # 3. 计算关键比例
            cup_depth = min(left_high, right_high) - cup_low
            handle_depth = right_high - handle_low
            cup_width = right_index - left_index
            handle_width = handle_index - right_index
            
            # 4. 验证形态特征
            is_cup_handle = (
                abs(left_high - right_high) / left_high < 0.1 and  # 杯口平衡
                0.2 <= cup_depth/left_high <= 0.4 and  # 适当的杯深
                0.3 <= handle_depth/cup_depth <= 0.5 and  # 合适的手柄深度
                handle_width < cup_width * 0.5  # 手柄比杯身窄
            )
            
            # 5. 确认信号
            has_volume = self.analyze_volume_surge(recent_data.tail(6))[0]
            has_k_pattern = self.analyze_k_line_pattern(recent_data.tail(6))[0]
            
            if is_cup_handle and (has_volume or has_k_pattern):
                info = (
                    f"左杯口: {left_high:.2f}, "
                    f"杯底: {cup_low:.2f}, "
                    f"右杯口: {right_high:.2f}, "
                    f"手柄低点: {handle_low:.2f}, "
                    f"杯深比例: {(cup_depth/left_high):.2f}, "
                    f"手柄深度比例: {(handle_depth/cup_depth):.2f}"
                )
                return True, info
            
            return False, ""
            
        except Exception as e:
            logger.error(f"茶杯带柄形态分析出错: {str(e)}")
            return False, ""
        
    def analyze_break_bottom_reversal(self, data: pd.DataFrame) -> Tuple[bool, str]:
        """
        破底翻形态分析（做多反转机会）
        
        分析破底翻形态，当市场价格在下跌过程中跌破关键低点，
        随后迅速出现反转阳线（突破最低低点后出现明显反弹）时视为买入信号。
        
        参数:
            data (pd.DataFrame): 包含OHLCV数据的DataFrame，
                必须包含的列：'open', 'close', 'high', 'low', 'tick_volume'
        
        返回:
            Tuple[bool, str]: (是否形成破底翻形态, 详细信息)
                - 第一个元素表示是否形成有效的破底翻形态
                - 第二个元素包含详细的分析信息
        """
        try:
            # 至少需要20根K线用于识别底部形态
            if len(data) < 20:
                return False, "数据量不足"

            recent_data = data.tail(20).reset_index(drop=True)
            
            # 1. 找出这段时间内的最低低点及其位置（视为“破底”）
            lowest_low = recent_data['low'].min()
            lowest_pos = int(recent_data['low'].idxmin())
            
            # 若最低低点不在倒数第一根K线，则考虑其后的反转K线（破底后的翻转）
            if lowest_pos >= len(recent_data) - 1:
                return False, "未能形成反转信号"

            reversal_bar = recent_data.iloc[lowest_pos + 1]
            
            # 2. 判断反转K线是否为阳线且有足够反弹（回升幅度至少0.5%）
            rebound_valid = (
                reversal_bar['close'] > reversal_bar['open'] and
                reversal_bar['close'] >= lowest_low * 1.005
            )
            
            # 3. 结合成交量确认
            # 计算最近20根K线的平均成交量
            avg_volume = recent_data['tick_volume'].mean()
            volume_valid = reversal_bar['tick_volume'] > avg_volume * 1.2
            
            # 4. 综合判断：最低低点后出现反转阳线且幅度与量能均满足要求
            is_valid = rebound_valid and volume_valid
            
            info = (
                f"最低低点: {lowest_low:.8f} (位置: {lowest_pos}),\n"
                f"反转K线: 开:{reversal_bar['open']:.8f}, 收:{reversal_bar['close']:.8f},\n"
                f"反弹幅度: {(reversal_bar['close']/lowest_low - 1)*100:.2f}%,\n"
                f"反转量能: {reversal_bar['tick_volume']:.2f} (均量: {avg_volume:.2f})"
            )
            
            return is_valid, info
            
        except Exception as e:
            logger.error(f"破底翻形态分析出错: {str(e)}")
            return False, f"分析出错: {str(e)}"
            
    def analyze_support_resistance(self, data: pd.DataFrame, min_strength: float = 0.3) -> Tuple[bool, str]:
        """
        分析支撑阻力区间
        
        参数:
            data: OHLCV数据
            min_strength: 最小强度阈值，默认0.3
            
        强度等级:
            > 0.7: 非常强
            0.5-0.7: 强
            0.3-0.5: 中等
            < 0.3: 弱
        """
        try:
            if len(data) < 150:
                return False, "数据不足150根K线，无法进行支撑阻力分析"
                
            current_price = data['close'].iloc[-1]
            
            # 计算价格区间 - 使用收盘价范围
            price_range = data['close'].max() - data['close'].min()
            zone_width = price_range * 0.001  # 保持0.1%的区间宽度
            
            # 创建价格区间 - 主要关注收盘价
            price_zones = {}
            for i, row in enumerate(data.itertuples()):
                # 以收盘价为主，其他价格权重降低
                prices = [
                    (row.close, 1.0),  # 收盘价权重1.0
                    (row.open, 0.5),   # 开盘价权重0.5
                    (row.high, 0.3),   # 最高价权重0.3
                    (row.low, 0.3)     # 最低价权重0.3
                ]
                
                for price, weight in prices:
                    zone_key = round(price / zone_width) * zone_width
                    
                    if zone_key not in price_zones:
                        price_zones[zone_key] = {
                            'volume': 0,
                            'touches': 0,
                            'close_touches': 0,  # 新增：收盘价触及次数
                            'last_touch': i,
                            'price_range': (zone_key - zone_width/2, zone_key + zone_width/2)
                        }
                    
                    price_zones[zone_key]['volume'] += row.tick_volume * weight
                    price_zones[zone_key]['touches'] += weight
                    if price == row.close:  # 如果是收盘价触及
                        price_zones[zone_key]['close_touches'] += 1
                    price_zones[zone_key]['last_touch'] = i

            # 计算区间强度 - 加入收盘价触及因子
            for zone in price_zones.values():
                # 时间衰减调整 - 使用更合理的衰减
                days_passed = len(data) - zone['last_touch']
                time_factor = 1 / (1 + np.log1p(days_passed * 0.1))
                
                # 成交量因子
                max_volume = max(z['volume'] for z in price_zones.values())
                volume_factor = zone['volume'] / max_volume
                
                # 触及次数因子 - 加入收盘价触及权重
                max_touches = max(z['touches'] for z in price_zones.values())
                touch_factor = zone['touches'] / max_touches
                
                # 收盘价触及因子
                max_close_touches = max(z['close_touches'] for z in price_zones.values())
                close_touch_factor = zone['close_touches'] / max(max_close_touches, 1)
                
                # 新的强度计算公式
                zone['strength'] = (
                    volume_factor * 0.3 +
                    touch_factor * 0.3 +
                    close_touch_factor * 0.3 +
                    time_factor * 0.1
                )
                zone['strength'] = round(zone['strength'], 3)

            # 筛选重要区间时增加强度过滤
            support_zones = []
            resistance_zones = []
            
            # 先收集所有区间
            for price, zone in price_zones.items():
                if zone['strength'] < min_strength:
                    continue
                    
                if price < current_price:
                    support_zones.append({
                        'price': price,
                        'range': zone['price_range'],
                        'strength': round(zone['strength'], 3)
                    })
                elif price > current_price:
                    resistance_zones.append({
                        'price': price,
                        'range': zone['price_range'],
                        'strength': round(zone['strength'], 3)
                    })
            
            # 分别对支撑位和阻力位按照价格排序
            support_zones.sort(key=lambda x: x['price'], reverse=True)  # 支撑位从高到低
            resistance_zones.sort(key=lambda x: x['price'])  # 阻力位从低到高
            
            # 只保留最强的两个区间
            support_zones = support_zones[:2]
            resistance_zones = resistance_zones[:2]
            
            # 如果没有足够强的区间
            if not (support_zones or resistance_zones):
                return False, "未找到强度大于0.3的支撑阻力区间"
                
            # 格式化输出信息
            resistance_info = []
            support_info = []
            for i, z in enumerate(resistance_zones):
                strength_level = (
                    "非常强" if z['strength'] > 0.7 else
                    "强" if z['strength'] > 0.5 else
                    "中等"
                )
                resistance_info.append(
                    f"向上第{i+1}区间: {z['range'][0]:.8f}-{z['range'][1]:.8f} "
                    f"(强度:{z['strength']:.2f} - {strength_level}, "
                    f"收盘触及:{price_zones[z['price']]['close_touches']}次)"
                )
                
            for i, z in enumerate(support_zones):
                strength_level = (
                    "非常强" if z['strength'] > 0.7 else
                    "强" if z['strength'] > 0.5 else
                    "中等"
                )
                support_info.append(
                    f"向下第{i+1}区间: {z['range'][0]:.8f}-{z['range'][1]:.8f} "
                    f"(强度:{z['strength']:.2f} - {strength_level}, "
                    f"收盘触及:{price_zones[z['price']]['close_touches']}次)"
                )
            
            info = "\n".join(resistance_info) + "\n" + "\n".join(support_info)
            return True, info  # 只有成功分析出区间时才返回True
            
        except Exception as e:
            logger.error(f"支撑阻力分析出错: {str(e)}")
            return False, f"支撑阻力分析出错: {str(e)}"  # 出错时返回False
        
    def analyze_adam_theory(self, data: pd.DataFrame) -> Tuple[bool, str]:
        """亚当理论分析 - 仅分析做多机会"""
        try:
            if len(data) < 80:
                return False, "数据量不足"
            
            # 1. 识别突破点
            recent_data = data.tail(80)
            
            # 使用更精确的横盘区间识别方法
            highs = recent_data['high'].rolling(window=20).max()
            lows = recent_data['low'].rolling(window=20).min()
            consolidation_high = highs.iloc[-2]  # 使用前一个周期的高点
            consolidation_low = lows.iloc[-2]    # 使用前一个周期的低点
            current_close = recent_data['close'].iloc[-1]
            
            # 计算横盘区间的波动范围
            range_size = consolidation_high - consolidation_low
            
            # 2. 计算对称性特征
            # 使用更多的历史数据来识别波动特征
            pre_breakout_data = data.iloc[-30:-1]  # 使用最近30根K线，不包含当前K线
            swing_high = pre_breakout_data['high'].max()
            swing_low = pre_breakout_data['low'].min()
            
            # 计算波动特征
            pre_breakout_range = swing_high - swing_low
            percentage_range = (pre_breakout_range / swing_low) * 100  # 计算百分比波动
            
            # 3. 预测目标位
            target_price = current_close + pre_breakout_range
            target_percentage = ((target_price - current_close) / current_close) * 100
            
            # 4. 计算其他确认指标
            # 自动检测并使用正确的成交量字段
            if 'tick_volume' in recent_data.columns:
                volume_data = recent_data['tick_volume']  # MT5数据
            elif 'volume' in recent_data.columns:
                volume_data = recent_data['volume']      # Binance数据
            else:
                raise ValueError("找不到成交量数据")
            
            volume_ma = volume_data.rolling(20).mean()
            volume_std = volume_data.rolling(20).std()
            current_volume = volume_data.iloc[-1]
            volume_zscore = (current_volume - volume_ma.iloc[-1]) / volume_std.iloc[-1]
            volume_breakout = volume_zscore > 1.0  # 使用标准差来判断放量
            
            # 计算横盘时间和特征
            consolidation_bars = 0
            price_range = []
            for i in range(2, len(recent_data)):
                price = recent_data['close'].iloc[-i]
                if consolidation_low * 0.95 <= price <= consolidation_high * 1.05:
                    consolidation_bars += 1
                    price_range.append(price)
                else:
                    break
                
            # 计算横盘的稳定性
            if price_range:
                price_std = np.std(price_range)
                price_stability = price_std / np.mean(price_range)
            else:
                price_stability = 1.0
            
            # 5. 生成分析结果
            # 优化判断条件
            is_valid = (
                current_close > consolidation_low and  # 价格在低点之上
                consolidation_bars >= 3 and           # 至少3根K线的横盘
                pre_breakout_range > 0 and           # 有效的波动范围
                price_stability < 0.1                 # 横盘足够稳定
            )
            
            # 增加更详细的分析信息
            info = (
                f"当前价格: {current_close:.8f}\n"
                f"目标位: {target_price:.8f} (上涨空间: {target_percentage:.8f}%)\n"
                f"横盘区间: {consolidation_low:.8f} - {consolidation_high:.8f} "
                f"(波动: {(range_size/consolidation_low*100):.8f}%)\n"
                f"横盘时间: {consolidation_bars}根K线 "
                f"(稳定性: {price_stability:.8f})\n"
                f"历史波动范围: {pre_breakout_range:.8f} "
                f"({percentage_range:.8f}%)\n"
                f"量能分析: {'强势放量' if volume_zscore > 2 else '温和放量' if volume_zscore > 1 else '无明显放量'} "
                f"(Z-Score: {volume_zscore:.8f})"
            )
            
            return is_valid, info
            
        except Exception as e:
            logger.error(f"亚当理论分析出错: {str(e)}")
            return False, f"分析出错: {str(e)}"

    @timeit
    def analyze_all_patterns(self, data: pd.DataFrame) -> Dict[str, Tuple[bool, str]]:
        """分析所有技术形态"""
        try:
            results = {}
            results["趋势线突破"] = self.analyze_trend_line_breakout(data)
            results["底部箱体突破"] = self.analyze_support_box_breakout(data)
            results["放量"] = self.analyze_volume_surge(data)
            results["收敛三角形"] = self.analyze_converging_triangle(data)
            results["头肩底"] = self.analyze_head_shoulders(data)
            results["K线组合"] = self.analyze_k_line_pattern(data)
            results["回调买点"] = self.analyze_pullback_entry(data)
            results["老鸭头"] = self.analyze_duck_head(data)
            results["茶杯带柄"] = self.analyze_cup_handle(data)
            results["破底翻"] = self.analyze_break_bottom_reversal(data)
            return results
        except Exception as e:
            logger.error(f"形态分析出错: {str(e)}")
            return {}

    @timeit
    def detect_trading_signals(self, data: pd.DataFrame) -> List[Dict]:
        """
        检测交易信号 - 双突破系统
        
        根据各形态分析结果,按照以下逻辑组合信号:
        1. 完美形态：趋势突破 + 箱体突破 + (三角形或头肩底) + K线组合 + 放量
        2. 趋势突破组合：趋势突破 + 任意一个确认信号
        3. 形态突破确认：(箱体/三角形/头肩底) + (K线/放量)确认
        4. 回调买点：回调 + (K线/放量)确认
        5. 老鸭头：老鸭头 + (K线/放量)确认
        6. 茶杯带柄：茶杯带柄 + (K线/放量)确认
        """
        signals = []
        pattern_results = self.analyze_all_patterns(data)
        
        # 提取各形态的结果
        trend_break = pattern_results["趋势线突破"][0]
        box_break = pattern_results["底部箱体突破"][0]
        volume = pattern_results["放量"][0]
        triangle = pattern_results["收敛三角形"][0]
        head_shoulders = pattern_results["头肩底"][0]
        k_line = pattern_results["K线组合"][0]
        pullback = pattern_results["回调买点"][0]
        duck_head = pattern_results["老鸭头"][0]
        cup = pattern_results["茶杯带柄"][0]
        break_bottom = pattern_results["破底翻"][0]

        # 分析支撑阻力和亚当理论
        _, sr_info = self.analyze_support_resistance(data)
        _, adam_info = self.analyze_adam_theory(data)

        # 组合额外分析信息
        extra_analysis = (
            "\n=== 支撑阻力分析 ===\n"
            f"{sr_info}\n"
            "\n=== 亚当理论分析 ===\n"
            f"{adam_info}"
        )

        # 1. 完美形态：趋势突破 + 箱体突破 + (三角形或头肩底) + K线组合 + 放量
        if all([trend_break, (box_break or break_bottom), (triangle or head_shoulders), k_line, volume]):
            signals.append({
                'type': 'A',  # 完美形态
                'strength': 'HIGH',
                'patterns': pattern_results,
                'details': "完美形态：趋势突破 + 箱体突破 + 形态确认 + K线组合 + 放量",
                'extra_analysis': extra_analysis
            })
            
        # 2. 趋势突破组合：趋势突破 + 任意一个确认信号
        elif trend_break and any([box_break, triangle, head_shoulders, k_line, volume, break_bottom]):
            signals.append({
                'type': 'B',  # 趋势突破
                'strength': 'MEDIUM',
                'patterns': pattern_results,
                'details': "趋势突破组合：趋势突破 + 确认信号",
                'extra_analysis': extra_analysis
            })
            
        # 3. 形态突破确认：(箱体/三角形/头肩底) + (K线/放量)确认
        elif (box_break or triangle or head_shoulders or break_bottom) and (k_line or volume):
            signals.append({
                'type': 'C',  # 形态突破确认
                'strength': 'MEDIUM',
                'patterns': pattern_results,
                'details': "形态突破确认：形态 + 确认信号",
                'extra_analysis': extra_analysis
            })
            
        # 4. 其他交易机会：回调/老鸭头/茶杯带柄
        elif any([pullback, duck_head, cup]) and (k_line or volume):
            signals.append({
                'type': 'D',  # 其他交易机会
                'strength': 'MEDIUM',
                'patterns': pattern_results,
                'details': "其他交易机会：回调/老鸭头/茶杯带柄 + 确认信号",
                'extra_analysis': extra_analysis
            })

        # 为每个信号添加详细的形态信息
        for signal in signals:
            satisfied_patterns = []
            for pattern_name, (is_valid, info) in pattern_results.items():
                if is_valid:
                    satisfied_patterns.append(f"{pattern_name}: {info}")
            signal['satisfied_patterns'] = satisfied_patterns
            
        return signals