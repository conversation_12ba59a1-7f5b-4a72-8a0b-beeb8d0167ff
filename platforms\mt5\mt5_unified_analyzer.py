import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

import MetaTrader5 as mt5
from typing import Dict, List
import pandas as pd
import logging
import time
import traceback
from datetime import datetime
from core.unified_analyzer import UnifiedPatternAnalyzer
from core.notifier import NotificationService
from platforms.mt5.mt5_data import MT5DataFetcher
from platforms.mt5.mt5_config import SYMBOLS
from utils.log_utils import setup_logger
from platforms.mt5.mt5_config import SYMBOLS, MT5_PATH

# 设置日志
logger = setup_logger(
    'mt5_analyzer', 
    os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), 'logs', 'mt5', 'analyzer.log'),
    level=logging.INFO
)

def handle_notification_timeout(func):
    """处理通知超时的装饰器"""
    def wrapper(*args, **kwargs):
        max_wait = 10  # 最大等待时间
        start_time = time.time()
        
        while True:
            try:
                return func(*args, **kwargs)
            except Exception as e:
                if "timed out" in str(e).lower():
                    elapsed = time.time() - start_time
                    if elapsed > max_wait:
                        logger.error(f"通知发送超时，超过最大等待时间 {max_wait} 秒")
                        return False
                    logger.info(f"通知发送超时，继续重试...")
                    time.sleep(5)  # 短暂等待后重试
                else:
                    raise  # 重新抛出非超时异常
    return wrapper


class MT5UnifiedMarketAnalyzer:
    """MT5统一市场分析器（支持多空分析）"""
    
    def __init__(self, custom_symbols: List[str] = None, direction: str = "both"):
        """
        初始化MT5统一市场分析器
        
        参数:
            custom_symbols: 自定义分析品种列表，如果为None则使用默认品种
            direction: 分析方向，可选值为 "long"、"short" 或 "both"
        """
        self.data_fetcher = MT5DataFetcher(analysis_mode=True)
        self.pattern_analyzer = UnifiedPatternAnalyzer(direction=direction)
        self.notifier = NotificationService()
        self.direction = direction.lower()
        # 使用自定义品种或默认品种
        self.analysis_symbols = custom_symbols if custom_symbols is not None else SYMBOLS["analysis"]
        # 添加品种特定的方向设置
        self.symbol_directions = {symbol: self.direction for symbol in self.analysis_symbols}
    
    def set_symbol_direction(self, symbol: str, direction: str):
        """设置特定品种的分析方向"""
        if direction.lower() not in ["long", "short", "both"]:
            logger.warning(f"无效的分析方向: {direction}，使用默认方向: {self.direction}")
            direction = self.direction
        
        self.symbol_directions[symbol] = direction.lower()
        logger.debug(f"设置 {symbol} 的分析方向为: {direction}")

    def get_symbol_direction(self, symbol: str) -> str:
        """获取特定品种的分析方向"""
        return self.symbol_directions.get(symbol, self.direction)

    @handle_notification_timeout
    def send_notification_with_timeout(self, **kwargs):
        """带超时处理的通知发送"""
        return self.notifier.send_notification(**kwargs)

    def analyze_market(self, **kwargs) -> List[Dict]:
        """市场分析主函数"""
        all_signals = []
        try:
            if not mt5.initialize():
                logger.error("MT5初始化失败")
                return all_signals

            notify = kwargs.get('notify', True)
            direction_display = "双向" if self.direction == "both" else ("多头" if self.direction == "long" else "空头")
            logger.info(f"开始市场分析（方向：{direction_display}）...")
            
            # 遍历分析品种
            for symbol in self.analysis_symbols:
                try:
                    if symbol not in self.data_fetcher.symbols:
                        logger.debug(f"跳过未配置的品种: {symbol}")
                        continue
                        
                    if not self.data_fetcher.is_trading_time(symbol):
                        logger.info(f"{symbol} 不在交易时间")
                        continue
                    
                    # 获取当前交易方向
                    current_direction = self.get_symbol_direction(symbol)
                    direction_text = "双向" if current_direction == "both" else ("多头" if current_direction == "long" else "空头")
                    logger.info(f"开始分析品种: {symbol} (方向: {direction_text})")
                    
                    # 遍历所有时间周期
                    for timeframe in self.data_fetcher.timeframes:
                        try:
                            logger.info(f"分析 {symbol} {timeframe} 周期...")
                            data = self.data_fetcher.fetch_data(symbol, timeframe)
                            if data is None:
                                logger.warning(f"无法获取 {symbol} {timeframe} 周期数据")
                                continue
                                
                            # 记录数据基本信息
                            logger.info(f"{symbol} {timeframe} 获取到 {len(data)} 条K线数据")
                            
                            # 使用统一分析器分析
                            logger.info(f"开始对 {symbol} {timeframe} 进行模式分析...")
                            signals = self.pattern_analyzer.detect_trading_signals(data)
                            
                            # 只有在有信号时才处理和发送通知
                            if signals:
                                logger.info(f"{symbol} {timeframe} 发现 {len(signals)} 个信号")
                                for signal in signals:
                                    try:
                                        # 添加额外信息
                                        signal.update({
                                            'symbol': symbol,
                                            'timeframe': timeframe,
                                            'timestamp': pd.Timestamp.now()
                                        })
                                        all_signals.append(signal)
                                        
                                        # 记录详细信号信息
                                        logger.info(f"信号详情: {symbol} {timeframe} {signal['direction']} 类型:{signal['type']} 强度:{signal['strength']}")
                                        logger.info(f"满足条件: {signal['satisfied_patterns']}")
                                        if 'details' in signal:
                                            logger.info(f"详细分析: {signal['details']}")
                                        
                                        # 只有在有效信号时才发送通知
                                        if notify:
                                            # 修改这里，根据NotificationService的实际接口调整参数
                                            try:
                                                self.send_notification_with_timeout(
                                                    symbol=symbol,
                                                    timeframe=timeframe,
                                                    signal_type=signal['type'],
                                                    signal_strength=signal['strength'],
                                                    conditions=signal['satisfied_patterns'],
                                                    extra_info={
                                                        'platform': 'MT5',
                                                        'details': signal['details'],
                                                        'direction': signal['direction'],
                                                        'extra_analysis': signal.get('extra_analysis', '')
                                                    }
                                                )
                                            except Exception as e:
                                                logger.error(f"发送通知失败: {str(e)}")
                                    except Exception as e:
                                        logger.error(f"处理信号时出错: {str(e)}")
                                        continue
                            else:
                                logger.info(f"{symbol} {timeframe} 未发现交易信号")
                                        
                        except Exception as e:
                            logger.error(f"分析 {symbol} {timeframe} 时出错: {str(e)}")
                            continue
                except Exception as e:
                    logger.error(f"处理品种 {symbol} 时出错: {str(e)}")
                    continue
            
            if all_signals:
                logger.info(f"分析完成，发现 {len(all_signals)} 个信号:")
                for signal in all_signals:
                    logger.info(
                        f"{signal['symbol']} {signal['timeframe']}分钟 "
                        f"{signal['direction']} 类型:{signal['type']} "
                        f"强度:{signal['strength']}"
                    )
            else:
                logger.info("分析完成，未发现交易信号")
                
            return all_signals
            
        except Exception as e:
            logger.error(f"市场分析过程出错: {str(e)}")
            return all_signals

def run_analysis(direction: str = "both", notify: bool = True) -> None:
    """主运行函数"""
    max_retries = 3
    retry_delay = 1
    
    for attempt in range(max_retries):
        try:
            logger.info(f"=== 开始新一轮市场分析（方向：{direction}）===")
            
            # 创建分析器实例并运行
            analyzer = MT5UnifiedMarketAnalyzer(direction=direction)
            signals = analyzer.analyze_market(notify=notify)
            
            # 只在有信号时输出结果
            if signals:
                # 按方向和品种分类统计
                long_signals = [s for s in signals if s['direction'] == 'LONG']
                short_signals = [s for s in signals if s['direction'] == 'SHORT']
                
                # 按品种分组
                symbols_long = {}
                symbols_short = {}
                
                for signal in long_signals:
                    symbol = signal['symbol']
                    if symbol not in symbols_long:
                        symbols_long[symbol] = []
                    symbols_long[symbol].append(signal)
                
                for signal in short_signals:
                    symbol = signal['symbol']
                    if symbol not in symbols_short:
                        symbols_short[symbol] = []
                    symbols_short[symbol].append(signal)
                
                # 输出统计信息
                logger.info("\n=== 分析结果汇总 ===")
                logger.info(f"总信号数: {len(signals)} | 多头: {len(long_signals)} | 空头: {len(short_signals)}")
                
                if long_signals:
                    logger.info(f"\n做多信号 ({len(long_signals)}):")
                    for symbol, symbol_signals in symbols_long.items():
                        logger.info(f"  {symbol} ({len(symbol_signals)}个):")
                        for signal in symbol_signals:
                            logger.info(f"    {signal['timeframe']}分钟 类型:{signal['type']} 强度:{signal['strength']}")
                
                if short_signals:
                    logger.info(f"\n做空信号 ({len(short_signals)}):")
                    for symbol, symbol_signals in symbols_short.items():
                        logger.info(f"  {symbol} ({len(symbol_signals)}个):")
                        for signal in symbol_signals:
                            logger.info(f"    {signal['timeframe']}分钟 类型:{signal['type']} 强度:{signal['strength']}")
            else:
                logger.info("=== 分析结果汇总: 未发现交易信号 ===")
            
            logger.info("=== 本轮分析完成 ===\n")
            return
            
        except Exception as e:
            if attempt < max_retries - 1:
                logger.error(f"运行出错 (尝试 {attempt + 1}/{max_retries}): {str(e)}")
                logger.info(f"等待 {retry_delay} 秒后重试...")
                time.sleep(retry_delay)
            else:
                logger.error(f"运行在 {max_retries} 次尝试后仍然失败")
                logger.error(f"详细错误信息: {traceback.format_exc()}")
                    
if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='MT5统一市场分析器')
    parser.add_argument('--direction', type=str, default='both', choices=['long', 'short', 'both'],
                        help='分析方向：long（做多）、short（做空）或both（两者）')
    parser.add_argument('--notify', action='store_true', default=True,
                        help='是否发送通知')
    
    args = parser.parse_args()
    run_analysis(direction=args.direction, notify=args.notify)