# 价格行为交易EA

## 策略说明

这是一个基于价格行为的自动交易EA，主要通过识别大阳线/大阴线并结合EMA20均线过滤来生成交易信号。

### 核心交易逻辑

1. **大阳/阴线识别**：
   - 通过K线实体占整体K线的比例来识别大阳线/大阴线
   - 默认设置为实体占比≥50%的K线被视为大阳/阴线

2. **EMA20均线过滤**：
   - 多单：要求大阳线收盘价在EMA20上方
   - 空单：要求大阴线收盘价在EMA20下方
   - 可通过参数关闭此过滤条件

3. **交易方向控制**：
   - 可设置只做多单、只做空单或双向交易

4. **订单类型**：
   - 挂单模式：使用Buy Stop/Sell Stop在前一根K线高点/低点上方/下方挂单
   - 市价模式：直接以市价开仓

5. **止损设置**：
   - 多单：前一根K线最低点下方
   - 空单：前一根K线最高点上方
   - 自动调整以符合经纪商最小止损距离要求

6. **止盈设置**：
   - 基于止损距离和设定的盈亏比自动计算
   - 默认盈亏比为1:2

7. **挂单管理**：
   - 挂单自动取消功能：超过设定时间（默认60秒）的挂单会被自动取消
   - 一次只允许一个挂单或持仓

8. **仓位管理**：
   - 固定手数模式：使用设定的固定手数交易
   - 自动手数模式：根据账户结余自动计算交易手数

## 参数说明

| 参数名 | 默认值 | 说明 |
|--------|--------|------|
| LotSize | 0.1 | 交易手数 |
| AutoLotSize | false | 是否自动调整交易手数 |
| LotSizePerK | 0.01 | 每1000结余对应的手数(自动模式) |
| RiskRewardRatio | 2 | 盈亏比(1:N) |
| UseEMA20Filter | true | 是否使用EMA20过滤 |
| OnlyLong | true | 是否只做多单 |
| OnlyShort | false | 是否只做空单 |
| BigCandlePercent | 0.5 | 大阳/阴线定义(实体占比) |
| UsePendingOrder | true | 是否使用挂单 |
| PendingOrderExpiry | 60 | 挂单自动取消时间(秒) |

## 使用建议

1. **时间框架**：
   - 建议在M5-H1时间框架上使用
   - 较大时间框架可能需要调整止损距离和盈亏比

2. **交易品种**：
   - 适用于波动较大的品种，如指数、贵金属等
   - 对于波动较小的品种，可能需要调小BigCandlePercent参数

3. **参数优化**：
   - BigCandlePercent：根据交易品种的波动特性调整，波动小的品种可设置为0.3-0.4
   - RiskRewardRatio：根据个人风险偏好调整，保守型可设置为1.5，激进型可设置为2.5或更高
   - PendingOrderExpiry：根据交易时段和品种特性调整，活跃时段可设置较短时间

4. **风险控制**：
   - 建议结合资金管理规则使用
   - 单笔交易风险不超过账户的1-2%

## 常见问题

1. **挂单失败**：
   - 检查止损/止盈点位是否符合经纪商最小距离要求
   - 检查挂单价格是否合理（多单高于当前价格，空单低于当前价格）

2. **交易信号稀少**：
   - 尝试降低BigCandlePercent参数值
   - 考虑关闭EMA20过滤或同时允许多空双向交易

3. **止损过大**：
   - 考虑在较小的时间框架上使用EA
   - 调整止损计算逻辑，可修改代码中的止损点位计算部分

## 更新日志

- v1.00 (2025-04-10)：初始版本发布