//+------------------------------------------------------------------+
//|                                            two_breakthrough.mq5   |
//|                                  Copyright 2025,  HUA   JIA       |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, HUA JIA"
#property version   "1.00"
#property strict

//定义止损止盈模式枚举
enum ENUM_SL_TP_MODE
{
   SL_TP_POINTS,     // 点数
   SL_TP_PERCENT,    // 基于品种百分比模式
   SL_TP_ATR   // 动态atr模式
};

//+------------------------------------------------------------------+
//| 定义趋势方向枚举                                                 |
//+------------------------------------------------------------------+
enum ENUM_TRENDLINE_DIRECTION
{
   TRENDLINE_UP,       // 上升趋势
   TRENDLINE_DOWN,     // 下降趋势
   TRENDLINE_NEUTRAL   // 无明显趋势
};
// 定义枚举类型
enum ENUM_POSITION_STATUS {
   POSITION_STATUS_NONE,       // 无持仓
   POSITION_STATUS_LONG,       // 多头持仓
   POSITION_STATUS_SHORT       // 空头持仓
};
//| 定义成交量状态枚举                                               |
//+------------------------------------------------------------------+
enum ENUM_VOLUME_STATE
{
   VOLUME_EXPANSION,    // 成交量放大
   VOLUME_NORMAL        // 成交量正常
};
//+------------------------------------------------------------------+
//| 定义三角形方向枚举                                               |
//+------------------------------------------------------------------+
enum ENUM_TRIANGLE_DIRECTION
{
   TRIANGLE_UP,       // 多头信号
   TRIANGLE_DOWN,     // 空头信号
   TRIANGLE_NEUTRAL   // 无明确信号
};

//+------------------------------------------------------------------+
//| 定义三角形类型枚举                                               |
//+------------------------------------------------------------------+
enum ENUM_TRIANGLE_TYPE
{
   TRIANGLE_NONE,       // 无三角形
   TRIANGLE_SYMMETRIC,  // 对称三角形
   TRIANGLE_ASCENDING,  // 上升三角形
   TRIANGLE_DESCENDING  // 下降三角形
};
//+------------------------------------------------------------------+
//| 定义箱体方向枚举                                                 |
//+------------------------------------------------------------------+
enum ENUM_BOX_DIRECTION
{
   BOX_UP,          // 多头信号
   BOX_DOWN,        // 空头信号
   BOX_NEUTRAL      // 无明确信号
};

//+------------------------------------------------------------------+
//| 定义箱体类型枚举                                                 |
//+------------------------------------------------------------------+
enum ENUM_BOX_TYPE
{
   BOX_NONE,       // 无箱体
   BOX_HORIZONTAL, // 水平箱体
   BOX_ASCENDING,  // 上升箱体
   BOX_DESCENDING  // 下降箱体
};
//+------------------------------------------------------------------+
//| 定义K线形态枚举                                                  |
//+------------------------------------------------------------------+
enum ENUM_CANDLE_PATTERN
{
   PATTERN_NONE,              // 无形态
   PATTERN_BULLISH_MARUBOZU,  // 多头光头光脚阳线
   PATTERN_BEARISH_MARUBOZU,  // 空头光头光脚阴线
   PATTERN_HAMMER,            // 锤子线
   PATTERN_HANGING_MAN,       // 上吊线
   PATTERN_INVERTED_HAMMER,   // 倒锤子线
   PATTERN_SHOOTING_STAR,     // 流星线
   PATTERN_BULLISH_ENGULFING, // 多头吞没形态
   PATTERN_BEARISH_ENGULFING, // 空头吞没形态
   PATTERN_MORNING_STAR,      // 晨星形态
   PATTERN_EVENING_STAR,      // 暮星形态
   PATTERN_DOJI,              // 十字星
   PATTERN_HARAMI_BULLISH,    // 多头孕线
   PATTERN_HARAMI_BEARISH     // 空头孕线
};
//+------------------------------------------------------------------+
//| 定义K线形态方向枚举                                              |
//+------------------------------------------------------------------+
enum ENUM_CANDLE_DIRECTION
{
   CANDLE_UP,       // 多头信号
   CANDLE_DOWN,     // 空头信号
   CANDLE_NEUTRAL   // 中性信号
};
//+------------------------------------------------------------------+
//| 定义头肩形态类型枚举                                             |
//+------------------------------------------------------------------+
enum ENUM_HS_PATTERN_TYPE
{
   HS_PATTERN_NONE,       // 无形态
   HS_PATTERN_TOP,        // 头肩顶
   HS_PATTERN_BOTTOM      // 头肩底
};

//+------------------------------------------------------------------+
//| 定义头肩形态方向枚举                                             |
//+------------------------------------------------------------------+
enum ENUM_HS_DIRECTION
{
   HS_UP,       // 多头信号
   HS_DOWN,     // 空头信号
   HS_NEUTRAL   // 中性信号
};

// 引入必要的库
#include <Trade\Trade.mqh>

// 定义常量
#define PRICE_BUFFER_SIZE 400   // 价格数据缓冲区大小



// 全局变量
CTrade g_trade;                // 交易对象
int g_magic_number = 12345;    // 魔术数字，用于标识EA的订单
ulong g_position_ticket = 0;   // 当前持仓票据
ENUM_POSITION_STATUS g_position_status = POSITION_STATUS_NONE; // 当前持仓状态
datetime g_last_trade_time = 0; // 上次交易时间
int g_trades_today = 0;        // 今日交易次数
datetime g_current_day = 0;    // 当前交易日

// 输入参数
// 交易参数
input bool     AllowLong = true;           // 允许做多
input bool     AllowShort = false;         // 允许做空
input int      MaxTradesPerDay = 5;        // 每日最大交易次数
input int      TradeInterval = 3600;       // 交易间隔（秒）

// 风险管理参数
input group "风险管理参数"
input double   FixedLotSize = 0.01;                // 固定手数
input ENUM_SL_TP_MODE StopLossTakeProfitMode = SL_TP_POINTS;  // 止损止盈模式
input double   StopLossValue = 200;                // 止损值(点数/百分比/ATR倍数)
input double   TakeProfitValue = 600;              // 止盈值(点数/百分比/ATR倍数)
input int      ATR_Period = 14;                    // ATR周期(ATR模式)

// 添加全局变量
int g_atr_handle = INVALID_HANDLE;
double g_atr_buffer[];

// 调试参数
input bool     UseDebugMode = true;        // 使用调试模式
input int      DebugLevel = 3;             // 调试级别（1-3）


//+------------------------------------------------------------------+
//| Expert initialization function                                    |
//+------------------------------------------------------------------+
int OnInit()
{
   // 设置交易参数
   g_trade.SetExpertMagicNumber(g_magic_number);
   g_trade.SetDeviationInPoints(10);
   g_trade.SetTypeFilling(ORDER_FILLING_FOK);
   
   // 初始化ATR指标(如果使用ATR模式)
   if(StopLossTakeProfitMode == SL_TP_ATR)
   {
      g_atr_handle = iATR(_Symbol, _Period, ATR_Period);
      if(g_atr_handle == INVALID_HANDLE)
      {
         Print("创建ATR指标失败，错误码: ", GetLastError());
         return(INIT_FAILED);
      }
   }
   
   // 检查当前持仓
   CheckExistingPositions();
   
   // 重置交易计数
   ResetDailyTradeCount();
   
   if(UseDebugMode)
   {
      Print("EA初始化完成 - 版本1.00");
      Print("交易品种: ", _Symbol, ", 周期: ", EnumToString((ENUM_TIMEFRAMES)_Period));
      Print("允许做多: ", AllowLong ? "是" : "否", ", 允许做空: ", AllowShort ? "是" : "否");
      Print("每日最大交易次数: ", MaxTradesPerDay, ", 交易间隔: ", TradeInterval, "秒");
      Print("止损止盈模式: ", EnumToString(StopLossTakeProfitMode));
   }
   
   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   // 释放指标句柄
   if(g_atr_handle != INVALID_HANDLE)
      IndicatorRelease(g_atr_handle);
      
   // 输出EA卸载信息
   if(UseDebugMode)
      Print("EA已卸载，原因代码: ", reason);
}

//+------------------------------------------------------------------+
//| 获取当前ATR值                                                    |
//+------------------------------------------------------------------+
double GetCurrentATR()
{
   if(g_atr_handle == INVALID_HANDLE)
      return 0;
      
   ArraySetAsSeries(g_atr_buffer, true);
   if(CopyBuffer(g_atr_handle, 0, 0, 3, g_atr_buffer) <= 0)
   {
      Print("复制ATR数据失败，错误码: ", GetLastError());
      return 0;
   }
   
   return g_atr_buffer[0];
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
   // 检查是否有持仓
   if(HasOpenPosition())
   {
      // 持仓管理（止盈止损已在下单时设置，这里不需要额外处理）
      if(UseDebugMode && DebugLevel > 2)
         Print("当前持有持仓，票据号: ", g_position_ticket);
   }
   else
   {
      // 检查交易时间间隔
      if(TimeCurrent() - g_last_trade_time < TradeInterval)
      {
         if(UseDebugMode && DebugLevel > 2)
            Print("交易间隔未到，剩余时间: ", TradeInterval - (TimeCurrent() - g_last_trade_time), "秒");
         return;
      }
      
      // 检查每日交易次数
      CheckNewTradingDay();
      if(g_trades_today >= MaxTradesPerDay)
      {
         if(UseDebugMode && DebugLevel > 1)
            Print("已达到每日最大交易次数: ", MaxTradesPerDay);
         return;
      }
      
      // 检查入场条件
      CheckEntrySignals();
   }
}

//+------------------------------------------------------------------+
//| 检查新的交易日                                                   |
//+------------------------------------------------------------------+
void CheckNewTradingDay()
{
   datetime current_time = TimeCurrent();
   MqlDateTime time_struct;
   TimeToStruct(current_time, time_struct);
   
   datetime current_day = StringToTime(StringFormat("%04d.%02d.%02d", 
                          time_struct.year, time_struct.mon, time_struct.day));
   
   if(current_day != g_current_day)
   {
      g_trades_today = 0;
      g_current_day = current_day;
      if(UseDebugMode)
         Print("新的交易日，重置交易计数");
   }
}

//+------------------------------------------------------------------+
//| 重置每日交易计数                                                 |
//+------------------------------------------------------------------+
void ResetDailyTradeCount()
{
   datetime current_time = TimeCurrent();
   MqlDateTime time_struct;
   TimeToStruct(current_time, time_struct);
   
   g_current_day = StringToTime(StringFormat("%04d.%02d.%02d", 
                   time_struct.year, time_struct.mon, time_struct.day));
   g_trades_today = 0;
}

//+------------------------------------------------------------------+
//| 检查现有持仓                                                     |
//+------------------------------------------------------------------+
void CheckExistingPositions()
{
   g_position_ticket = 0;
   g_position_status = POSITION_STATUS_NONE;
   
   for(int i = 0; i < PositionsTotal(); i++)
   {
      ulong ticket = PositionGetTicket(i);
      if(ticket > 0)
      {
         if(PositionGetInteger(POSITION_MAGIC) == g_magic_number && 
            PositionGetString(POSITION_SYMBOL) == _Symbol)
         {
            g_position_ticket = ticket;
            
            if(PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY)
               g_position_status = POSITION_STATUS_LONG;
            else if(PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_SELL)
               g_position_status = POSITION_STATUS_SHORT;
            
            if(UseDebugMode)
            {
               Print("找到现有持仓: 票据=", g_position_ticket, 
                     ", 类型=", EnumToString((ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE)));
            }
            
            break;
         }
      }
   }
}

//+------------------------------------------------------------------+
//| 检查是否有持仓                                                   |
//+------------------------------------------------------------------+
bool HasOpenPosition()
{
   if(g_position_ticket > 0)
   {
      // 验证持仓是否仍然存在
      if(!PositionSelectByTicket(g_position_ticket))
      {
         g_position_ticket = 0;
         g_position_status = POSITION_STATUS_NONE;
         return false;
      }
      return true;
   }
   return false;
}

//+------------------------------------------------------------------+
//| 检查多头信号                                                     |
//+------------------------------------------------------------------+

// 检查多头信号
void CheckLongSignal(ENUM_TRENDLINE_DIRECTION trend_signal, double close_price)
{
   // 只有在趋势线显示多头信号时才继续
   if(trend_signal != TRENDLINE_UP)
   {
      if(UseDebugMode && DebugLevel > 1)
         Print("趋势线未显示多头信号，不执行买入");
      return;
   }
   
   // 确认信号 - 使用以下组合之一:
   // 1. K线形态 + 成交量
   // 2. 箱体
   // 3. 三角形
   // 4. 头肩形态
   bool confirmation_signal = false;
   string signal_source = "";
   
   // 1. 检查K线形态 + 成交量组合
   CCandlePatternAnalyzer candle_analyzer;
   candle_analyzer.SetParameters(CandlePeriod, CandleBodyRatio, CandleDojiRatio, CandleShadowRatio, CandleEngulfingRatio);
   
   if(candle_analyzer.Update())
   {
      ENUM_CANDLE_PATTERN pattern = candle_analyzer.DetectPattern(0);
      ENUM_CANDLE_DIRECTION candle_signal = candle_analyzer.GetPatternDirection(pattern);
      
      // 成交量分析
      CVolumeAnalyzer volume_analyzer;
      volume_analyzer.SetParameters(VolumePeriod, VolumeThreshold);
      volume_analyzer.Analyze();
      ENUM_VOLUME_STATE volume_signal = volume_analyzer.GetVolumeState();
      
      if(candle_signal == CANDLE_UP && volume_signal == VOLUME_EXPANSION)
      {
         confirmation_signal = true;
         signal_source = "K线形态+成交量放大";
      }
   }
   
   // 2. 检查箱体信号
   if(!confirmation_signal)
   {
      CBoxAnalyzer box_analyzer;
      box_analyzer.SetParameters(BoxPeriod, BoxBreakoutThreshold);
      
      if(box_analyzer.Analyze())
      {
         ENUM_BOX_DIRECTION box_signal = box_analyzer.GetSignalDirection(close_price);
         if(box_signal == BOX_UP)
         {
            confirmation_signal = true;
            signal_source = "箱体突破";
         }
      }
   }
   
   // 3. 检查三角形信号
   if(!confirmation_signal)
   {
      CTriangleAnalyzer triangle_analyzer;
      triangle_analyzer.SetParameters(TrianglePeriod);
      
      if(triangle_analyzer.Analyze())
      {
         ENUM_TRIANGLE_DIRECTION triangle_signal = triangle_analyzer.GetSignalDirection(close_price);
         if(triangle_signal == TRIANGLE_UP)
         {
            confirmation_signal = true;
            signal_source = "三角形突破";
         }
      }
   }
   
   // 4. 检查头肩形态信号
   if(!confirmation_signal)
   {
      CHeadAndShouldersAnalyzer hs_analyzer;
      hs_analyzer.SetParameters(HSPeriod, HSBreakoutThreshold);
      
      if(hs_analyzer.Analyze())
      {
         ENUM_HS_DIRECTION hs_signal = hs_analyzer.GetSignalDirection(close_price);
         if(hs_signal == HS_UP)
         {
            confirmation_signal = true;
            signal_source = "头肩底形态";
         }
      }
   }
   
   // 如果有确认信号，执行买入
   if(confirmation_signal)
   {
      ExecuteBuy(signal_source);
   }
   else if(UseDebugMode && DebugLevel > 1)
   {
      Print("未获得多头确认信号，不执行买入");
   }
}

//+------------------------------------------------------------------+
//| 检查空头信号                                                     |
//+------------------------------------------------------------------+

// 检查空头信号
void CheckShortSignal(ENUM_TRENDLINE_DIRECTION trend_signal, double close_price)
{
   // 只有在趋势线显示空头信号时才继续
   if(trend_signal != TRENDLINE_DOWN)
   {
      if(UseDebugMode && DebugLevel > 1)
         Print("趋势线未显示空头信号，不执行卖出");
      return;
   }
   
   // 确认信号 - 使用以下组合之一:
   // 1. K线形态 + 成交量
   // 2. 箱体
   // 3. 三角形
   // 4. 头肩形态
   bool confirmation_signal = false;
   string signal_source = "";
   
   // 1. 检查K线形态 + 成交量组合
   CCandlePatternAnalyzer candle_analyzer;
   candle_analyzer.SetParameters(CandlePeriod, CandleBodyRatio, CandleDojiRatio, CandleShadowRatio, CandleEngulfingRatio);
   
   if(candle_analyzer.Update())
   {
      ENUM_CANDLE_PATTERN pattern = candle_analyzer.DetectPattern(0);
      ENUM_CANDLE_DIRECTION candle_signal = candle_analyzer.GetPatternDirection(pattern);
      
      // 成交量分析
      CVolumeAnalyzer volume_analyzer;
      volume_analyzer.SetParameters(VolumePeriod, VolumeThreshold);
      volume_analyzer.Analyze();
      ENUM_VOLUME_STATE volume_signal = volume_analyzer.GetVolumeState();
      
      if(candle_signal == CANDLE_DOWN && volume_signal == VOLUME_EXPANSION)
      {
         confirmation_signal = true;
         signal_source = "K线形态+成交量放大";
      }
   }
   
   // 2. 检查箱体信号
   if(!confirmation_signal)
   {
      CBoxAnalyzer box_analyzer;
      box_analyzer.SetParameters(BoxPeriod, BoxBreakoutThreshold);
      
      if(box_analyzer.Analyze())
      {
         ENUM_BOX_DIRECTION box_signal = box_analyzer.GetSignalDirection(close_price);
         if(box_signal == BOX_DOWN)
         {
            confirmation_signal = true;
            signal_source = "箱体突破";
         }
      }
   }
   
   // 3. 检查三角形信号
   if(!confirmation_signal)
   {
      CTriangleAnalyzer triangle_analyzer;
      triangle_analyzer.SetParameters(TrianglePeriod);
      
      if(triangle_analyzer.Analyze())
      {
         ENUM_TRIANGLE_DIRECTION triangle_signal = triangle_analyzer.GetSignalDirection(close_price);
         if(triangle_signal == TRIANGLE_DOWN)
         {
            confirmation_signal = true;
            signal_source = "三角形突破";
         }
      }
   }
   
   // 4. 检查头肩形态信号
   if(!confirmation_signal)
   {
      CHeadAndShouldersAnalyzer hs_analyzer;
      hs_analyzer.SetParameters(HSPeriod, HSBreakoutThreshold);
      
      if(hs_analyzer.Analyze())
      {
         ENUM_HS_DIRECTION hs_signal = hs_analyzer.GetSignalDirection(close_price);
         if(hs_signal == HS_DOWN)
         {
            confirmation_signal = true;
            signal_source = "头肩顶形态";
         }
      }
   }
   
   // 如果有确认信号，执行卖出
   if(confirmation_signal)
   {
      ExecuteSell(signal_source);
   }
   else if(UseDebugMode && DebugLevel > 1)
   {
      Print("未获得空头确认信号，不执行卖出");
   }
}

//+------------------------------------------------------------------+
//| 检查入场信号                                                     |
//+------------------------------------------------------------------+
void CheckEntrySignals()
{
   // 获取价格数据
   double ask = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
   double bid = SymbolInfoDouble(_Symbol, SYMBOL_BID);
   double point = SymbolInfoDouble(_Symbol, SYMBOL_POINT);
   double close_price = iClose(_Symbol, _Period, 0);
   
   // 分析趋势线信号 (必要条件)
   CTrendLine trend_analyzer;
   bool trend_valid = trend_analyzer.Analyze(UpTrendPriceType, TrendLinePeriod);
   ENUM_TRENDLINE_DIRECTION trend_signal = trend_valid ? trend_analyzer.GetSignalDirection(close_price) : TRENDLINE_NEUTRAL;
   
   // 记录趋势线分析结果
   if(UseDebugMode)
   {
      string trend_str = "";
      switch(trend_signal)
      {
         case TRENDLINE_UP: trend_str = "多头"; break;
         case TRENDLINE_DOWN: trend_str = "空头"; break;
         default: trend_str = "中性"; break;
      }
      
      Print("【趋势线分析】信号: ", trend_str, 
            ", 斜率: ", DoubleToString(trend_analyzer.GetSlope(), 6),
            ", 质量: ", DoubleToString(trend_analyzer.GetQuality(), 2),
            ", R²: ", DoubleToString(trend_analyzer.GetRSquared(), 2));
   }
   
   // 检查多头信号
   if(AllowLong && trend_signal == TRENDLINE_UP)
   {
      CheckLongSignal(trend_signal, close_price);
   }
   // 检查空头信号
   else if(AllowShort && trend_signal == TRENDLINE_DOWN)
   {
      CheckShortSignal(trend_signal, close_price);
   }
   else if(UseDebugMode && DebugLevel > 1)
   {
      Print("趋势线未显示明确信号，不执行交易");
   }
}

//+------------------------------------------------------------------+
//| 执行买入操作                                                     |
//+------------------------------------------------------------------+
void ExecuteBuy(string signal_source)
{
   // 获取当前价格
   double ask = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
   double point = SymbolInfoDouble(_Symbol, SYMBOL_POINT);
   
   // 计算止损和止盈
   double stop_loss = 0, take_profit = 0;
   
   // 获取品种的最小止损距离
   double min_stop_level = SymbolInfoInteger(_Symbol, SYMBOL_TRADE_STOPS_LEVEL) * point;
   
   // 根据不同模式计算止损止盈
   switch(StopLossTakeProfitMode)
   {
      case SL_TP_POINTS:
      {
         // 使用固定点数
         stop_loss = NormalizeDouble(ask - StopLossValue * point, _Digits);
         take_profit = NormalizeDouble(ask + TakeProfitValue * point, _Digits);
         break;
      }

      case SL_TP_PERCENT:
      {
         // 使用价格百分比
         stop_loss = NormalizeDouble(ask * (1 - StopLossValue / 100), _Digits);
         take_profit = NormalizeDouble(ask * (1 + TakeProfitValue / 100), _Digits);
         break;
      }

      case SL_TP_ATR:
      {
         // 使用ATR倍数
         double atr_value = GetCurrentATR();
         if(atr_value > 0)
         {
            stop_loss = NormalizeDouble(ask - atr_value * StopLossValue, _Digits);
            take_profit = NormalizeDouble(ask + atr_value * TakeProfitValue, _Digits);
         }
         else
         {
            // 如果ATR无效，使用固定点数作为备选
            stop_loss = NormalizeDouble(ask - 200 * point, _Digits);
            take_profit = NormalizeDouble(ask + 600 * point, _Digits);
            Print("警告: ATR值无效，使用固定点数作为备选");
         }
         break;
      }
   }
   
   // 确保止损止盈不小于最小距离
   if(ask - stop_loss < min_stop_level)
   {
      stop_loss = NormalizeDouble(ask - min_stop_level * 1.1, _Digits);
      if(UseDebugMode && DebugLevel > 1)
         Print("止损距离太小，已调整为最小允许距离");
   }
   
   if(take_profit - ask < min_stop_level)
   {
      take_profit = NormalizeDouble(ask + min_stop_level * 1.1, _Digits);
      if(UseDebugMode && DebugLevel > 1)
         Print("止盈距离太小，已调整为最小允许距离");
   }
   
   if(UseDebugMode && DebugLevel > 1)
   {
      Print("买入止损止盈计算: 当前价=", ask, 
            ", 止损=", stop_loss, " (距离=", NormalizeDouble(ask - stop_loss, _Digits), ")", 
            ", 止盈=", take_profit, " (距离=", NormalizeDouble(take_profit - ask, _Digits), ")");
   }
   
   // 执行买入操作
   if(g_trade.Buy(FixedLotSize, _Symbol, ask, stop_loss, take_profit, signal_source))
   {
      g_position_ticket = g_trade.ResultOrder();
      g_position_status = POSITION_STATUS_LONG;
      g_last_trade_time = TimeCurrent();
      g_trades_today++;
      
      Print("执行买入: 价格=", ask, ", 止损=", stop_loss, ", 止盈=", take_profit, 
            ", 仓位=", FixedLotSize, ", 信号来源=", signal_source);
   }
   else
   {
      uint error_code = g_trade.ResultRetcode();
      string error_desc = g_trade.ResultComment();
      Print("买入失败，错误码: ", error_code, ", 描述: ", error_desc);
      
      // 处理特定错误
      HandleTradeError(error_code);
   }
}

//+------------------------------------------------------------------+
//| 执行卖出操作                                                     |
//+------------------------------------------------------------------+
void ExecuteSell(string signal_source)
{
   // 获取当前价格
   double bid = SymbolInfoDouble(_Symbol, SYMBOL_BID);
   double point = SymbolInfoDouble(_Symbol, SYMBOL_POINT);
   
   // 计算止损和止盈
   double stop_loss = 0, take_profit = 0;
   
   // 获取品种的最小止损距离
   double min_stop_level = SymbolInfoInteger(_Symbol, SYMBOL_TRADE_STOPS_LEVEL) * point;
   
   // 根据不同模式计算止损止盈
   switch(StopLossTakeProfitMode)
   {
      case SL_TP_POINTS:
      {
         // 使用固定点数
         stop_loss = NormalizeDouble(bid + StopLossValue * point, _Digits);
         take_profit = NormalizeDouble(bid - TakeProfitValue * point, _Digits);
         break;
      }

      case SL_TP_PERCENT:
      {
         // 使用价格百分比
         stop_loss = NormalizeDouble(bid * (1 + StopLossValue / 100), _Digits);
         take_profit = NormalizeDouble(bid * (1 - TakeProfitValue / 100), _Digits);
         break;
      }

      case SL_TP_ATR:
      {
         // 使用ATR倍数
         double atr_value = GetCurrentATR();
         if(atr_value > 0)
         {
            stop_loss = NormalizeDouble(bid + atr_value * StopLossValue, _Digits);
            take_profit = NormalizeDouble(bid - atr_value * TakeProfitValue, _Digits);
         }
         else
         {
            // 如果ATR无效，使用固定点数作为备选
            stop_loss = NormalizeDouble(bid + 200 * point, _Digits);
            take_profit = NormalizeDouble(bid - 600 * point, _Digits);
            Print("警告: ATR值无效，使用固定点数作为备选");
         }
         break;
      }
   }
   
   // 确保止损止盈不小于最小距离
   if(stop_loss - bid < min_stop_level)
   {
      stop_loss = NormalizeDouble(bid + min_stop_level * 1.1, _Digits);
      if(UseDebugMode && DebugLevel > 1)
         Print("止损距离太小，已调整为最小允许距离");
   }
   
   if(bid - take_profit < min_stop_level)
   {
      take_profit = NormalizeDouble(bid - min_stop_level * 1.1, _Digits);
      if(UseDebugMode && DebugLevel > 1)
         Print("止盈距离太小，已调整为最小允许距离");
   }
   
   if(UseDebugMode && DebugLevel > 1)
   {
      Print("卖出止损止盈计算: 当前价=", bid, 
            ", 止损=", stop_loss, " (距离=", NormalizeDouble(stop_loss - bid, _Digits), ")", 
            ", 止盈=", take_profit, " (距离=", NormalizeDouble(bid - take_profit, _Digits), ")");
   }
   
   // 执行卖出操作
   if(g_trade.Sell(FixedLotSize, _Symbol, bid, stop_loss, take_profit, signal_source))
   {
      g_position_ticket = g_trade.ResultOrder();
      g_position_status = POSITION_STATUS_SHORT;
      g_last_trade_time = TimeCurrent();
      g_trades_today++;
      
      Print("执行卖出: 价格=", bid, ", 止损=", stop_loss, ", 止盈=", take_profit, 
            ", 仓位=", FixedLotSize, ", 信号来源=", signal_source);
   }
   else
   {
      uint error_code = g_trade.ResultRetcode();
      string error_desc = g_trade.ResultComment();
      Print("卖出失败，错误码: ", error_code, ", 描述: ", error_desc);
      
      // 处理特定错误
      HandleTradeError(error_code);
   }
}

//+------------------------------------------------------------------+
//| 处理交易错误                                                     |
//+------------------------------------------------------------------+
void HandleTradeError(int error_code)
{
   switch(error_code)
   {
      case 10004: // 交易服务器忙
         Print("交易服务器忙，稍后重试");
         break;
      case 10018: // 市场关闭
         Print("市场已关闭，无法交易");
         break;
      case 10019: // 没有足够的资金
         Print("账户资金不足，无法开仓");
         break;
      case 10021: // 无效的止损水平
         Print("止损设置无效，可能太接近当前价格");
         break;
      default:
         Print("发生交易错误，错误码: ", error_code);
         break;
   }
}

// 以下是趋势线部分

//+------------------------------------------------------------------+
//| 趋势线分析类                                                     |
//+------------------------------------------------------------------+
class CTrendLine
{
private:
   double            m_prices[];           // 价格数据
   int               m_key_points[];       // 关键点索引
   double            m_key_prices[];       // 关键点价格
   double            m_slope;              // 趋势线斜率
   double            m_intercept;          // 趋势线截距
   int               m_touch_count;        // 触点数量
   double            m_quality;            // 趋势线质量(0-1)
   ENUM_TRENDLINE_DIRECTION m_direction;   // 趋势方向
   double            m_error_margin;       // 误差容忍度
   double            m_r_squared;          // R方值(决定系数)
   
   // 内部方法
   void FindKeyPoints(ENUM_APPLIED_PRICE price_type);
   void CalculateTrendLine();
   void EvaluateTrendQuality();
   
   // 稳健回归算法 (模拟TheilSen回归)
   void TheilSenRegression();
   
   // 计算中位数
   double Median(double &values[]);
   
public:
   // 构造函数
   CTrendLine() 
   { 
      m_direction = TRENDLINE_NEUTRAL; 
      m_touch_count = 0; 
      m_quality = 0; 
      m_error_margin = 0.001; // 添加误差容忍度
      m_r_squared = 0;        // 初始化R方值
   }
   
   // 分析趋势
   bool Analyze(ENUM_APPLIED_PRICE price_type, int period);
   
   // 获取趋势线值
   double GetTrendValue(int shift);
   
   // 获取趋势方向
   ENUM_TRENDLINE_DIRECTION GetDirection() { return m_direction; }
   
   // 获取触点数量
   int GetTouchCount() { return m_touch_count; }
   
   // 获取趋势线质量
   double GetQuality() { return m_quality; }
   
   // 获取趋势线斜率
   double GetSlope() { return m_slope; }
   
   // 获取R方值
   double GetRSquared() { return m_r_squared; }
   
   // 检查价格是否突破趋势线
   bool IsBreakout(double price);
   
   // 获取交易信号方向
   ENUM_TRENDLINE_DIRECTION GetSignalDirection(double price);
   
   // 设置误差容忍度
   void SetErrorMargin(double margin) { m_error_margin = margin; }
};

//+------------------------------------------------------------------+
//| 寻找关键点                                                       |
//+------------------------------------------------------------------+
void CTrendLine::FindKeyPoints(ENUM_APPLIED_PRICE price_type)
{
   // 清空数组
   ArrayResize(m_key_points, 0);
   ArrayResize(m_key_prices, 0);
   
   int size = ArraySize(m_prices);
   if(size < 10) return;
   
   // 使用固定窗口大小5，与Python一致
   int window_size = 5;
   
   // 寻找局部极值点
   for(int i = window_size; i < size - window_size; i++)
   {
      bool is_key_point = true;
      
      if(price_type == PRICE_HIGH || price_type == PRICE_CLOSE || price_type == PRICE_OPEN)
      {
         // 寻找局部高点 - 使用Python的条件
         for(int j = 1; j <= window_size; j++)
         {
            if(m_prices[i] < m_prices[i-j] || m_prices[i] < m_prices[i+j])
            {
               is_key_point = false;
               break;
            }
         }
      }
      else if(price_type == PRICE_LOW)
      {
         // 寻找局部低点 - 使用Python的条件
         for(int j = 1; j <= window_size; j++)
         {
            if(m_prices[i] > m_prices[i-j] || m_prices[i] > m_prices[i+j])
            {
               is_key_point = false;
               break;
            }
         }
      }
      
      if(is_key_point)
      {
         // 过滤太近的点位
         bool too_close = false;
         if(ArraySize(m_key_points) > 0)
         {
            int last_idx = m_key_points[ArraySize(m_key_points) - 1];
            if(i - last_idx < window_size)
               too_close = true;
         }
         
         if(!too_close)
         {
            int new_size = ArraySize(m_key_points) + 1;
            ArrayResize(m_key_points, new_size);
            ArrayResize(m_key_prices, new_size);
            m_key_points[new_size - 1] = i;
            m_key_prices[new_size - 1] = m_prices[i];
         }
      }
   }
}

//+------------------------------------------------------------------+
//| 计算中位数                                                       |
//+------------------------------------------------------------------+
double CTrendLine::Median(double &values[])
{
   int size = ArraySize(values);
   if(size == 0) return 0;
   
   // 创建临时数组并排序
   double temp[];
   ArrayResize(temp, size);
   ArrayCopy(temp, values);
   ArraySort(temp);
   
   // 返回中位数
   if(size % 2 == 0)
      return (temp[size/2-1] + temp[size/2]) / 2.0;
   else
      return temp[size/2];
}

//+------------------------------------------------------------------+
//| 稳健回归算法 (模拟TheilSen回归)                                  |
//+------------------------------------------------------------------+
void CTrendLine::TheilSenRegression()
{
   int key_count = ArraySize(m_key_points);
   if(key_count < 2)
   {
      m_slope = 0;
      m_intercept = 0;
      m_direction = TRENDLINE_NEUTRAL;
      return;
   }
   
   // 计算所有可能的斜率
   double slopes[];
   ArrayResize(slopes, 0);
   
   for(int i = 0; i < key_count; i++)
   {
      for(int j = i + 1; j < key_count; j++)
      {
         double x_diff = m_key_points[j] - m_key_points[i];
         
         // 避免除零
         if(x_diff != 0)
         {
            double slope = (m_key_prices[j] - m_key_prices[i]) / x_diff;
            
            // 添加斜率到数组
            int size = ArraySize(slopes);
            ArrayResize(slopes, size + 1);
            slopes[size] = slope;
         }
      }
   }
   
   // 计算斜率的中位数
   m_slope = Median(slopes);
   
   // 计算截距
   double intercepts[];
   ArrayResize(intercepts, key_count);
   
   for(int i = 0; i < key_count; i++)
   {
      intercepts[i] = m_key_prices[i] - m_slope * m_key_points[i];
   }
   
   // 计算截距的中位数
   m_intercept = Median(intercepts);
   
   // 确定趋势方向 - 使用Python的阈值
   if(m_slope > 0.0002)
      m_direction = TRENDLINE_UP;
   else if(m_slope < -0.0002)
      m_direction = TRENDLINE_DOWN;
   else
      m_direction = TRENDLINE_NEUTRAL;
}

//+------------------------------------------------------------------+
//| 计算趋势线                                                       |
//+------------------------------------------------------------------+
void CTrendLine::CalculateTrendLine()
{
   // 使用稳健回归算法替代最小二乘法
   TheilSenRegression();
}

//+------------------------------------------------------------------+
//| 评估趋势线质量                                                   |
//+------------------------------------------------------------------+
void CTrendLine::EvaluateTrendQuality()
{
   int key_count = ArraySize(m_key_points);
   if(key_count < 3)
   {
      m_quality = 0;
      m_touch_count = 0;
      m_r_squared = 0;
      return;
   }
   
   // 计算R方值 (决定系数)
   double ss_total = 0;  // 总平方和
   double ss_residual = 0;  // 残差平方和
   double mean_y = 0;  // y的平均值
   
   // 计算平均值
   for(int i = 0; i < key_count; i++)
   {
      mean_y += m_key_prices[i];
   }
   mean_y /= key_count;
   
   // 计算总平方和和残差平方和
   for(int i = 0; i < key_count; i++)
   {
      int idx = m_key_points[i];
      double predicted = m_slope * idx + m_intercept;
      ss_total += MathPow(m_key_prices[i] - mean_y, 2);
      ss_residual += MathPow(m_key_prices[i] - predicted, 2);
   }
   
   // 计算R方值
   if(ss_total > 0)
   {
      m_r_squared = 1 - (ss_residual / ss_total);
   }
   else
   {
      m_r_squared = 0;
   }
   
   // 计算触点数量
   m_touch_count = 0;
   for(int i = 0; i < ArraySize(m_prices); i++)
   {
      double trend_value = m_slope * i + m_intercept;
      double deviation = MathAbs(m_prices[i] - trend_value);
      double relative_deviation = deviation / m_prices[i];
      
      if(relative_deviation < 0.005)  // 允许0.5%的偏差
      {
         m_touch_count++;
      }
   }
   
   // 综合评分 - 使用Python的条件
   // Python要求斜率大于0且R方值大于-0.5
   bool slope_valid = (m_direction == TRENDLINE_UP && m_slope > 0) || 
                      (m_direction == TRENDLINE_DOWN && m_slope < 0);
   bool r_squared_valid = (m_r_squared > -0.5);
   
   if(slope_valid && r_squared_valid && m_touch_count >= 3)
   {
      m_quality = 0.5 + m_r_squared * 0.5;  // 基础分0.5，R方值贡献0.5
   }
   else
   {
      m_quality = MathMax(0, m_r_squared) * 0.5;  // 只有R方值贡献
   }
}

//+------------------------------------------------------------------+
//| 分析趋势                                                         |
//+------------------------------------------------------------------+
bool CTrendLine::Analyze(ENUM_APPLIED_PRICE price_type, int period)
{
   // 复制价格数据
   ArrayResize(m_prices, period);
   ArraySetAsSeries(m_prices, true);
   
   int copied = 0;
   switch(price_type)
   {
      case PRICE_CLOSE: copied = CopyClose(_Symbol, _Period, 0, period, m_prices); break;
      case PRICE_OPEN: copied = CopyOpen(_Symbol, _Period, 0, period, m_prices); break;
      case PRICE_HIGH: copied = CopyHigh(_Symbol, _Period, 0, period, m_prices); break;
      case PRICE_LOW: copied = CopyLow(_Symbol, _Period, 0, period, m_prices); break;
      default: copied = CopyClose(_Symbol, _Period, 0, period, m_prices);
   }
   
   if(copied <= 0)
   {
      Print("复制价格数据失败，错误码: ", GetLastError());
      return false;
   }
   
   // 寻找关键点
   FindKeyPoints(price_type);
   
   // 计算趋势线
   CalculateTrendLine();
   
   // 评估趋势线质量
   EvaluateTrendQuality();
   
   // 使用Python的要求：触点数量至少3个，质量阈值0.5
   return m_touch_count >= 3 && m_quality > 0.5;
}

//+------------------------------------------------------------------+
//| 获取趋势线值                                                     |
//+------------------------------------------------------------------+
double CTrendLine::GetTrendValue(int shift)
{
   return m_slope * shift + m_intercept;
}

//+------------------------------------------------------------------+
//| 检查价格是否突破趋势线                                           |
//+------------------------------------------------------------------+
bool CTrendLine::IsBreakout(double price)
{
   if(m_direction == TRENDLINE_NEUTRAL || m_quality < 0.5)
      return false;
      
   double trend_value = GetTrendValue(0);
   
   // 使用Python的突破条件
   if(m_direction == TRENDLINE_UP)
   {
      // 上升趋势线，价格在趋势线上方且斜率大于0
      return price > trend_value * (1.0 + m_error_margin) && m_slope > 0 && m_r_squared > -0.5;
   }
   else
   {
      // 下降趋势线，价格在趋势线下方且斜率小于0
      return price < trend_value * (1.0 - m_error_margin) && m_slope < 0 && m_r_squared > -0.5;
   }
}

//+------------------------------------------------------------------+
//| 获取交易信号方向                                                 |
//+------------------------------------------------------------------+
ENUM_TRENDLINE_DIRECTION CTrendLine::GetSignalDirection(double price)
{
   // 使用Python的质量要求
   if(m_quality < 0.5 || m_direction == TRENDLINE_NEUTRAL || m_r_squared <= -0.5)
      return TRENDLINE_NEUTRAL;
      
   // 检查是否突破趋势线
   double trend_value = GetTrendValue(0);
   
   if(m_direction == TRENDLINE_UP)
   {
      // 上升趋势线被向下突破 - 空头信号
      if(price < trend_value * (1.0 - m_error_margin))
         return TRENDLINE_DOWN;
      else
         return TRENDLINE_UP; // 价格在上升趋势线上方 - 多头信号
   }
   else if(m_direction == TRENDLINE_DOWN)
   {
      // 下降趋势线被向上突破 - 多头信号
      if(price > trend_value * (1.0 + m_error_margin))
         return TRENDLINE_UP;
      else
         return TRENDLINE_DOWN; // 价格在下降趋势线下方 - 空头信号
   }
   
   return TRENDLINE_NEUTRAL;
}

// 趋势过滤参数
input group "趋势线分析参数"
input bool     UseTrendFilter = true;      // 使用趋势线分析
input ENUM_APPLIED_PRICE UpTrendPriceType = PRICE_LOW;   // 上升趋势价格类型
input ENUM_APPLIED_PRICE DownTrendPriceType = PRICE_HIGH; // 下降趋势价格类型
input int      TrendLinePeriod = 30;        // 趋势线分析周期
input double   TrendLineQualityThreshold = 0.5; // 趋势线质量阈值
input double   TrendLineErrorMargin = 0.001;    // 趋势线误差容忍度

// 全局趋势线对象
CTrendLine g_up_trend_line;    // 上升趋势线
CTrendLine g_down_trend_line;  // 下降趋势线

//+------------------------------------------------------------------+
//| 分析趋势线并返回信号方向                                         |
//+------------------------------------------------------------------+
ENUM_TRENDLINE_DIRECTION AnalyzeTrendLineSignal()
{
   // 设置误差容忍度
   g_up_trend_line.SetErrorMargin(TrendLineErrorMargin);
   g_down_trend_line.SetErrorMargin(TrendLineErrorMargin);
   
   // 分析上升趋势线
   bool up_trend_valid = g_up_trend_line.Analyze(UpTrendPriceType, TrendLinePeriod);
   
   // 分析下降趋势线
   bool down_trend_valid = g_down_trend_line.Analyze(DownTrendPriceType, TrendLinePeriod);
   
   // 获取当前价格
   double current_price = SymbolInfoDouble(_Symbol, SYMBOL_LAST);
   
   // 检查上升趋势线信号
   ENUM_TRENDLINE_DIRECTION up_signal = TRENDLINE_NEUTRAL;
   if(up_trend_valid && g_up_trend_line.GetDirection() == TRENDLINE_UP && 
      g_up_trend_line.GetQuality() > TrendLineQualityThreshold)
   {
      up_signal = g_up_trend_line.GetSignalDirection(current_price);
   }
   
   // 检查下降趋势线信号
   ENUM_TRENDLINE_DIRECTION down_signal = TRENDLINE_NEUTRAL;
   if(down_trend_valid && g_down_trend_line.GetDirection() == TRENDLINE_DOWN && 
      g_down_trend_line.GetQuality() > TrendLineQualityThreshold)
   {
      down_signal = g_down_trend_line.GetSignalDirection(current_price);
   }
   
   // 综合两个趋势线的信号
   if(up_signal == TRENDLINE_UP || down_signal == TRENDLINE_UP)
      return TRENDLINE_UP;
   else if(up_signal == TRENDLINE_DOWN || down_signal == TRENDLINE_DOWN)
      return TRENDLINE_DOWN;
   
   return TRENDLINE_NEUTRAL;
}

//+------------------------------------------------------------------+
//| 检测趋势线突破并返回方向                                         |
//+------------------------------------------------------------------+
ENUM_TRENDLINE_DIRECTION CheckTrendLineBreakoutSignal(double price)
{
   ENUM_TRENDLINE_DIRECTION direction = TRENDLINE_NEUTRAL;
   
   // 检查上升趋势线突破
   if(g_up_trend_line.GetDirection() == TRENDLINE_UP && 
      g_up_trend_line.GetQuality() > TrendLineQualityThreshold &&
      g_up_trend_line.IsBreakout(price))
   {
      direction = TRENDLINE_DOWN; // 上升趋势线被突破，空头信号
   }
   
   // 检查下降趋势线突破
   if(g_down_trend_line.GetDirection() == TRENDLINE_DOWN && 
      g_down_trend_line.GetQuality() > TrendLineQualityThreshold &&
      g_down_trend_line.IsBreakout(price))
   {
      direction = TRENDLINE_UP; // 下降趋势线被突破，多头信号
   }
   
   return direction;
}

// 调用方法：调用 AnalyzeTrendLineSignal() 或 CheckTrendLineBreakoutSignal(price) 函数
// 就能直接获取交易方向（TREND_UP 表示多头信号，TREND_DOWN 表示空头信号，TREND_NEUTRAL 表示震荡或无明确信号）

// 以下是成交量放大逻辑
// 采用最近6根K线的成交量来计算当前成交量与平均成交量的比率

//+------------------------------------------------------------------+
//| 成交量分析类                                                     |
//+------------------------------------------------------------------+
class CVolumeAnalyzer
{
private:
   long              m_volumes[];          // 成交量数据 (改为long类型)
   double            m_avg_volume;         // 平均成交量
   double            m_volume_ratio;       // 当前成交量与平均成交量的比率
   int               m_period;             // 分析周期
   double            m_threshold;          // 放大阈值
   
public:
   // 构造函数
   CVolumeAnalyzer() { m_avg_volume = 0; m_volume_ratio = 0; m_period = 6; m_threshold = 1.5; }
   
   // 设置参数
   void SetParameters(int period, double threshold)
   {
      m_period = period;
      m_threshold = threshold;
   }
   
   // 分析成交量
   bool Analyze();
   
   // 检查成交量是否放大
   bool IsVolumeExpansion() { return m_volume_ratio > m_threshold; }
   
   // 获取成交量比率
   double GetVolumeRatio() { return m_volume_ratio; }
   
   // 获取平均成交量
   double GetAverageVolume() { return m_avg_volume; }
   
   // 获取成交量状态
   ENUM_VOLUME_STATE GetVolumeState();
};

//+------------------------------------------------------------------+
//| 分析成交量                                                       |
//+------------------------------------------------------------------+
bool CVolumeAnalyzer::Analyze()
{
   // 复制成交量数据
   ArrayResize(m_volumes, m_period);
   ArraySetAsSeries(m_volumes, true);
   
   // 使用正确的CopyTickVolume函数签名
   int copied = CopyTickVolume(_Symbol, _Period, 0, m_period, m_volumes);
   
   if(copied <= 0)
   {
      Print("复制成交量数据失败，错误码: ", GetLastError());
      return false;
   }
   
   // 计算平均成交量（不包括当前K线）
   double sum_volume = 0;
   for(int i = 1; i < m_period; i++)
   {
      sum_volume += (double)m_volumes[i]; // 转换为double进行计算
   }
   
   m_avg_volume = sum_volume / (m_period - 1);
   
   // 添加除零保护
   if(m_avg_volume <= 0.0000001)
   {
      Print("警告: 平均成交量接近零，无法计算成交量比率");
      m_volume_ratio = 1.0; // 设置为默认值
      return false;
   }
   
   // 计算当前成交量与平均成交量的比率
   m_volume_ratio = (double)m_volumes[0] / m_avg_volume;
   
   if(UseDebugMode && DebugLevel > 1)
   {
      Print("成交量分析: 当前成交量=", m_volumes[0], 
            ", 平均成交量=", m_avg_volume, 
            ", 比率=", m_volume_ratio,
            ", 是否放大=", IsVolumeExpansion() ? "是" : "否");
   }
   
   return true;
}

//+------------------------------------------------------------------+
//| 获取成交量状态                                                   |
//+------------------------------------------------------------------+
ENUM_VOLUME_STATE CVolumeAnalyzer::GetVolumeState()
{
   return IsVolumeExpansion() ? VOLUME_EXPANSION : VOLUME_NORMAL;
}

// 成交量过滤参数
input group "成交量分析参数"
input bool     UseVolumeFilter = true;     // 使用成交量过滤
input int      VolumePeriod = 5;          // 成交量分析周期
input double   VolumeThreshold = 1.5;      // 成交量放大阈值

// 全局成交量分析对象
CVolumeAnalyzer g_volume_analyzer;

//+------------------------------------------------------------------+
//| 分析成交量并返回状态                                             |
//+------------------------------------------------------------------+
ENUM_VOLUME_STATE AnalyzeVolumeState()
{
   // 设置参数
   g_volume_analyzer.SetParameters(VolumePeriod, VolumeThreshold);
   
   // 分析成交量
   if(!g_volume_analyzer.Analyze())
      return VOLUME_NORMAL;
   
   // 获取成交量状态
   ENUM_VOLUME_STATE state = g_volume_analyzer.GetVolumeState();
   
   if(UseDebugMode && DebugLevel > 0)
   {
      Print("成交量状态: ", state == VOLUME_EXPANSION ? "放大" : "正常", 
            ", 成交量比率=", g_volume_analyzer.GetVolumeRatio());
   }
   
   return state;
}

// 外部代码可以调用 AnalyzeVolumeState() 函数获取成交量状态
// 成交量放大逻辑完结

// 以下是三角形态逻辑
//+------------------------------------------------------------------+
//| 收敛三角形分析类                                                 |
//+------------------------------------------------------------------+
class CTriangleAnalyzer
{
private:
   double            m_triangle_high_prices[];      // 高点价格数据
   double            m_triangle_low_prices[];       // 低点价格数据
   int               m_triangle_high_points[];      // 高点索引
   int               m_triangle_low_points[];       // 低点索引
   double            m_triangle_high_slope;         // 高点趋势线斜率
   double            m_triangle_low_slope;          // 低点趋势线斜率
   double            m_triangle_high_intercept;     // 高点趋势线截距
   double            m_triangle_low_intercept;      // 低点趋势线截距
   int               m_triangle_period;             // 分析周期
   double            m_triangle_quality;            // 三角形质量(0-1)
   ENUM_TRIANGLE_TYPE m_triangle_type;              // 三角形类型
   double            m_triangle_convergence_point;  // 收敛点位置
   double            m_triangle_convergence_price;  // 收敛点价格
   double            m_triangle_convergence_angle;  // 收敛角度(度)
   double            m_triangle_width_ratio;        // 宽度比例
   double            m_error_margin;                // 误差容忍度
   
   // 内部方法
   void FindTriangleKeyPoints();
   void CalculateTriangleTrendLines();
   void DetermineTriangleType();
   void CalculateTriangleConvergence();
   void EvaluateTriangleQuality();
   
   // 稳健回归算法 (模拟TheilSen回归)
   void TheilSenRegression(int &points[], double &prices[], double &slope, double &intercept);
   
   // 计算中位数
   double Median(double &values[]);
   
public:
   // 构造函数
   CTriangleAnalyzer() 
   { 
      m_triangle_type = TRIANGLE_NONE; 
      m_triangle_quality = 0; 
      m_triangle_period = 60; 
      m_triangle_high_slope = 0;
      m_triangle_low_slope = 0;
      m_triangle_convergence_angle = 0;
      m_triangle_width_ratio = 0;
      m_error_margin = 0.001;  // 添加误差容忍度
   }
   
   // 设置参数
   void SetParameters(int period, double error_margin = 0.001)
   {
      m_triangle_period = period;
      m_error_margin = error_margin;
   }
   
   // 分析三角形
   bool Analyze();
   
   // 获取三角形类型
   ENUM_TRIANGLE_TYPE GetType() { return m_triangle_type; }
   
   // 获取三角形质量
   double GetQuality() { return m_triangle_quality; }
   
   // 获取收敛角度
   double GetConvergenceAngle() { return m_triangle_convergence_angle; }
   
   // 获取宽度比例
   double GetWidthRatio() { return m_triangle_width_ratio; }
   
   // 获取高点趋势线值
   double GetHighTrendValue(int shift);
   
   // 获取低点趋势线值
   double GetLowTrendValue(int shift);
   
   // 检查价格是否突破三角形
   bool IsBreakout(double price, ENUM_TRIANGLE_DIRECTION &direction);
   
   // 获取交易信号方向
   ENUM_TRIANGLE_DIRECTION GetSignalDirection(double price);
};

//+------------------------------------------------------------------+
//| 计算中位数                                                       |
//+------------------------------------------------------------------+
double CTriangleAnalyzer::Median(double &values[])
{
   int size = ArraySize(values);
   if(size == 0) return 0;
   
   // 创建临时数组并排序
   double temp[];
   ArrayResize(temp, size);
   ArrayCopy(temp, values);
   ArraySort(temp);
   
   // 返回中位数
   if(size % 2 == 0)
      return (temp[size/2-1] + temp[size/2]) / 2.0;
   else
      return temp[size/2];
}

//+------------------------------------------------------------------+
//| 稳健回归算法 (模拟TheilSen回归)                                  |
//+------------------------------------------------------------------+
void CTriangleAnalyzer::TheilSenRegression(int &points[], double &prices[], double &slope, double &intercept)
{
   int point_count = ArraySize(points);
   if(point_count < 2)
   {
      slope = 0;
      intercept = 0;
      return;
   }
   
   // 计算所有可能的斜率
   double slopes[];
   ArrayResize(slopes, 0);
   
   for(int i = 0; i < point_count; i++)
   {
      for(int j = i + 1; j < point_count; j++)
      {
         double x_diff = points[j] - points[i];
         
         // 避免除零
         if(x_diff != 0)
         {
            double slope_value = (prices[j] - prices[i]) / x_diff;
            
            // 添加斜率到数组
            int size = ArraySize(slopes);
            ArrayResize(slopes, size + 1);
            slopes[size] = slope_value;
         }
      }
   }
   
   // 计算斜率的中位数
   slope = Median(slopes);
   
   // 计算截距
   double intercepts[];
   ArrayResize(intercepts, point_count);
   
   for(int i = 0; i < point_count; i++)
   {
      intercepts[i] = prices[i] - slope * points[i];
   }
   
   // 计算截距的中位数
   intercept = Median(intercepts);
}

//+------------------------------------------------------------------+
//| 寻找关键点                                                       |
//+------------------------------------------------------------------+
void CTriangleAnalyzer::FindTriangleKeyPoints()
{
   // 清空数组
   ArrayResize(m_triangle_high_points, 0);
   ArrayResize(m_triangle_low_points, 0);
   
   int size = ArraySize(m_triangle_high_prices);
   if(size < 20) return;
   
   // 使用自适应窗口大小，类似Python中的实现
   int window_size = MathMax(3, MathMin(5, size / 20));
   
   // 寻找高点
   for(int i = window_size; i < size - window_size; i++)
   {
      bool is_high = true;
      for(int j = 1; j <= window_size; j++)
      {
         if(m_triangle_high_prices[i] <= m_triangle_high_prices[i-j] || 
            m_triangle_high_prices[i] <= m_triangle_high_prices[i+j])
         {
            is_high = false;
            break;
         }
      }
      
      if(is_high)
      {
         // 过滤太近的点位
         bool too_close = false;
         if(ArraySize(m_triangle_high_points) > 0)
         {
            int last_idx = m_triangle_high_points[ArraySize(m_triangle_high_points) - 1];
            if(i - last_idx < window_size)
               too_close = true;
         }
         
         if(!too_close)
         {
            int high_count = ArraySize(m_triangle_high_points);
            ArrayResize(m_triangle_high_points, high_count + 1);
            m_triangle_high_points[high_count] = i;
         }
      }
   }
   
   // 寻找低点
   for(int i = window_size; i < size - window_size; i++)
   {
      bool is_low = true;
      for(int j = 1; j <= window_size; j++)
      {
         if(m_triangle_low_prices[i] >= m_triangle_low_prices[i-j] || 
            m_triangle_low_prices[i] >= m_triangle_low_prices[i+j])
         {
            is_low = false;
            break;
         }
      }
      
      if(is_low)
      {
         // 过滤太近的点位
         bool too_close = false;
         if(ArraySize(m_triangle_low_points) > 0)
         {
            int last_idx = m_triangle_low_points[ArraySize(m_triangle_low_points) - 1];
            if(i - last_idx < window_size)
               too_close = true;
         }
         
         if(!too_close)
         {
            int low_count = ArraySize(m_triangle_low_points);
            ArrayResize(m_triangle_low_points, low_count + 1);
            m_triangle_low_points[low_count] = i;
         }
      }
   }
   
   // 确保至少有4个高点和4个低点
   if(ArraySize(m_triangle_high_points) < 4 || ArraySize(m_triangle_low_points) < 4)
   {
      ArrayResize(m_triangle_high_points, 0);
      ArrayResize(m_triangle_low_points, 0);
   }
}

//+------------------------------------------------------------------+
//| 计算趋势线                                                       |
//+------------------------------------------------------------------+
void CTriangleAnalyzer::CalculateTriangleTrendLines()
{
   // 使用稳健回归算法替代最小二乘法
   TheilSenRegression(m_triangle_high_points, m_triangle_high_prices, m_triangle_high_slope, m_triangle_high_intercept);
   TheilSenRegression(m_triangle_low_points, m_triangle_low_prices, m_triangle_low_slope, m_triangle_low_intercept);
}

//+------------------------------------------------------------------+
//| 确定三角形类型                                                   |
//+------------------------------------------------------------------+
void CTriangleAnalyzer::DetermineTriangleType()
{
   // 默认为无三角形
   m_triangle_type = TRIANGLE_NONE;
   
   // 检查斜率 - 使用与Python相同的阈值
   bool high_downward = m_triangle_high_slope < -0.0002;  // 高点趋势线向下
   bool low_upward = m_triangle_low_slope > 0.0002;       // 低点趋势线向上
   bool high_flat = MathAbs(m_triangle_high_slope) < 0.0002;  // 高点趋势线平
   bool low_flat = MathAbs(m_triangle_low_slope) < 0.0002;    // 低点趋势线平
   
   // 添加斜率比例要求，与Python一致
   bool slope_ratio_valid = true;
   if(high_downward && low_upward)
   {
      slope_ratio_valid = (MathAbs(m_triangle_high_slope) > MathAbs(m_triangle_low_slope) * 0.5) && 
                          (MathAbs(m_triangle_high_slope) < MathAbs(m_triangle_low_slope) * 2.0);
   }
   
   // 判断三角形类型
   if(high_downward && low_upward && slope_ratio_valid)
   {
      m_triangle_type = TRIANGLE_SYMMETRIC;  // 对称三角形
   }
   else if(high_downward && low_flat)
   {
      m_triangle_type = TRIANGLE_DESCENDING;  // 下降三角形
   }
   else if(high_flat && low_upward)
   {
      m_triangle_type = TRIANGLE_ASCENDING;  // 上升三角形
   }
}

//+------------------------------------------------------------------+
//| 计算收敛点                                                       |
//+------------------------------------------------------------------+
void CTriangleAnalyzer::CalculateTriangleConvergence()
{
   // 如果斜率平行或相同方向，无法收敛
   if(MathAbs(m_triangle_high_slope - m_triangle_low_slope) < 0.0001)
   {
      m_triangle_convergence_point = 0;
      m_triangle_convergence_price = 0;
      m_triangle_convergence_angle = 0;
      return;
   }
   
   // 计算收敛点
   m_triangle_convergence_point = (m_triangle_low_intercept - m_triangle_high_intercept) / 
                                 (m_triangle_high_slope - m_triangle_low_slope);
   
   // 计算收敛价格
   m_triangle_convergence_price = m_triangle_high_slope * m_triangle_convergence_point + 
                                 m_triangle_high_intercept;
   
   // 计算收敛角度
   double angle_high = MathArctan(m_triangle_high_slope) * 180 / M_PI;
   double angle_low = MathArctan(m_triangle_low_slope) * 180 / M_PI;
   m_triangle_convergence_angle = MathAbs(angle_high - angle_low);
   
   // 计算宽度比例
   if(ArraySize(m_triangle_high_points) > 0 && ArraySize(m_triangle_low_points) > 0)
   {
      int first_idx = MathMin(m_triangle_high_points[0], m_triangle_low_points[0]);
      double start_high = m_triangle_high_slope * first_idx + m_triangle_high_intercept;
      double start_low = m_triangle_low_slope * first_idx + m_triangle_low_intercept;
      double start_width = start_high - start_low;
      
      int last_idx = MathMax(
         m_triangle_high_points[ArraySize(m_triangle_high_points) - 1], 
         m_triangle_low_points[ArraySize(m_triangle_low_points) - 1]
      );
      double end_high = m_triangle_high_slope * last_idx + m_triangle_high_intercept;
      double end_low = m_triangle_low_slope * last_idx + m_triangle_low_intercept;
      double end_width = end_high - end_low;
      
      if(start_width > 0)
         m_triangle_width_ratio = end_width / start_width;
      else
         m_triangle_width_ratio = 0;
   }
   else
   {
      m_triangle_width_ratio = 0;
   }
}

//+------------------------------------------------------------------+
//| 评估三角形质量                                                   |
//+------------------------------------------------------------------+
void CTriangleAnalyzer::EvaluateTriangleQuality()
{
   // 默认质量为0
   m_triangle_quality = 0;
   
   // 如果没有三角形，直接返回
   if(m_triangle_type == TRIANGLE_NONE)
      return;
   
   // 计算点到趋势线的距离
   double total_high_deviation = 0;
   double total_low_deviation = 0;
   int high_touch_count = 0;
   int low_touch_count = 0;
   
   // 高点趋势线拟合度
   for(int i = 0; i < ArraySize(m_triangle_high_points); i++)
   {
      int idx = m_triangle_high_points[i];
      double trend_value = m_triangle_high_slope * idx + m_triangle_high_intercept;
      double deviation = MathAbs(m_triangle_high_prices[idx] - trend_value);
      
      // 相对偏差
      double relative_deviation = deviation / m_triangle_high_prices[idx];
      total_high_deviation += relative_deviation;
      
      // 如果点接近趋势线，计为触点
      if(relative_deviation < 0.01)  // 允许1%的偏差
         high_touch_count++;
   }
   
   // 低点趋势线拟合度
   for(int i = 0; i < ArraySize(m_triangle_low_points); i++)
   {
      int idx = m_triangle_low_points[i];
      double trend_value = m_triangle_low_slope * idx + m_triangle_low_intercept;
      double deviation = MathAbs(m_triangle_low_prices[idx] - trend_value);
      
      // 相对偏差
      double relative_deviation = deviation / m_triangle_low_prices[idx];
      total_low_deviation += relative_deviation;
      
      // 如果点接近趋势线，计为触点
      if(relative_deviation < 0.01)  // 允许1%的偏差
         low_touch_count++;
   }
   
   // 计算平均偏差
   double avg_high_deviation = total_high_deviation / MathMax(1, ArraySize(m_triangle_high_points));
   double avg_low_deviation = total_low_deviation / MathMax(1, ArraySize(m_triangle_low_points));
   
   // 计算拟合质量 (0-1)，与Python一致使用0.7的阈值
   double high_quality = 1.0 - MathMin(avg_high_deviation * 10, 1.0);  // 将10%的偏差映射到0-1
   double low_quality = 1.0 - MathMin(avg_low_deviation * 10, 1.0);
   
   // 检查是否满足Python的拟合度要求
   bool fit_quality_valid = (high_quality > 0.7 && low_quality > 0.7);
   
   // 宽度比例检查 - 与Python一致使用0.6的阈值
   bool width_ratio_valid = (m_triangle_width_ratio < 0.6);
   
   // 综合质量评分
   double fit_quality = (high_quality + low_quality) / 2.0;
   
   // 触点数量权重
   double touch_quality = (high_touch_count + low_touch_count) / MathMax(4.0, (double)(ArraySize(m_triangle_high_points) + ArraySize(m_triangle_low_points)) * 0.5);
   touch_quality = MathMin(touch_quality, 1.0);
   
   // 收敛角度权重 - 理想角度在15-45度之间
   double angle_quality = 0;
   if(m_triangle_convergence_angle >= 10 && m_triangle_convergence_angle <= 60)
      angle_quality = 1.0 - MathAbs(m_triangle_convergence_angle - 30) / 30.0;
   else
      angle_quality = 0.2;  // 给一个基础分
   
   // 宽度比例权重
   double width_quality = width_ratio_valid ? 1.0 : 0.2;
   
   // 综合评分 - 使用Python的条件进行加权
   if(fit_quality_valid && width_ratio_valid)
   {
      m_triangle_quality = fit_quality * 0.5 + touch_quality * 0.3 + angle_quality * 0.1 + width_quality * 0.1;
   }
   else
   {
      m_triangle_quality = fit_quality * 0.3 + touch_quality * 0.2 + angle_quality * 0.1 + width_quality * 0.1;
   }
}

//+------------------------------------------------------------------+
//| 分析三角形                                                       |
//+------------------------------------------------------------------+
bool CTriangleAnalyzer::Analyze()
{
   // 复制价格数据
   ArrayResize(m_triangle_high_prices, m_triangle_period);
   ArrayResize(m_triangle_low_prices, m_triangle_period);
   ArraySetAsSeries(m_triangle_high_prices, true);
   ArraySetAsSeries(m_triangle_low_prices, true);
   
   int copied_high = CopyHigh(_Symbol, _Period, 0, m_triangle_period, m_triangle_high_prices);
   int copied_low = CopyLow(_Symbol, _Period, 0, m_triangle_period, m_triangle_low_prices);
   
   if(copied_high <= 0 || copied_low <= 0)
   {
      Print("复制价格数据失败，错误码: ", GetLastError());
      return false;
   }
   
   // 寻找关键点
   FindTriangleKeyPoints();
   
   // 如果没有足够的关键点，返回失败
   if(ArraySize(m_triangle_high_points) < 4 || ArraySize(m_triangle_low_points) < 4)
      return false;
   
   // 计算趋势线
   CalculateTriangleTrendLines();
   
   // 确定三角形类型
   DetermineTriangleType();
   
   // 计算收敛点
   CalculateTriangleConvergence();
   
   // 评估三角形质量
   EvaluateTriangleQuality();
   
   // 降低要求：质量阈值从0.6降到0.4
   return m_triangle_type != TRIANGLE_NONE && m_triangle_quality > 0.4;
}

//+------------------------------------------------------------------+
//| 获取高点趋势线值                                                 |
//+------------------------------------------------------------------+
double CTriangleAnalyzer::GetHighTrendValue(int shift)
{
   return m_triangle_high_slope * shift + m_triangle_high_intercept;
}

//+------------------------------------------------------------------+
//| 获取低点趋势线值                                                 |
//+------------------------------------------------------------------+
double CTriangleAnalyzer::GetLowTrendValue(int shift)
{
   return m_triangle_low_slope * shift + m_triangle_low_intercept;
}

//+------------------------------------------------------------------+
//| 检查价格是否突破三角形                                           |
//+------------------------------------------------------------------+
bool CTriangleAnalyzer::IsBreakout(double price, ENUM_TRIANGLE_DIRECTION &direction)
{
   // 如果没有三角形，返回false
   if(m_triangle_type == TRIANGLE_NONE)
   {
      direction = TRIANGLE_NEUTRAL;
      return false;
   }
   
   // 获取当前趋势线值
   double high_value = GetHighTrendValue(0);
   double low_value = GetLowTrendValue(0);
   
   // 添加误差容忍度
   bool breakout_up = price > high_value * (1.0 + m_error_margin);
   bool breakout_down = price < low_value * (1.0 - m_error_margin);
   
   if(breakout_up)
   {
      direction = TRIANGLE_UP;
      return true;
   }
   else if(breakout_down)
   {
      direction = TRIANGLE_DOWN;
      return true;
   }
   
   direction = TRIANGLE_NEUTRAL;
   return false;
}

//+------------------------------------------------------------------+
//| 获取交易信号方向                                                 |
//+------------------------------------------------------------------+
ENUM_TRIANGLE_DIRECTION CTriangleAnalyzer::GetSignalDirection(double price)
{
   // 如果三角形质量不够或者没有形成三角形，返回中性信号
   if(m_triangle_quality < 0.4 || m_triangle_type == TRIANGLE_NONE)
      return TRIANGLE_NEUTRAL;
      
   // 检查是否突破三角形
   ENUM_TRIANGLE_DIRECTION breakout_direction = TRIANGLE_NEUTRAL;
   bool is_breakout = IsBreakout(price, breakout_direction);
   
   if(is_breakout)
      return breakout_direction;
      
   // 如果没有突破，根据三角形类型给出预期方向
   switch(m_triangle_type)
   {
      case TRIANGLE_ASCENDING:
         return TRIANGLE_UP;    // 上升三角形通常看涨
         
      case TRIANGLE_DESCENDING:
         return TRIANGLE_DOWN;  // 下降三角形通常看跌
         
      case TRIANGLE_SYMMETRIC:
         // 对称三角形需要等待突破确认
         return TRIANGLE_NEUTRAL;
   }
   
   return TRIANGLE_NEUTRAL;
}

// 三角形分析参数
input group "三角形分析参数"
input bool     UseTrianglePattern = true;  // 使用三角形形态分析
input int      TrianglePeriod = 30;        // 三角形分析周期
input double   TriangleQualityThreshold = 0.7; // 三角形质量阈值 (提高到0.7与Python一致)
input double   TriangleErrorMargin = 0.001;    // 三角形误差容忍度

// 全局三角形分析对象
CTriangleAnalyzer g_triangle_analyzer;

//+------------------------------------------------------------------+
//| 分析三角形并返回信号方向                                         |
//+------------------------------------------------------------------+
ENUM_TRIANGLE_DIRECTION AnalyzeTriangleSignal()
{
   // 设置参数
   g_triangle_analyzer.SetParameters(TrianglePeriod, TriangleErrorMargin);
   
   // 分析三角形
   bool triangle_valid = g_triangle_analyzer.Analyze();
   
   if(!triangle_valid || g_triangle_analyzer.GetQuality() < TriangleQualityThreshold)
      return TRIANGLE_NEUTRAL;
   
   // 获取当前价格
   double current_price = SymbolInfoDouble(_Symbol, SYMBOL_LAST);
   
   // 获取交易信号方向
   ENUM_TRIANGLE_DIRECTION signal = g_triangle_analyzer.GetSignalDirection(current_price);
   
   if(UseDebugMode && DebugLevel > 0)
   {
      string type_str = "";
      switch(g_triangle_analyzer.GetType())
      {
         case TRIANGLE_SYMMETRIC: type_str = "对称三角形"; break;
         case TRIANGLE_ASCENDING: type_str = "上升三角形"; break;
         case TRIANGLE_DESCENDING: type_str = "下降三角形"; break;
      }
      
      string signal_str = "中性";
      if(signal == TRIANGLE_UP) signal_str = "多头";
      else if(signal == TRIANGLE_DOWN) signal_str = "空头";
      
      Print("三角形信号: ", signal_str, 
            ", 类型=", type_str, 
            ", 质量=", g_triangle_analyzer.GetQuality(),
            ", 宽度比=", g_triangle_analyzer.GetWidthRatio(),
            ", 收敛角度=", g_triangle_analyzer.GetConvergenceAngle());
   }
   
   return signal;
}

// 三角形态逻辑完结

// 以下是头肩底和头肩顶形态逻辑
//+------------------------------------------------------------------+
//| 头肩形态分析类                                                   |
//+------------------------------------------------------------------+
class CHeadAndShouldersAnalyzer
{
private:
   double            m_hs_high_prices[];      // 高点价格数据
   double            m_hs_low_prices[];       // 低点价格数据
   double            m_hs_close_prices[];     // 收盘价数据
   int               m_hs_period;             // 分析周期
   
   int               m_hs_left_shoulder_idx;  // 左肩索引
   int               m_hs_head_idx;           // 头部索引
   int               m_hs_right_shoulder_idx; // 右肩索引
   int               m_hs_neckline_left_idx;  // 颈线左侧点索引
   int               m_hs_neckline_right_idx; // 颈线右侧点索引
   
   double            m_hs_neckline_slope;     // 颈线斜率
   double            m_hs_neckline_intercept; // 颈线截距
   
   ENUM_HS_PATTERN_TYPE m_hs_type;            // 形态类型
   double            m_hs_quality;            // 形态质量(0-1)
   double            m_hs_completion;         // 形态完成度(0-1)
   double            m_hs_breakout_threshold; // 突破阈值
   
   // 内部方法
   void FindKeyPoints();
   void CalculateNeckline();
   void DeterminePatternType();
   void EvaluateQuality();
   bool ValidatePattern();
   
public:
   // 构造函数
   CHeadAndShouldersAnalyzer() 
   { 
      m_hs_type = HS_PATTERN_NONE; 
      m_hs_quality = 0; 
      m_hs_period = 100;
      m_hs_completion = 0;
      m_hs_left_shoulder_idx = -1;
      m_hs_head_idx = -1;
      m_hs_right_shoulder_idx = -1;
      m_hs_neckline_left_idx = -1;
      m_hs_neckline_right_idx = -1;
      m_hs_neckline_slope = 0;
      m_hs_neckline_intercept = 0;
      m_hs_breakout_threshold = 0.03;
   }
   
   // 设置参数
   void SetParameters(int period, double breakout_threshold = 0.03)
   {
      m_hs_period = period;
      m_hs_breakout_threshold = breakout_threshold;
   }
   
   // 分析形态
   bool Analyze();
   
   // 获取形态类型
   ENUM_HS_PATTERN_TYPE GetType() { return m_hs_type; }
   
   // 获取形态质量
   double GetQuality() { return m_hs_quality; }
   
   // 获取形态完成度
   double GetCompletion() { return m_hs_completion; }
   
   // 获取颈线值
   double GetNecklineValue(int shift);
   
   // 检查价格是否突破颈线
   bool IsBreakout(double price, ENUM_HS_DIRECTION &direction);
   
   // 获取形态高度
   double GetPatternHeight();
   
   // 获取目标价格
   double GetPriceTarget();
   
   // 获取交易信号方向
   ENUM_HS_DIRECTION GetSignalDirection(double price);
};

//+------------------------------------------------------------------+
//| 寻找关键点                                                       |
//+------------------------------------------------------------------+
void CHeadAndShouldersAnalyzer::FindKeyPoints()
{
   // 重置关键点索引
   m_hs_left_shoulder_idx = -1;
   m_hs_head_idx = -1;
   m_hs_right_shoulder_idx = -1;
   m_hs_neckline_left_idx = -1;
   m_hs_neckline_right_idx = -1;
   
   int size = ArraySize(m_hs_low_prices);
   if(size < 30) return; // 确保有足够的数据
   
   // 使用较小的窗口大小，与Python一致
   int window_size = 3;
   
   // 寻找局部极值点
   int low_points[];
   double low_values[];
   
   for(int i = window_size; i < size - window_size; i++)
   {
      bool is_low_point = true;
      
      // 检查是否是局部最低点
      for(int j = 1; j <= window_size; j++)
      {
         if(m_hs_low_prices[i] > m_hs_low_prices[i-j] || m_hs_low_prices[i] > m_hs_low_prices[i+j])
         {
            is_low_point = false;
            break;
         }
      }
      
      if(is_low_point)
      {
         // 添加到低点数组
         int new_size = ArraySize(low_points) + 1;
         ArrayResize(low_points, new_size);
         ArrayResize(low_values, new_size);
         low_points[new_size - 1] = i;
         low_values[new_size - 1] = m_hs_low_prices[i];
      }
   }
   
   // 如果找到至少3个低点，选择最近的三个
   int low_count = ArraySize(low_points);
   if(low_count >= 3)
   {
      // 选择最近的三个低点
      m_hs_left_shoulder_idx = low_points[low_count - 3];
      m_hs_head_idx = low_points[low_count - 2];
      m_hs_right_shoulder_idx = low_points[low_count - 1];
      
      // 设置颈线点
      m_hs_neckline_left_idx = m_hs_left_shoulder_idx;
      m_hs_neckline_right_idx = m_hs_right_shoulder_idx;
   }
}

//+------------------------------------------------------------------+
//| 计算颈线                                                         |
//+------------------------------------------------------------------+
void CHeadAndShouldersAnalyzer::CalculateNeckline()
{
   if(m_hs_neckline_left_idx < 0 || m_hs_neckline_right_idx < 0)
   {
      m_hs_neckline_slope = 0;
      m_hs_neckline_intercept = 0;
      return;
   }
   
   // 使用左肩和右肩之间的最高点作为颈线点
   double left_high = 0, right_high = 0;
   int left_high_idx = m_hs_left_shoulder_idx, right_high_idx = m_hs_right_shoulder_idx;
   
   // 寻找左肩和头部之间的最高点
   for(int i = m_hs_left_shoulder_idx; i <= m_hs_head_idx; i++)
   {
      if(m_hs_high_prices[i] > left_high)
      {
         left_high = m_hs_high_prices[i];
         left_high_idx = i;
      }
   }
   
   // 寻找头部和右肩之间的最高点
   for(int i = m_hs_head_idx; i <= m_hs_right_shoulder_idx; i++)
   {
      if(m_hs_high_prices[i] > right_high)
      {
         right_high = m_hs_high_prices[i];
         right_high_idx = i;
      }
   }
   
   // 更新颈线点
   m_hs_neckline_left_idx = left_high_idx;
   m_hs_neckline_right_idx = right_high_idx;
   
   // 计算颈线斜率和截距
   double x_diff = m_hs_neckline_right_idx - m_hs_neckline_left_idx;
   if(x_diff != 0)
   {
      m_hs_neckline_slope = (right_high - left_high) / x_diff;
      m_hs_neckline_intercept = left_high - m_hs_neckline_slope * m_hs_neckline_left_idx;
   }
   else
   {
      m_hs_neckline_slope = 0;
      m_hs_neckline_intercept = (left_high + right_high) / 2;
   }
}

//+------------------------------------------------------------------+
//| 确定形态类型                                                     |
//+------------------------------------------------------------------+
void CHeadAndShouldersAnalyzer::DeterminePatternType()
{
   if(m_hs_left_shoulder_idx < 0 || m_hs_head_idx < 0 || m_hs_right_shoulder_idx < 0)
   {
      m_hs_type = HS_PATTERN_NONE;
      return;
   }
   
   double left_shoulder = m_hs_low_prices[m_hs_left_shoulder_idx];
   double head = m_hs_low_prices[m_hs_head_idx];
   double right_shoulder = m_hs_low_prices[m_hs_right_shoulder_idx];
   
   // 头肩底判断条件 - 使用Python的宽松条件
   if(head < left_shoulder && head < right_shoulder)
   {
      m_hs_type = HS_PATTERN_BOTTOM;
   }
   // 头肩顶判断条件
   else if(head > left_shoulder && head > right_shoulder)
   {
      m_hs_type = HS_PATTERN_TOP;
   }
   else
   {
      m_hs_type = HS_PATTERN_NONE;
   }
}

//+------------------------------------------------------------------+
//| 验证形态                                                         |
//+------------------------------------------------------------------+
bool CHeadAndShouldersAnalyzer::ValidatePattern()
{
   if(m_hs_type == HS_PATTERN_NONE)
      return false;
      
   double left_shoulder = m_hs_low_prices[m_hs_left_shoulder_idx];
   double head = m_hs_low_prices[m_hs_head_idx];
   double right_shoulder = m_hs_low_prices[m_hs_right_shoulder_idx];
   
   // 计算关键特征
   double head_to_left = MathAbs(head - left_shoulder);
   double head_to_right = MathAbs(head - right_shoulder);
   double shoulders_diff = MathAbs(left_shoulder - right_shoulder);
   
   // 计算形态深度 - 与Python一致
   double pattern_depth = MathMin(head_to_left, head_to_right) / head;
   
   // 计算时间间隔
   int left_span = m_hs_head_idx - m_hs_left_shoulder_idx;
   int right_span = m_hs_right_shoulder_idx - m_hs_head_idx;
   double time_symmetry = MathMin(left_span, right_span) / (double)MathMax(left_span, right_span);
   
   // 计算反弹确认 - 新增，与Python一致
   double left_high = 0, right_high = 0;
   
   // 寻找左肩后的反弹高点
   for(int i = m_hs_left_shoulder_idx; i <= m_hs_head_idx; i++)
   {
      if(m_hs_high_prices[i] > left_high)
      {
         left_high = m_hs_high_prices[i];
      }
   }
   
   // 寻找右肩后的反弹高点
   for(int i = m_hs_right_shoulder_idx; i < MathMin(m_hs_right_shoulder_idx + 5, ArraySize(m_hs_high_prices)); i++)
   {
      if(m_hs_high_prices[i] > right_high)
      {
         right_high = m_hs_high_prices[i];
      }
   }
   
   double left_rebound = left_high / left_shoulder - 1;
   double right_rebound = right_high / right_shoulder - 1;
   
   // 使用Python的宽松条件
   bool is_valid = (
      shoulders_diff < MathMin(head_to_left, head_to_right) * 0.8 &&  // 肩部差异放宽到80%
      pattern_depth > 0.003 &&  // 形态深度降低到0.3%
      time_symmetry > 0.5 &&    // 时间对称性要求
      MathMin(left_rebound, right_rebound) > 0.001  // 确保有最小反弹(0.1%)
   );
   
   return is_valid;
}

//+------------------------------------------------------------------+
//| 评估质量                                                         |
//+------------------------------------------------------------------+
void CHeadAndShouldersAnalyzer::EvaluateQuality()
{
   if(m_hs_type == HS_PATTERN_NONE)
   {
      m_hs_quality = 0;
      m_hs_completion = 0;
      return;
   }
   
   double left_shoulder = m_hs_low_prices[m_hs_left_shoulder_idx];
   double head = m_hs_low_prices[m_hs_head_idx];
   double right_shoulder = m_hs_low_prices[m_hs_right_shoulder_idx];
   
   // 计算关键特征
   double head_to_left = MathAbs(head - left_shoulder);
   double head_to_right = MathAbs(head - right_shoulder);
   double shoulders_diff = MathAbs(left_shoulder - right_shoulder);
   
   // 计算形态深度
   double pattern_depth = MathMin(head_to_left, head_to_right) / MathAbs(head);
   
   // 计算肩部对称性
   double shoulder_symmetry = 1.0 - (shoulders_diff / MathMin(head_to_left, head_to_right));
   shoulder_symmetry = MathMax(0, shoulder_symmetry);
   
   // 计算时间间隔对称性
   int left_span = m_hs_head_idx - m_hs_left_shoulder_idx;
   int right_span = m_hs_right_shoulder_idx - m_hs_head_idx;
   double time_symmetry = MathMin(left_span, right_span) / (double)MathMax(left_span, right_span);
   
   // 计算颈线质量
   double neckline_quality = 1.0 - MathAbs(m_hs_neckline_slope) * 10; // 颈线越平，质量越高
   neckline_quality = MathMax(0, neckline_quality);
   
   // 综合评分 - 调整权重
   m_hs_quality = pattern_depth * 0.3 + shoulder_symmetry * 0.3 + time_symmetry * 0.2 + neckline_quality * 0.2;
   
   // 计算完成度
   int total_pattern_span = m_hs_right_shoulder_idx - m_hs_left_shoulder_idx;
   int current_position = ArraySize(m_hs_low_prices) - 1 - m_hs_left_shoulder_idx;
   m_hs_completion = MathMin(1.0, current_position / (double)total_pattern_span);
}

//+------------------------------------------------------------------+
//| 分析形态                                                         |
//+------------------------------------------------------------------+
bool CHeadAndShouldersAnalyzer::Analyze()
{
   // 复制价格数据
   ArrayResize(m_hs_high_prices, m_hs_period);
   ArrayResize(m_hs_low_prices, m_hs_period);
   ArrayResize(m_hs_close_prices, m_hs_period);
   
   ArraySetAsSeries(m_hs_high_prices, true);
   ArraySetAsSeries(m_hs_low_prices, true);
   ArraySetAsSeries(m_hs_close_prices, true);
   
   int copied_high = CopyHigh(_Symbol, _Period, 0, m_hs_period, m_hs_high_prices);
   int copied_low = CopyLow(_Symbol, _Period, 0, m_hs_period, m_hs_low_prices);
   int copied_close = CopyClose(_Symbol, _Period, 0, m_hs_period, m_hs_close_prices);
   
   if(copied_high <= 0 || copied_low <= 0 || copied_close <= 0)
   {
      Print("复制价格数据失败，错误码: ", GetLastError());
      return false;
   }
   
   // 寻找关键点
   FindKeyPoints();
   
   // 确定形态类型
   DeterminePatternType();
   
   // 验证形态
   bool is_valid = ValidatePattern();
   
   if(is_valid)
   {
      // 计算颈线
      CalculateNeckline();
      
      // 评估质量
      EvaluateQuality();
   }
   else
   {
      m_hs_type = HS_PATTERN_NONE;
      m_hs_quality = 0;
      m_hs_completion = 0;
   }
   
   return is_valid && m_hs_quality > 0.5;
}

//+------------------------------------------------------------------+
//| 获取颈线值                                                       |
//+------------------------------------------------------------------+
double CHeadAndShouldersAnalyzer::GetNecklineValue(int shift)
{
   return m_hs_neckline_slope * shift + m_hs_neckline_intercept;
}

//+------------------------------------------------------------------+
//| 检查价格是否突破颈线                                             |
//+------------------------------------------------------------------+
bool CHeadAndShouldersAnalyzer::IsBreakout(double price, ENUM_HS_DIRECTION &direction)
{
   if(m_hs_type == HS_PATTERN_NONE || m_hs_quality < 0.5)
   {
      direction = HS_NEUTRAL;
      return false;
   }
   
   double neckline_value = GetNecklineValue(0);
   
   if(m_hs_type == HS_PATTERN_BOTTOM)
   {
      // 头肩底突破颈线 - 多头信号
      if(price > neckline_value * (1.0 + m_hs_breakout_threshold))
      {
         direction = HS_UP;
         return true;
      }
   }
   else if(m_hs_type == HS_PATTERN_TOP)
   {
      // 头肩顶突破颈线 - 空头信号
      if(price < neckline_value * (1.0 - m_hs_breakout_threshold))
      {
         direction = HS_DOWN;
         return true;
      }
   }
   
   direction = HS_NEUTRAL;
   return false;
}

//+------------------------------------------------------------------+
//| 获取形态高度                                                     |
//+------------------------------------------------------------------+
double CHeadAndShouldersAnalyzer::GetPatternHeight()
{
   if(m_hs_type == HS_PATTERN_NONE)
      return 0;
      
   double head = m_hs_low_prices[m_hs_head_idx];
   double neckline_at_head = GetNecklineValue(m_hs_head_idx);
   
   return MathAbs(neckline_at_head - head);
}

//+------------------------------------------------------------------+
//| 获取目标价格                                                     |
//+------------------------------------------------------------------+
double CHeadAndShouldersAnalyzer::GetPriceTarget()
{
   if(m_hs_type == HS_PATTERN_NONE)
      return 0;
      
   double pattern_height = GetPatternHeight();
   double neckline_value = GetNecklineValue(0);
   
   if(m_hs_type == HS_PATTERN_BOTTOM)
   {
      // 头肩底目标价格 = 颈线 + 形态高度
      return neckline_value + pattern_height;
   }
   else if(m_hs_type == HS_PATTERN_TOP)
   {
      // 头肩顶目标价格 = 颈线 - 形态高度
      return neckline_value - pattern_height;
   }
   
   return 0;
}

//+------------------------------------------------------------------+
//| 获取交易信号方向                                                 |
//+------------------------------------------------------------------+
ENUM_HS_DIRECTION CHeadAndShouldersAnalyzer::GetSignalDirection(double price)
{
   if(m_hs_type == HS_PATTERN_NONE || m_hs_quality < 0.5)
      return HS_NEUTRAL;
      
   // 检查是否突破颈线
   ENUM_HS_DIRECTION breakout_direction = HS_NEUTRAL;
   bool is_breakout = IsBreakout(price, breakout_direction);
   
   if(is_breakout)
      return breakout_direction;
      
   // 如果没有突破，根据形态类型给出预期方向
   if(m_hs_type == HS_PATTERN_BOTTOM && m_hs_completion > 0.9)
   {
      // 头肩底即将完成，预期向上
      return HS_UP;
   }
   else if(m_hs_type == HS_PATTERN_TOP && m_hs_completion > 0.9)
   {
      // 头肩顶即将完成，预期向下
      return HS_DOWN;
   }
   
   return HS_NEUTRAL;
}

// 头肩形态参数
input group "头肩形态分析参数"
input bool     UseHSPattern = true;        // 使用头肩形态分析
input int      HSPeriod = 30;             // 头肩形态分析周期
input double   HSQualityThreshold = 0.5;   // 头肩形态质量阈值
input double   HSBreakoutThreshold = 0.03; // 头肩形态突破阈值

// 全局头肩形态分析对象
CHeadAndShouldersAnalyzer g_hs_analyzer;

//+------------------------------------------------------------------+
//| 分析头肩形态并返回信号方向                                       |
//+------------------------------------------------------------------+
ENUM_HS_DIRECTION AnalyzeHSSignal()
{
   // 设置参数
   g_hs_analyzer.SetParameters(HSPeriod, HSBreakoutThreshold);
   
   // 分析头肩形态
   bool hs_valid = g_hs_analyzer.Analyze();
   
   if(!hs_valid || g_hs_analyzer.GetQuality() < HSQualityThreshold)
      return HS_NEUTRAL;
   
   // 获取当前价格
   double current_price = SymbolInfoDouble(_Symbol, SYMBOL_LAST);
   
   // 获取交易信号方向
   ENUM_HS_DIRECTION signal = g_hs_analyzer.GetSignalDirection(current_price);
   
   if(UseDebugMode && DebugLevel > 0)
   {
      string type_str = "";
      switch(g_hs_analyzer.GetType())
      {
         case HS_PATTERN_BOTTOM: type_str = "头肩底"; break;
         case HS_PATTERN_TOP: type_str = "头肩顶"; break;
      }
      
      string signal_str = "中性";
      if(signal == HS_UP) signal_str = "多头";
      else if(signal == HS_DOWN) signal_str = "空头";
      
      Print("头肩形态信号: ", signal_str, 
            ", 类型=", type_str, 
            ", 质量=", g_hs_analyzer.GetQuality(),
            ", 完成度=", g_hs_analyzer.GetCompletion(),
            ", 目标价格=", g_hs_analyzer.GetPriceTarget());
   }
   
   return signal;
}

// 头肩形态逻辑完结
// 以下是箱体理论逻辑

//+------------------------------------------------------------------+
//| 箱体分析类                                                       |
//+------------------------------------------------------------------+
class CBoxAnalyzer
{
private:
   double            m_box_high_prices[];      // 高点价格数据
   double            m_box_low_prices[];       // 低点价格数据
   int               m_box_high_points[];      // 高点索引
   int               m_box_low_points[];       // 低点索引
   double            m_box_upper_slope;        // 上边界斜率
   double            m_box_lower_slope;        // 下边界斜率
   double            m_box_upper_intercept;    // 上边界截距
   double            m_box_lower_intercept;    // 下边界截距
   int               m_box_period;             // 分析周期
   double            m_box_quality;            // 箱体质量(0-1)
   ENUM_BOX_TYPE     m_box_type;               // 箱体类型
   double            m_box_height_ratio;       // 高度比例
   double            m_box_touch_count;        // 触点数量
   double            m_box_price_range;        // 价格范围
   double            m_box_breakout_threshold; // 突破阈值
   
   // 内部方法
   void FindDensityRegions();
   void EvaluateBoxQuality();
   void FindLocalExtrema();
   
public:
   // 构造函数
   CBoxAnalyzer() 
   { 
      m_box_type = BOX_NONE; 
      m_box_quality = 0; 
      m_box_period = 60; 
      m_box_upper_slope = 0;
      m_box_lower_slope = 0;
      m_box_height_ratio = 0;
      m_box_touch_count = 0;
      m_box_breakout_threshold = 0.03; // 默认3%
   }
   
   // 设置参数
   void SetParameters(int period, double breakout_threshold = 0.03)
   {
      m_box_period = period;
      m_box_breakout_threshold = breakout_threshold;
   }
   
   // 分析箱体
   bool Analyze();
   
   // 获取箱体类型
   ENUM_BOX_TYPE GetType() { return m_box_type; }
   
   // 获取箱体质量
   double GetQuality() { return m_box_quality; }
   
   // 获取高度比例
   double GetHeightRatio() { return m_box_height_ratio; }
   
   // 获取触点数量
   double GetTouchCount() { return m_box_touch_count; }
   
   // 获取上边界值
   double GetUpperValue(int shift);
   
   // 获取下边界值
   double GetLowerValue(int shift);
   
   // 检查价格是否突破箱体
   bool IsBreakout(double price, ENUM_BOX_DIRECTION &direction);
   
   // 获取交易信号方向
   ENUM_BOX_DIRECTION GetSignalDirection(double price);
};

//+------------------------------------------------------------------+
//| 使用改进的KDE方法寻找价格密集区域                                |
//+------------------------------------------------------------------+
void CBoxAnalyzer::FindDensityRegions()
{
   int size = ArraySize(m_box_high_prices);
   if(size < 20) return;
   
   // 创建价格序列用于KDE计算
   double price_series[];
   ArrayResize(price_series, size);
   
   // 使用收盘价(这里用高低价的平均值模拟)
   for(int i = 0; i < size; i++)
   {
      price_series[i] = (m_box_high_prices[i] + m_box_low_prices[i]) / 2.0;
   }
   
   // 计算价格范围
   double min_price = ArrayMinimum(m_box_low_prices);
   double max_price = ArrayMaximum(m_box_high_prices);
   double price_range = max_price - min_price;
   
   // 创建价格区间 - 增加到100个区间，与Python一致
   int bin_count = 100;
   double bin_size = price_range / bin_count;
   double price_bins[];
   ArrayResize(price_bins, bin_count);
   double density[];
   ArrayResize(density, bin_count);
   
   // 初始化价格区间
   for(int i = 0; i < bin_count; i++)
   {
      price_bins[i] = min_price + bin_size * i;
      density[i] = 0;
   }
   
   // 使用改进的高斯核密度估计
   double bandwidth = price_range * 0.02; // 带宽参数，可调整
   
   for(int i = 0; i < size; i++)
   {
      double price = price_series[i];
      
      for(int j = 0; j < bin_count; j++)
      {
         // 高斯核函数
         double distance = price - price_bins[j];
         double kernel = MathExp(-(distance * distance) / (2 * bandwidth * bandwidth));
         density[j] += kernel;
      }
   }
   
   // 找到密度阈值（取前30%的高密度区域，与Python一致）
   double sorted_density[];
   ArrayResize(sorted_density, bin_count);
   ArrayCopy(sorted_density, density);
   ArraySort(sorted_density);
   int percentile_idx = (int)(bin_count * 0.7); // 70%分位数
   double density_threshold = sorted_density[percentile_idx];
   
   // 找到高密度区域的上下边界
   int lower_bin = -1, upper_bin = -1;
   
   // 从下往上找第一个高密度区域
   for(int i = 0; i < bin_count; i++)
   {
      if(density[i] > density_threshold)
      {
         lower_bin = i;
         break;
      }
   }
   
   // 从上往下找第一个高密度区域
   for(int i = bin_count-1; i >= 0; i--)
   {
      if(density[i] > density_threshold)
      {
         upper_bin = i;
         break;
      }
   }
   
   // 计算箱体边界价格
   if(lower_bin >= 0 && upper_bin >= 0 && lower_bin < upper_bin)
   {
      double box_bottom = price_bins[lower_bin];
      double box_top = price_bins[upper_bin];
      
      // 设置箱体边界线
      m_box_upper_intercept = box_top;
      m_box_lower_intercept = box_bottom;
      m_box_upper_slope = 0;
      m_box_lower_slope = 0;
      
      // 计算箱体高度
      m_box_price_range = box_top - box_bottom;
      
      // 设置箱体类型
      m_box_type = BOX_HORIZONTAL;
      
      // 计算高度比例
      double avg_price = 0;
      for(int i = 0; i < size; i++)
      {
         avg_price += price_series[i];
      }
      
      if(size > 0)
      {
         avg_price /= size;
         if(MathAbs(avg_price) > 0.0000001)
         {
            m_box_height_ratio = m_box_price_range / avg_price;
         }
      }
      
      // 寻找局部极值点作为触点
      FindLocalExtrema();
   }
}

//+------------------------------------------------------------------+
//| 寻找局部极值点作为触点                                           |
//+------------------------------------------------------------------+
void CBoxAnalyzer::FindLocalExtrema()
{
   int size = ArraySize(m_box_high_prices);
   m_box_touch_count = 0;
   
   // 创建价格序列
   double price_series[];
   ArrayResize(price_series, size);
   
   for(int i = 0; i < size; i++)
   {
      price_series[i] = (m_box_high_prices[i] + m_box_low_prices[i]) / 2.0;
   }
   
   // 寻找局部极值点
   for(int i = 2; i < size - 2; i++)
   {
      // 局部最小值
      bool is_min = true;
      for(int j = MathMax(0, i-2); j <= MathMin(size-1, i+2); j++)
      {
         if(j != i && price_series[j] < price_series[i])
         {
            is_min = false;
            break;
         }
      }
      
      // 局部最大值
      bool is_max = true;
      for(int j = MathMax(0, i-2); j <= MathMin(size-1, i+2); j++)
      {
         if(j != i && price_series[j] > price_series[i])
         {
            is_max = false;
            break;
         }
      }
      
      // 检查是否在箱体范围内或接近边界
      if((is_min || is_max))
      {
         double error_margin = 0.001; // 使用与Python一致的误差容忍度
         
         // 检查是否接近上边界
         if(MathAbs(price_series[i] - m_box_upper_intercept) / m_box_upper_intercept < error_margin)
         {
            m_box_touch_count++;
         }
         // 检查是否接近下边界
         else if(MathAbs(price_series[i] - m_box_lower_intercept) / m_box_lower_intercept < error_margin)
         {
            m_box_touch_count++;
         }
         // 检查是否在箱体内
         else if(price_series[i] >= m_box_lower_intercept * (1 - error_margin) && 
                 price_series[i] <= m_box_upper_intercept * (1 + error_margin))
         {
            // 在箱体内的极值点也计入触点
            m_box_touch_count++;
         }
      }
   }
}

//+------------------------------------------------------------------+
//| 评估箱体质量                                                     |
//+------------------------------------------------------------------+
void CBoxAnalyzer::EvaluateBoxQuality()
{
   if(m_box_type == BOX_NONE)
   {
      m_box_quality = 0;
      return;
   }
   
   // 基于多个因素评估质量
   
   // 1. 触点数量 - 触点越多，质量越高
   double touch_quality = MathMin(m_box_touch_count / 10.0, 1.0);
   
   // 2. 高度比例 - 箱体高度适中，质量越高
   double height_quality = 0;
   if(m_box_height_ratio > 0.01 && m_box_height_ratio < 0.2)
   {
      // 高度比例在1%到20%之间是理想的
      height_quality = 1.0 - MathAbs(0.1 - m_box_height_ratio) / 0.1;
      height_quality = MathMax(height_quality, 0);
   }
   
   // 3. 平行度 - 上下边界越平行，质量越高
   double parallel_quality = 1.0 - MathMin(MathAbs(m_box_upper_slope - m_box_lower_slope) * 1000.0, 1.0);
   
   // 4. 价格分布 - 价格在箱体内分布越均匀，质量越高
   double distribution_quality = 0;
   
   int in_box_count = 0;
   for(int i = 0; i < ArraySize(m_box_high_prices); i++)
   {
      double mid_price = (m_box_high_prices[i] + m_box_low_prices[i]) / 2.0;
      if(mid_price >= m_box_lower_intercept && mid_price <= m_box_upper_intercept)
         in_box_count++;
   }
   
   distribution_quality = (double)in_box_count / ArraySize(m_box_high_prices);
   
   // 综合评分 - 调整权重，更重视触点数量和分布
   m_box_quality = (touch_quality * 0.4 + height_quality * 0.2 + parallel_quality * 0.1 + distribution_quality * 0.3);
}

//+------------------------------------------------------------------+
//| 分析箱体                                                         |
//+------------------------------------------------------------------+
bool CBoxAnalyzer::Analyze()
{
   // 复制价格数据
   ArrayResize(m_box_high_prices, m_box_period);
   ArrayResize(m_box_low_prices, m_box_period);
   ArraySetAsSeries(m_box_high_prices, true);
   ArraySetAsSeries(m_box_low_prices, true);
   
   int copied_high = CopyHigh(_Symbol, _Period, 0, m_box_period, m_box_high_prices);
   int copied_low = CopyLow(_Symbol, _Period, 0, m_box_period, m_box_low_prices);
   
   if(copied_high <= 0 || copied_low <= 0)
   {
      Print("复制价格数据失败，错误码: ", GetLastError());
      return false;
   }
   
   // 重置箱体类型
   m_box_type = BOX_NONE;
   
   // 使用改进的KDE方法寻找价格密集区域
   FindDensityRegions();
   
   // 评估箱体质量
   EvaluateBoxQuality();
   
   if(UseDebugMode && DebugLevel > 1 && m_box_type != BOX_NONE)
   {
      string type_str = "";
      switch(m_box_type)
      {
         case BOX_HORIZONTAL: type_str = "水平箱体"; break;
         case BOX_ASCENDING: type_str = "上升箱体"; break;
         case BOX_DESCENDING: type_str = "下降箱体"; break;
      }
      
      Print("箱体分析: 类型=", type_str, 
            ", 质量=", m_box_quality, 
            ", 高度比例=", m_box_height_ratio, 
            ", 触点数量=", m_box_touch_count);
   }
   
   // 要求至少有3个触点，与Python一致
   return m_box_type != BOX_NONE && m_box_quality > 0.6 && m_box_touch_count >= 3;
}

//+------------------------------------------------------------------+
//| 获取上边界值                                                     |
//+------------------------------------------------------------------+
double CBoxAnalyzer::GetUpperValue(int shift)
{
   return m_box_upper_slope * shift + m_box_upper_intercept;
}

//+------------------------------------------------------------------+
//| 获取下边界值                                                     |
//+------------------------------------------------------------------+
double CBoxAnalyzer::GetLowerValue(int shift)
{
   return m_box_lower_slope * shift + m_box_lower_intercept;
}

//+------------------------------------------------------------------+
//| 检查价格是否突破箱体                                             |
//+------------------------------------------------------------------+
bool CBoxAnalyzer::IsBreakout(double price, ENUM_BOX_DIRECTION &direction)
{
   if(m_box_type == BOX_NONE)
   {
      direction = BOX_NEUTRAL;
      return false;
   }
   
   double upper_value = GetUpperValue(0);
   double lower_value = GetLowerValue(0);
   
   // 计算突破阈值
   double upper_threshold = upper_value * (1 + m_box_breakout_threshold);
   double lower_threshold = lower_value * (1 - m_box_breakout_threshold);
   
   // 检查是否突破上边界
   if(price > upper_threshold)
   {
      direction = BOX_UP;
      return true;
   }
   
   // 检查是否突破下边界
   if(price < lower_threshold)
   {
      direction = BOX_DOWN;
      return true;
   }
   
   direction = BOX_NEUTRAL;
   return false;
}

//+------------------------------------------------------------------+
//| 获取交易信号方向                                                 |
//+------------------------------------------------------------------+
ENUM_BOX_DIRECTION CBoxAnalyzer::GetSignalDirection(double price)
{
   // 如果箱体质量不够或者没有形成箱体，返回中性信号
   if(m_box_quality < 0.6 || m_box_type == BOX_NONE || m_box_touch_count < 3)
      return BOX_NEUTRAL;
      
   // 检查是否突破箱体
   ENUM_BOX_DIRECTION breakout_direction = BOX_NEUTRAL;
   bool is_breakout = IsBreakout(price, breakout_direction);
   
   if(is_breakout)
      return breakout_direction;
      
   // 如果没有突破，根据价格在箱体中的位置给出信号
   double upper_value = GetUpperValue(0);
   double lower_value = GetLowerValue(0);
   
   // 计算价格在箱体中的相对位置
   double relative_position = 0;
   if(MathAbs(upper_value - lower_value) > 0.0000001)
   {
      relative_position = (price - lower_value) / (upper_value - lower_value);
   }
   
   // 根据箱体类型和价格位置给出信号
   switch(m_box_type)
   {
      case BOX_HORIZONTAL:
         // 水平箱体 - 接近上边界卖出，接近下边界买入
         if(relative_position > 0.8)
            return BOX_DOWN;  // 接近上边界，空头信号
         else if(relative_position < 0.2)
            return BOX_UP;    // 接近下边界，多头信号
         break;
         
      case BOX_ASCENDING:
         // 上升箱体 - 偏向多头
         if(relative_position < 0.3)
            return BOX_UP;    // 接近下边界，多头信号
         break;
         
      case BOX_DESCENDING:
         // 下降箱体 - 偏向空头
         if(relative_position > 0.7)
            return BOX_DOWN;  // 接近上边界，空头信号
         break;
   }
   
   return BOX_NEUTRAL;  // 在箱体中间，无明确信号
}

// 箱体分析参数
input group "箱体分析参数"
input bool     UseBoxPattern = true;       // 使用箱体形态分析
input int      BoxPeriod = 60;             // 箱体分析周期
input double   BoxQualityThreshold = 0.6;  // 箱体质量阈值
input double   BoxBreakoutThreshold = 0.03; // 箱体突破阈值(%)

// 全局箱体分析对象
CBoxAnalyzer g_box_analyzer;

//+------------------------------------------------------------------+
//| 分析箱体并返回信号方向                                           |
//+------------------------------------------------------------------+
ENUM_BOX_DIRECTION AnalyzeBoxSignal()
{
   // 设置参数
   g_box_analyzer.SetParameters(BoxPeriod, BoxBreakoutThreshold);
   
   // 分析箱体
   bool box_valid = g_box_analyzer.Analyze();
   
   if(!box_valid || g_box_analyzer.GetQuality() < BoxQualityThreshold)
      return BOX_NEUTRAL;
   
   // 获取当前价格
   double current_price = SymbolInfoDouble(_Symbol, SYMBOL_LAST);
   
   // 获取交易信号方向
   ENUM_BOX_DIRECTION signal = g_box_analyzer.GetSignalDirection(current_price);
   
   if(UseDebugMode && DebugLevel > 0)
   {
      string type_str = "";
      switch(g_box_analyzer.GetType())
      {
         case BOX_HORIZONTAL: type_str = "水平箱体"; break;
         case BOX_ASCENDING: type_str = "上升箱体"; break;
         case BOX_DESCENDING: type_str = "下降箱体"; break;
      }
      
      string signal_str = "中性";
      if(signal == BOX_UP) signal_str = "多头";
      else if(signal == BOX_DOWN) signal_str = "空头";
      
      Print("箱体信号: ", signal_str, 
            ", 类型=", type_str, 
            ", 质量=", g_box_analyzer.GetQuality(),
            ", 触点数量=", g_box_analyzer.GetTouchCount(),
            ", 价格=", current_price);
   }
   
   return signal;
}

// 箱体理论逻辑完结
// 调用 AnalyzeBoxSignal() 函数，就能直接获取箱体分析的交易信号方向（多头/空头/震荡）

// 以下是K线组合形态逻辑
//+------------------------------------------------------------------+
//| K线形态分析类                                                    |
//+------------------------------------------------------------------+
class CCandlePatternAnalyzer
{
private:
   double            m_candle_open[];             // 开盘价数据
   double            m_candle_high[];             // 最高价数据
   double            m_candle_low[];              // 最低价数据
   double            m_candle_close[];            // 收盘价数据
   int               m_candle_period;             // 分析周期
   double            m_candle_body_ratio;         // 实体与整体的比例阈值
   double            m_candle_doji_ratio;         // 十字星实体比例阈值
   double            m_candle_shadow_ratio;       // 影线比例阈值
   double            m_candle_engulfing_ratio;    // 吞没形态比例阈值
   
   // 内部方法
   double CalculateCandleBodySize(int index);
   double CalculateCandleUpperShadow(int index);
   double CalculateCandleLowerShadow(int index);
   double CalculateCandleTotalSize(int index);
   bool IsBullishCandle(int index);
   bool IsBearishCandle(int index);
   
public:
   // 构造函数
   CCandlePatternAnalyzer() 
   { 
      m_candle_period = 20; 
      m_candle_body_ratio = 0.6;
      m_candle_doji_ratio = 0.05;
      m_candle_shadow_ratio = 0.1;
      m_candle_engulfing_ratio = 1.2;
   }
   
   // 设置参数
   void SetParameters(int period, double body_ratio, double doji_ratio, double shadow_ratio, double engulfing_ratio)
   {
      m_candle_period = period;
      m_candle_body_ratio = body_ratio;
      m_candle_doji_ratio = doji_ratio;
      m_candle_shadow_ratio = shadow_ratio;
      m_candle_engulfing_ratio = engulfing_ratio;
   }
   
   // 更新数据
   bool Update();
   
   // 检测K线形态
   ENUM_CANDLE_PATTERN DetectPattern(int start_pos = 0);
   
   // 检测大阳线
   bool IsBullishMarubozu(int index);
   
   // 检测大阴线
   bool IsBearishMarubozu(int index);
   
   // 检测锤子线
   bool IsHammer(int index);
   
   // 检测上吊线
   bool IsHangingMan(int index);
   
   // 检测倒锤子线
   bool IsInvertedHammer(int index);
   
   // 检测流星线
   bool IsShootingStar(int index);
   
   // 检测十字星
   bool IsDoji(int index);
   
   // 检测多头吞没形态
   bool IsBullishEngulfing(int index);
   
   // 检测空头吞没形态
   bool IsBearishEngulfing(int index);
   
   // 检测晨星形态
   bool IsMorningStar(int index);
   
   // 检测暮星形态
   bool IsEveningStar(int index);
   
   // 检测多头孕线
   bool IsHaramiBullish(int index);
   
   // 检测空头孕线
   bool IsHaramiBearish(int index);
   
   // 获取形态名称
   string GetPatternName(ENUM_CANDLE_PATTERN pattern);
   
   // 获取形态方向
   ENUM_CANDLE_DIRECTION GetPatternDirection(ENUM_CANDLE_PATTERN pattern);
};

//+------------------------------------------------------------------+
//| 计算K线实体大小                                                  |
//+------------------------------------------------------------------+
double CCandlePatternAnalyzer::CalculateCandleBodySize(int index)
{
   return MathAbs(m_candle_close[index] - m_candle_open[index]);
}

//+------------------------------------------------------------------+
//| 计算K线上影线长度                                                |
//+------------------------------------------------------------------+
double CCandlePatternAnalyzer::CalculateCandleUpperShadow(int index)
{
   if(IsBullishCandle(index))
      return m_candle_high[index] - m_candle_close[index];
   else
      return m_candle_high[index] - m_candle_open[index];
}

//+------------------------------------------------------------------+
//| 计算K线下影线长度                                                |
//+------------------------------------------------------------------+
double CCandlePatternAnalyzer::CalculateCandleLowerShadow(int index)
{
   if(IsBullishCandle(index))
      return m_candle_open[index] - m_candle_low[index];
   else
      return m_candle_close[index] - m_candle_low[index];
}

//+------------------------------------------------------------------+
//| 计算K线总长度                                                    |
//+------------------------------------------------------------------+
double CCandlePatternAnalyzer::CalculateCandleTotalSize(int index)
{
   return m_candle_high[index] - m_candle_low[index];
}

//+------------------------------------------------------------------+
//| 判断是否为阳线                                                   |
//+------------------------------------------------------------------+
bool CCandlePatternAnalyzer::IsBullishCandle(int index)
{
   return m_candle_close[index] > m_candle_open[index];
}

//+------------------------------------------------------------------+
//| 判断是否为阴线                                                   |
//+------------------------------------------------------------------+
bool CCandlePatternAnalyzer::IsBearishCandle(int index)
{
   return m_candle_close[index] < m_candle_open[index];
}

//+------------------------------------------------------------------+
//| 更新数据                                                         |
//+------------------------------------------------------------------+
bool CCandlePatternAnalyzer::Update()
{
   // 复制价格数据
   ArrayResize(m_candle_open, m_candle_period);
   ArrayResize(m_candle_high, m_candle_period);
   ArrayResize(m_candle_low, m_candle_period);
   ArrayResize(m_candle_close, m_candle_period);
   
   ArraySetAsSeries(m_candle_open, true);
   ArraySetAsSeries(m_candle_high, true);
   ArraySetAsSeries(m_candle_low, true);
   ArraySetAsSeries(m_candle_close, true);
   
   int copied_open = CopyOpen(_Symbol, _Period, 0, m_candle_period, m_candle_open);
   int copied_high = CopyHigh(_Symbol, _Period, 0, m_candle_period, m_candle_high);
   int copied_low = CopyLow(_Symbol, _Period, 0, m_candle_period, m_candle_low);
   int copied_close = CopyClose(_Symbol, _Period, 0, m_candle_period, m_candle_close);
   
   if(copied_open <= 0 || copied_high <= 0 || copied_low <= 0 || copied_close <= 0)
   {
      Print("复制价格数据失败，错误码: ", GetLastError());
      return false;
   }
   
   return true;
}

//+------------------------------------------------------------------+
//| 检测大阳线                                                       |
//+------------------------------------------------------------------+
bool CCandlePatternAnalyzer::IsBullishMarubozu(int index)
{
   if(!IsBullishCandle(index))
      return false;
      
   double body_size = CalculateCandleBodySize(index);
   double total_size = CalculateCandleTotalSize(index);
   
   // 添加除零保护
   if(MathAbs(total_size) < 0.0000001)
   {
      return false;
   }
   
   double upper_shadow = CalculateCandleUpperShadow(index);
   double lower_shadow = CalculateCandleLowerShadow(index);
   
   // 实体占总长度的比例大于阈值，且上下影线很小
   return (body_size / total_size > m_candle_body_ratio) && 
          (upper_shadow / total_size < m_candle_shadow_ratio) && 
          (lower_shadow / total_size < m_candle_shadow_ratio);
}

//+------------------------------------------------------------------+
//| 检测大阴线                                                       |
//+------------------------------------------------------------------+
bool CCandlePatternAnalyzer::IsBearishMarubozu(int index)
{
   if(!IsBearishCandle(index))
      return false;
      
   double body_size = CalculateCandleBodySize(index);
   double total_size = CalculateCandleTotalSize(index);
   
   // 添加除零保护
   if(MathAbs(total_size) < 0.0000001)
   {
      return false;
   }
   
   double upper_shadow = CalculateCandleUpperShadow(index);
   double lower_shadow = CalculateCandleLowerShadow(index);
   
   // 实体占总长度的比例大于阈值，且上下影线很小
   return (body_size / total_size > m_candle_body_ratio) && 
          (upper_shadow / total_size < m_candle_shadow_ratio) && 
          (lower_shadow / total_size < m_candle_shadow_ratio);
}

//+------------------------------------------------------------------+
//| 检测锤子线                                                       |
//+------------------------------------------------------------------+
bool CCandlePatternAnalyzer::IsHammer(int index)
{
   double body_size = CalculateCandleBodySize(index);
   double total_size = CalculateCandleTotalSize(index);
   
   // 添加除零保护
   if(MathAbs(total_size) < 0.0000001 || MathAbs(body_size) < 0.0000001)
   {
      return false;
   }
   
   double upper_shadow = CalculateCandleUpperShadow(index);
   double lower_shadow = CalculateCandleLowerShadow(index);
   
   // 下影线长度至少是实体的2倍，且上影线很短
   return (lower_shadow > 2.0 * body_size) && 
          (upper_shadow / total_size < m_candle_shadow_ratio) && 
          (body_size / total_size < 0.3);
}

//+------------------------------------------------------------------+
//| 检测上吊线                                                       |
//+------------------------------------------------------------------+
bool CCandlePatternAnalyzer::IsHangingMan(int index)
{
   // 上吊线形态与锤子线相似，但出现在上升趋势顶部
   // 这里简单判断前面是否有上升趋势
   if(index >= 5 && m_candle_close[index+5] < m_candle_close[index+1])
      return IsHammer(index);
      
   return false;
}

//+------------------------------------------------------------------+
//| 检测倒锤子线                                                     |
//+------------------------------------------------------------------+
bool CCandlePatternAnalyzer::IsInvertedHammer(int index)
{
   double body_size = CalculateCandleBodySize(index);
   double total_size = CalculateCandleTotalSize(index);
   
   // 添加除零保护
   if(MathAbs(total_size) < 0.0000001 || MathAbs(body_size) < 0.0000001)
   {
      return false;
   }
   
   double upper_shadow = CalculateCandleUpperShadow(index);
   double lower_shadow = CalculateCandleLowerShadow(index);
   
   // 上影线长度至少是实体的2倍，且下影线很短
   return (upper_shadow > 2.0 * body_size) && 
          (lower_shadow / total_size < m_candle_shadow_ratio) && 
          (body_size / total_size < 0.3);
}

//+------------------------------------------------------------------+
//| 检测流星线                                                       |
//+------------------------------------------------------------------+
bool CCandlePatternAnalyzer::IsShootingStar(int index)
{
   // 流星线形态与倒锤子线相似，但出现在上升趋势顶部
   // 这里简单判断前面是否有上升趋势
   if(index >= 5 && m_candle_close[index+5] < m_candle_close[index+1])
      return IsInvertedHammer(index);
      
   return false;
}

//+------------------------------------------------------------------+
//| 检测十字星                                                       |
//+------------------------------------------------------------------+
bool CCandlePatternAnalyzer::IsDoji(int index)
{
   double body_size = CalculateCandleBodySize(index);
   double total_size = CalculateCandleTotalSize(index);
   
   // 添加除零保护
   if(MathAbs(total_size) < 0.0000001)
   {
      return false;
   }
   
   // 实体非常小
   return (body_size / total_size < m_candle_doji_ratio);
}

//+------------------------------------------------------------------+
//| 检测多头吞没形态                                                 |
//+------------------------------------------------------------------+
bool CCandlePatternAnalyzer::IsBullishEngulfing(int index)
{
   if(index < 1)
      return false;
      
   // 前一根是阴线，当前是阳线
   if(!IsBearishCandle(index+1) || !IsBullishCandle(index))
      return false;
      
   // 当前K线的实体完全吞没前一根K线的实体
   return (m_candle_open[index] < m_candle_close[index+1]) && 
          (m_candle_close[index] > m_candle_open[index+1]) && 
          (CalculateCandleBodySize(index) > m_candle_engulfing_ratio * CalculateCandleBodySize(index+1));
}

//+------------------------------------------------------------------+
//| 检测空头吞没形态                                                 |
//+------------------------------------------------------------------+
bool CCandlePatternAnalyzer::IsBearishEngulfing(int index)
{
   if(index < 1)
      return false;
      
   // 前一根是阳线，当前是阴线
   if(!IsBullishCandle(index+1) || !IsBearishCandle(index))
      return false;
      
   // 当前K线的实体完全吞没前一根K线的实体
   return (m_candle_open[index] > m_candle_close[index+1]) && 
          (m_candle_close[index] < m_candle_open[index+1]) && 
          (CalculateCandleBodySize(index) > m_candle_engulfing_ratio * CalculateCandleBodySize(index+1));
}

//+------------------------------------------------------------------+
//| 检测晨星形态                                                     |
//+------------------------------------------------------------------+
bool CCandlePatternAnalyzer::IsMorningStar(int index)
{
   if(index < 2)
      return false;
      
   // 第一根是大阴线
   double body_size1 = CalculateCandleBodySize(index+2);
   double total_size1 = CalculateCandleTotalSize(index+2);
   
   // 添加除零保护
   if(MathAbs(total_size1) < 0.0000001)
   {
      return false;
   }
   
   if(!IsBearishCandle(index+2) || body_size1 / total_size1 < 0.5)
      return false;
      
   // 第二根是小实体（可能是十字星）
   double body_size2 = CalculateCandleBodySize(index+1);
   double total_size2 = CalculateCandleTotalSize(index+1);
   
   // 添加除零保护
   if(MathAbs(total_size2) < 0.0000001)
   {
      return false;
   }
   
   if(body_size2 / total_size2 > 0.3)
      return false;
      
   // 第三根是大阳线
   double body_size3 = CalculateCandleBodySize(index);
   double total_size3 = CalculateCandleTotalSize(index);
   
   // 添加除零保护
   if(MathAbs(total_size3) < 0.0000001)
   {
      return false;
   }
   
   if(!IsBullishCandle(index) || body_size3 / total_size3 < 0.5)
      return false;
      
   // 第二根的收盘价低于第一根和第三根
   return (m_candle_close[index+1] < m_candle_close[index+2]) && (m_candle_close[index+1] < m_candle_open[index]);
}

//+------------------------------------------------------------------+
//| 检测暮星形态                                                     |
//+------------------------------------------------------------------+
bool CCandlePatternAnalyzer::IsEveningStar(int index)
{
   if(index < 2)
      return false;
      
   // 第一根是大阳线
   double body_size1 = CalculateCandleBodySize(index+2);
   double total_size1 = CalculateCandleTotalSize(index+2);
   
   // 添加除零保护
   if(MathAbs(total_size1) < 0.0000001)
   {
      return false;
   }
   
   if(!IsBullishCandle(index+2) || body_size1 / total_size1 < 0.5)
      return false;
      
   // 第二根是小实体（可能是十字星）
   double body_size2 = CalculateCandleBodySize(index+1);
   double total_size2 = CalculateCandleTotalSize(index+1);
   
   // 添加除零保护
   if(MathAbs(total_size2) < 0.0000001)
   {
      return false;
   }
   
   if(body_size2 / total_size2 > 0.3)
      return false;
      
   // 第三根是大阴线
   double body_size3 = CalculateCandleBodySize(index);
   double total_size3 = CalculateCandleTotalSize(index);
   
   // 添加除零保护
   if(MathAbs(total_size3) < 0.0000001)
   {
      return false;
   }
   
   if(!IsBearishCandle(index) || body_size3 / total_size3 < 0.5)
      return false;
      
   // 第二根的收盘价高于第一根和第三根
   return (m_candle_close[index+1] > m_candle_close[index+2]) && (m_candle_close[index+1] > m_candle_open[index]);
}

//+------------------------------------------------------------------+
//| 检测多头孕线                                                     |
//+------------------------------------------------------------------+
bool CCandlePatternAnalyzer::IsHaramiBullish(int index)
{
   if(index < 1)
      return false;
      
   // 前一根是大阴线，当前是阳线
   if(!IsBearishCandle(index+1) || !IsBullishCandle(index))
      return false;
      
   // 当前K线的实体被前一根K线的实体完全包含
   return (m_candle_open[index] > m_candle_close[index+1]) && 
          (m_candle_close[index] < m_candle_open[index+1]) && 
          (CalculateCandleBodySize(index) < 0.8 * CalculateCandleBodySize(index+1));
}

//+------------------------------------------------------------------+
//| 检测空头孕线                                                     |
//+------------------------------------------------------------------+
bool CCandlePatternAnalyzer::IsHaramiBearish(int index)
{
   if(index < 1)
      return false;
      
   // 前一根是大阳线，当前是阴线
   if(!IsBullishCandle(index+1) || !IsBearishCandle(index))
      return false;
      
   // 当前K线的实体被前一根K线的实体完全包含
   return (m_candle_open[index] < m_candle_close[index+1]) && 
          (m_candle_close[index] > m_candle_open[index+1]) && 
          (CalculateCandleBodySize(index) < 0.8 * CalculateCandleBodySize(index+1));
}

//+------------------------------------------------------------------+
//| 检测K线形态                                                      |
//+------------------------------------------------------------------+
ENUM_CANDLE_PATTERN CCandlePatternAnalyzer::DetectPattern(int start_pos)
{
   // 检测各种K线形态
   if(IsBullishMarubozu(start_pos))
      return PATTERN_BULLISH_MARUBOZU;
      
   if(IsBearishMarubozu(start_pos))
      return PATTERN_BEARISH_MARUBOZU;
      
   if(IsHammer(start_pos))
      return PATTERN_HAMMER;
      
   if(IsHangingMan(start_pos))
      return PATTERN_HANGING_MAN;
      
   if(IsInvertedHammer(start_pos))
      return PATTERN_INVERTED_HAMMER;
      
   if(IsShootingStar(start_pos))
      return PATTERN_SHOOTING_STAR;
      
   if(IsDoji(start_pos))
      return PATTERN_DOJI;
      
   if(IsBullishEngulfing(start_pos))
      return PATTERN_BULLISH_ENGULFING;
      
   if(IsBearishEngulfing(start_pos))
      return PATTERN_BEARISH_ENGULFING;
      
   if(IsMorningStar(start_pos))
      return PATTERN_MORNING_STAR;
      
   if(IsEveningStar(start_pos))
      return PATTERN_EVENING_STAR;
      
   if(IsHaramiBullish(start_pos))
      return PATTERN_HARAMI_BULLISH;
      
   if(IsHaramiBearish(start_pos))
      return PATTERN_HARAMI_BEARISH;
      
   return PATTERN_NONE;
}

//+------------------------------------------------------------------+
//| 获取形态名称                                                     |
//+------------------------------------------------------------------+
string CCandlePatternAnalyzer::GetPatternName(ENUM_CANDLE_PATTERN pattern)
{
   switch(pattern)
   {
      case PATTERN_BULLISH_MARUBOZU: return "多头光头光脚阳线";
      case PATTERN_BEARISH_MARUBOZU: return "空头光头光脚阴线";
      case PATTERN_HAMMER: return "锤子线";
      case PATTERN_HANGING_MAN: return "上吊线";
      case PATTERN_INVERTED_HAMMER: return "倒锤子线";
      case PATTERN_SHOOTING_STAR: return "流星线";
      case PATTERN_DOJI: return "十字星";
      case PATTERN_BULLISH_ENGULFING: return "多头吞没形态";
      case PATTERN_BEARISH_ENGULFING: return "空头吞没形态";
      case PATTERN_MORNING_STAR: return "晨星形态";
      case PATTERN_EVENING_STAR: return "暮星形态";
      case PATTERN_HARAMI_BULLISH: return "多头孕线";
      case PATTERN_HARAMI_BEARISH: return "空头孕线";
      default: return "无形态";
   }
}

//+------------------------------------------------------------------+
//| 获取形态方向                                                     |
//+------------------------------------------------------------------+
ENUM_CANDLE_DIRECTION CCandlePatternAnalyzer::GetPatternDirection(ENUM_CANDLE_PATTERN pattern)
{
   switch(pattern)
   {
      case PATTERN_BULLISH_MARUBOZU:
      case PATTERN_HAMMER:
      case PATTERN_INVERTED_HAMMER:
      case PATTERN_BULLISH_ENGULFING:
      case PATTERN_MORNING_STAR:
      case PATTERN_HARAMI_BULLISH:
         return CANDLE_UP;
         
      case PATTERN_BEARISH_MARUBOZU:
      case PATTERN_HANGING_MAN:
      case PATTERN_SHOOTING_STAR:
      case PATTERN_BEARISH_ENGULFING:
      case PATTERN_EVENING_STAR:
      case PATTERN_HARAMI_BEARISH:
         return CANDLE_DOWN;
         
      default:
         return CANDLE_NEUTRAL;
   }
}

// K线形态分析参数
input group "K线形态分析参数"
input bool     UseCandlePatterns = true;    // 使用K线形态分析
input int      CandlePeriod = 5;           // K线形态分析周期
input double   CandleBodyRatio = 0.6;       // 实体比例阈值
input double   CandleDojiRatio = 0.05;      // 十字星比例阈值
input double   CandleShadowRatio = 0.1;     // 影线比例阈值
input double   CandleEngulfingRatio = 1.2;  // 吞没形态比例阈值

// 全局K线形态分析对象
CCandlePatternAnalyzer g_candle_analyzer;

//+------------------------------------------------------------------+
//| 分析K线形态并返回信号方向                                        |
//+------------------------------------------------------------------+
ENUM_CANDLE_DIRECTION AnalyzeCandlePatternSignal()
{
   // 如果不使用K线形态分析，返回中性信号
   if(!UseCandlePatterns)
      return CANDLE_NEUTRAL;
      
   // 设置参数
   g_candle_analyzer.SetParameters(CandlePeriod, CandleBodyRatio, CandleDojiRatio, CandleShadowRatio, CandleEngulfingRatio);
   
   // 更新数据
   if(!g_candle_analyzer.Update())
      return CANDLE_NEUTRAL;
      
   // 检测当前K线形态
   ENUM_CANDLE_PATTERN current_pattern = g_candle_analyzer.DetectPattern(0);
   
   if(current_pattern == PATTERN_NONE)
      return CANDLE_NEUTRAL;
   
   // 获取形态方向
   ENUM_CANDLE_DIRECTION direction = g_candle_analyzer.GetPatternDirection(current_pattern);
   
   if(UseDebugMode && DebugLevel > 0)
   {
      string pattern_name = g_candle_analyzer.GetPatternName(current_pattern);
      string direction_str = "";
      
      switch(direction)
      {
         case CANDLE_UP: direction_str = "多头"; break;
         case CANDLE_DOWN: direction_str = "空头"; break;
         default: direction_str = "中性"; break;
      }
      
      Print("K线形态信号: ", pattern_name, ", 方向: ", direction_str);
   }
   
   return direction;
}

// K线形态逻辑完结
// 调用 AnalyzeCandlePatternSignal() 函数，就能直接获取K线形态分析的交易信号方向（多头/空头/震荡）