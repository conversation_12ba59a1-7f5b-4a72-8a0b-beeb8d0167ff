"""
MT5交易平台统一配置模块
集中管理所有MT5相关的配置，确保各组件之间的一致性
"""
import MetaTrader5 as mt5

# MT5终端路径配置
MT5_PATH = r"C:\Program Files\MetaTrader 5 EXNESS1\terminal64.exe"

# 支持的时间周期
TIMEFRAMES = [
    # mt5.TIMEFRAME_D1,
    # mt5.TIMEFRAME_H4,
    # mt5.TIMEFRAME_H1,
    mt5.TIMEFRAME_M15,
    # mt5.TIMEFRAME_M5,
]

# 时间周期优先级（从高到低）
TIMEFRAME_PRIORITY = [
    mt5.TIMEFRAME_D1,
    mt5.TIMEFRAME_H4,
    mt5.TIMEFRAME_H1,
    mt5.TIMEFRAME_M15,
    mt5.TIMEFRAME_M5,
]

# 时间周期注释映射（从注释提取时间周期）
TIMEFRAME_COMMENT_MAP = {
    "1m":1,
    "5m":5,
    "15":15,
    "1h":16385,
    "4h":16388,
    "6h":16390,
    "12h":16396,
    "1d":16408,
    "1w":32796,
}

TIMEFRAME_CHECK_MAP = {
    "1m": ["1m"],          # 1分钟周期的注释中应包含"1m"
    "5m": ["5m"],          # 5分钟周期的注释中应包含"5m"
    "15": ["15"],          # 15分钟周期的注释中应包含"15"
    "1h": ["1h"],          # 1小时周期的注释中应包含"1h"
    "4h": ["4h"],           # 4小时周期的注释中应包含"4h"
    "1d": ["1d"],
    "1w": ["1w"],
}


# 时间周期映射（用于分析器和交易器之间的转换）
TIMEFRAME_MAP = {
    1: mt5.TIMEFRAME_M1,     # M1
    5: mt5.TIMEFRAME_M5,     # M5
    15: mt5.TIMEFRAME_M15,     # M15
    16385: mt5.TIMEFRAME_H1,   # H1
    16388: mt5.TIMEFRAME_H4,   # H4
    16390: mt5.TIMEFRAME_H6,   # H6
    16396: mt5.TIMEFRAME_H12,  #H12
    16408: mt5.TIMEFRAME_D1,   # D1
    32769: mt5.TIMEFRAME_W1,   # W1 
}

# 时间周期字符串表示
TIMEFRAME_STRINGS = {
    mt5.TIMEFRAME_M1: "1m",
    mt5.TIMEFRAME_M5: "5m",
    mt5.TIMEFRAME_M15: "15",
    mt5.TIMEFRAME_H1: "1h",
    mt5.TIMEFRAME_H4: "4h",
    mt5.TIMEFRAME_D1: "1d",
    mt5.TIMEFRAME_W1: "1w",
}


# 交易品种
SYMBOLS = {
    # 默认交易品种（用于交易）
    "default": ["XAUUSDc", "BTCUSDc","EURUSDc","GBPUSDc","USDJPYc"],
    
    # 分析模式品种（用于市场分析）
    "analysis": [
        "EURUSDc", "GBPUSDc", "USDJPYc", "XAUUSDc",
        "USDCADc", "XAGUSDc", "USTECc", "BTCUSDc"
    ]
}

# 品种类别定义
SYMBOL_TYPES = {
    "forex": ["XAUUSDc", "EURUSDc", "GBPUSDc", "USDJPYc", "USDCADc", "XAGUSDc", "USTECc"],  # 外汇和贵金属以及股指期货
    "crypto": ["BTCUSDc", "DOGEUSDc"]   # 加密货币
}

# 交易配置
TRADING_CONFIG = {
    # 最大持仓数量（每个品种每个周期）
    "max_positions": 3,  # 修改为很大的数值，实际上不限制手数
    
    # ATR参数
    "atr_period": 14,
    
    # 风险控制参数
    "risk_control": {
        "enabled": True,                # 是否启用风险控制
        "dynamic_sizing": False,        # 禁用动态仓位计算，使用固定手数
        "use_equity": False,            # 是否使用净值代替结余计算仓位
        "base_balance": None,           # 设置为None，系统会自动使用首次运行时的账户余额作为基准
        "adjustment_power": 0.5,        # 调整幅度（0.5表示平方根关系，更保守）
    },
    
    # 品种和周期特定配置
    "symbols": {
        "XAUUSDc": {
            "pip_value": None,  # 将从MT5自动获取
            "pip_size": 1.0,
            "min_lot": 0.01,
            "lot_step": 0.01,
            "timeframes": {
                mt5.TIMEFRAME_H4: {
                    "lot_size": 0.12,   # 固定手数，不再使用序列
                    "risk_factor": 1    # 风险因子
                },
                mt5.TIMEFRAME_H1: {
                    "lot_size": 0.09,   # 固定手数
                    "risk_factor": 1    # 风险因子
                },
                mt5.TIMEFRAME_M15: {
                    "lot_size": 0.06,   # 固定手数
                    "risk_factor": 1    # 风险因子
                },
                mt5.TIMEFRAME_M5: {
                    "lot_size": 0.03,   # 固定手数
                    "risk_factor": 1    # 风险因子
                }
            }
        },
        "BTCUSDc": {
            "pip_value": None,  # 将从MT5自动获取
            "pip_size": 1.0,
            "min_lot": 0.01,
            "lot_step": 0.01,
            "timeframes": {
                mt5.TIMEFRAME_H4: {
                    "lot_size": 0.12,   # 固定手数，不再使用序列
                    "risk_factor": 1    # 风险因子
                },
                mt5.TIMEFRAME_H1: {
                    "lot_size": 0.09,   # 固定手数
                    "risk_factor": 1    # 风险因子
                },
                mt5.TIMEFRAME_M15: {
                    "lot_size": 0.06,   # 固定手数
                    "risk_factor": 1    # 风险因子
                },
                mt5.TIMEFRAME_M5: {
                    "lot_size": 0.03,   # 固定手数
                    "risk_factor": 1    # 风险因子
                }
            }
        },
        "GBPUSDc": {
            "pip_value": None,  # 将从MT5自动获取
            "pip_size": 1.0,
            "min_lot": 0.01,
            "lot_step": 0.01,
            "timeframes": {
                mt5.TIMEFRAME_H4: {
                    "lot_size": 0.12,   # 固定手数，不再使用序列
                    "risk_factor": 1    # 风险因子
                },
                mt5.TIMEFRAME_H1: {
                    "lot_size": 0.09,   # 固定手数
                    "risk_factor": 1    # 风险因子
                },
                mt5.TIMEFRAME_M15: {
                    "lot_size": 0.06,   # 固定手数
                    "risk_factor": 1    # 风险因子
                },
                mt5.TIMEFRAME_M5: {
                    "lot_size": 0.03,   # 固定手数
                    "risk_factor": 1    # 风险因子
                }
            }
        },
        "EURUSDc": {
            "pip_value": None,  # 将从MT5自动获取
            "pip_size": 1.0,
            "min_lot": 0.01,
            "lot_step": 0.01,
            "timeframes": {
                mt5.TIMEFRAME_H4: {
                    "lot_size": 0.12,   # 固定手数，不再使用序列
                    "risk_factor": 1    # 风险因子
                },
                mt5.TIMEFRAME_H1: {
                    "lot_size": 0.09,   # 固定手数
                    "risk_factor": 1    # 风险因子
                },
                mt5.TIMEFRAME_M15: {
                    "lot_size": 0.06,   # 固定手数
                    "risk_factor": 1    # 风险因子
                },
                mt5.TIMEFRAME_M5: {
                    "lot_size": 0.03,   # 固定手数
                    "risk_factor": 1    # 风险因子
                }
            }
        },
        "USDJPYc": {
            "pip_value": None,  # 将从MT5自动获取
            "pip_size": 1.0,
            "min_lot": 0.01,
            "lot_step": 0.01,
            "timeframes": {
                mt5.TIMEFRAME_H4: {
                    "lot_size": 0.12,   # 固定手数，不再使用序列
                    "risk_factor": 1    # 风险因子
                },
                mt5.TIMEFRAME_H1: {
                    "lot_size": 0.09,   # 固定手数
                    "risk_factor": 1    # 风险因子
                },
                mt5.TIMEFRAME_M15: {
                    "lot_size": 0.06,   # 固定手数
                    "risk_factor": 1    # 风险因子
                },
                mt5.TIMEFRAME_M5: {
                    "lot_size": 0.03,   # 固定手数
                    "risk_factor": 1    # 风险因子
                }
            }
        }
    }
}

# 持仓管理配置
POSITION_MANAGEMENT_CONFIG = {
    "enabled": True,  # 是否启用持仓管理
    
    # 紧急平仓配置
    "emergency_close": {
        "enabled": True,
        "max_loss_percent": 2.0  # 最大允许亏损占账户余额的百分比
    },
    
    # 信号驱动的持仓管理
    "signal_based_management": {
        "enabled": True,  # 启用信号驱动的持仓管理
        "close_on_no_signal": True,  # 没有信号时平仓
        "close_on_reverse_signal": False,  # 反向信号时平仓
    }
}
# 交易时间配置
TRADING_HOURS = {
    "forex": {  # 外汇和贵金属
        "weekday": {
            "start": "07:00",
            "end": "04:00"  # 次日
        },
        "weekend": False
    },
    "crypto": {  # 加密货币
        "weekday": {
            "start": "00:00",
            "end": "23:59"
        },
        "weekend": True  # 理论上加密货币周末可交易，但实际取决于经纪商
    }
}

# 添加MT5错误代码映射
MT5_ERROR_CODES = {
    10004: "交易服务器繁忙",
    10006: "请求被拒绝",
    10007: "请求取消",
    10008: "订单已经执行",
    10009: "执行请求失败",
    10010: "只有部分请求被执行",
    10011: "请求处理错误",
    10012: "请求超时",
    10013: "无效请求",
    10014: "无效交易量",
    10015: "无效价格",
    10016: "无效止损",
    10017: "市场关闭",  # 这是您遇到的错误
    10018: "不足资金",
    10019: "价格改变",
    10020: "价格偏离",
    10021: "交易条件改变",
    10022: "交易太频繁",
    10023: "交易被禁止",
    10024: "交易被禁止",
    10025: "交易被禁止",
    10026: "交易被禁止",
    10027: "自动交易被禁止",
    10028: "交易被禁止",
    10029: "交易被禁止",
    10030: "交易被禁止",
    10031: "交易被禁止",
    10032: "交易被禁止",
}

def get_symbol_pip_value(symbol):
    """从MT5获取品种的pip值"""
    try:
        # 获取品种信息
        symbol_info = mt5.symbol_info(symbol)
        if symbol_info is None:
            print(f"获取{symbol}信息失败")
            return None
            
        # 获取点值
        point = symbol_info.point
        # 获取合约大小
        trade_contract_size = symbol_info.trade_contract_size
        
        # 获取货币对类型
        symbol_path = symbol_info.path
        
        # 根据不同品种类型调整计算方法
        if "Forex" in symbol_path or "Metals" in symbol_path:
            # 外汇和贵金属通常是10倍点值
            pip_value = trade_contract_size * point * 10
        elif "Crypto" in symbol_path:
            # 加密货币通常是1倍点值
            pip_value = trade_contract_size * point
        else:
            # 其他品种默认使用10倍点值
            pip_value = trade_contract_size * point * 10
        
        print(f"{symbol} 信息: 点值={point}, 合约大小={trade_contract_size}, 路径={symbol_path}, 计算pip值={pip_value}")
        
        return pip_value
    except Exception as e:
        print(f"获取{symbol} pip值出错: {str(e)}")
        return None

# 添加初始化函数
def initialize_symbol_info():
    """初始化品种信息，从MT5获取pip值等数据"""
    # 确保MT5已连接
    if not mt5.initialize(path=MT5_PATH):
        print(f"MT5初始化失败，路径: {MT5_PATH}")
        print(f"MT5错误代码: {mt5.last_error()}")
        return False
        
    for symbol in TRADING_CONFIG["symbols"]:
        # 如果pip_value为None，则从MT5获取
        if TRADING_CONFIG["symbols"][symbol]["pip_value"] is None:
            pip_value = get_symbol_pip_value(symbol)
            if pip_value:
                TRADING_CONFIG["symbols"][symbol]["pip_value"] = pip_value
                print(f"从MT5获取{symbol}的pip值: {pip_value}")
            else:
                # 设置默认值
                TRADING_CONFIG["symbols"][symbol]["pip_value"] = 100.0
                print(f"无法从MT5获取{symbol}的pip值，使用默认值: 100.0")
    return True

# 信号强度配置 - 根据周期自适应调整
SIGNAL_CONFIG = {
    'A': {  # 完美形态
        'initial_stop': 1.8,     # 从2.0降低到1.8
        'breakeven_ratio': 1.2,  # 从1.5降低到1.2
        'trailing_start': 2.0,   # 从2.5降低到2.0
        'trailing_distance': 1.2,  # 从1.5降低到1.2
        'take_profit': None,      # 从5.0降低到4.0
        'profit_lock': 2.5       # 从3.0降低到2.5
    },
    'B': {  # 突破信号
        'initial_stop': 1.5,     # 从1.8降低到1.5
        'breakeven_ratio': 1.0,  # 从1.2降低到1.0
        'trailing_start': 1.6,   # 从2.0降低到1.6
        'trailing_distance': 1.0, # 从1.2降低到1.0
        'take_profit': None,      # 从4.0降低到3.5
        'profit_lock': 2.0       # 从2.5降低到2.0
    },
    'C': {  # 形态突破确认
        'initial_stop': 1.4,     # 从1.6降低到1.4
        'breakeven_ratio': 0.8,  # 从1.0降低到0.8
        'trailing_start': 1.5,   # 从1.8降低到1.5
        'trailing_distance': 0.8,  # 从1.0降低到0.8
        'take_profit': None,      # 从4.0降低到3.5
        'profit_lock': 1.8       # 从2.0降低到1.8
    },
    'D': {  # 其他交易机会
        'initial_stop': 1.3,     # 从1.5降低到1.3
        'breakeven_ratio': 0.7,  # 从0.8降低到0.7
        'trailing_start': 1.3,   # 从1.5降低到1.3
        'trailing_distance': 0.7,  # 从0.8降低到0.7
        'take_profit': None,      # 从3.0降低到2.8
        'profit_lock': 1.5       # 从1.8降低到1.5
    },
    'default': {
        'initial_stop': 1.5,     # 从1.8降低到1.5
        'breakeven_ratio': 1.0,  # 从1.2降低到1.0
        'trailing_start': 1.6,   # 从2.0降低到1.6
        'trailing_distance': 1.0,  # 从1.2降低到1.0
        'take_profit': None,      # 从4.0降低到3.5
        'profit_lock': 2.0       # 从2.5降低到2.0
    }
}

# 添加周期自适应调整函数
def adjust_atr_multiplier_by_timeframe(base_value, timeframe):
    """根据时间周期自适应调整ATR乘数"""
    # 时间周期调整系数 - 较大周期使用较小的乘数
    timeframe_factors = {
        mt5.TIMEFRAME_M1: 1.2,    # 1分钟周期
        mt5.TIMEFRAME_M5: 1.1,    # 5分钟周期
        mt5.TIMEFRAME_M15: 1.0,   # 15分钟周期
        mt5.TIMEFRAME_H1: 0.9,    # 1小时周期
        mt5.TIMEFRAME_H4: 0.7,    # 4小时周期 - 降低到0.7
        mt5.TIMEFRAME_D1: 0.5,    # 日线周期 - 降低到0.5
        mt5.TIMEFRAME_W1: 0.4     # 周线周期 - 降低到0.4
    }
    
    # 获取调整系数，默认为1.0
    factor = timeframe_factors.get(timeframe, 1.0)
    
    # 返回调整后的值
    return base_value * factor

# 如果直接运行此文件，则初始化品种信息
if __name__ == "__main__":
    success = initialize_symbol_info()
    if success:
        print("MT5配置初始化成功")
        mt5.shutdown()
    else:
        print("MT5配置初始化失败，请检查MT5安装和配置")