import subprocess
import time
import os
import datetime
import logging
from logging.handlers import TimedRotatingFileHandler
import sys

# 修改日志设置函数
def setup_rotating_logger(name, log_file, level=logging.INFO):
    """设置按日期滚动的日志"""
    logger = logging.getLogger(name)
    logger.setLevel(level)
    
    # 检查是否已经有处理器，避免重复添加
    if not logger.handlers:
        # 创建日志目录
        log_dir = os.path.dirname(log_file)
        if not os.path.exists(log_dir):
            os.makedirs(log_dir)
        
        # 创建按天滚动的日志处理器
        handler = TimedRotatingFileHandler(
            log_file,
            when='midnight',  # 每天午夜滚动
            interval=1,       # 每1天滚动一次
            backupCount=30,   # 保留30天的日志
            encoding='utf-8'
        )
        
        # 自定义日志文件后缀格式为 .YYYY-MM-DD
        handler.suffix = "%Y-%m-%d"
        
        # 设置日志格式
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        handler.setFormatter(formatter)
        
        # 添加处理器到日志器
        logger.addHandler(handler)
        
        # 添加控制台输出
        console = logging.StreamHandler()
        console.setFormatter(formatter)
        logger.addHandler(console)
    
    return logger

# 设置日志
logger = setup_rotating_logger(
    'scheduler',
    os.path.join(os.path.dirname(os.path.abspath(__file__)), 'logs', 'scheduler.log'),
    level=logging.INFO
)

def run_trader():
    """执行交易脚本"""
    logger.info("开始执行交易脚本")
    
    try:
        # 获取当前目录
        current_dir = os.path.dirname(os.path.abspath(__file__))
        
        # 先检查是否有遗留的交易进程
        try:
            # 在Windows上查找可能的遗留进程
            check_process = subprocess.run(
                ['tasklist', '/FI', 'IMAGENAME eq python.exe', '/FO', 'CSV'],
                capture_output=True,
                text=True
            )
            
            # 检查输出中是否包含mt5_trader.py
            if 'mt5_trader.py' in check_process.stdout:
                logger.warning("发现遗留的交易进程，尝试终止...")
                subprocess.run(['taskkill', '/F', '/IM', 'python.exe'], capture_output=True)
                time.sleep(2)  # 等待进程终止
        except Exception as e:
            logger.error(f"检查遗留进程时出错: {e}")
        
        # 使用subprocess启动进程，使用-u参数确保无缓冲
        process = subprocess.Popen(
            ["python", "-u", os.path.join(current_dir, "platforms", "mt5", "mt5_trader.py")],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            bufsize=1
        )
        
        # 设置超时时间（10分钟）
        try:
            stdout, stderr = process.communicate(timeout=600)  # 改回600秒(10分钟)
            
            if stdout:
                for line in stdout.splitlines():
                    logger.info(f"输出: {line}")
            
            if stderr:
                for line in stderr.splitlines():
                    logger.error(f"错误: {line}")
            
            if process.returncode != 0:
                logger.error(f"交易脚本执行失败，返回码: {process.returncode}")
                # 交易脚本执行失败时检查Git更新
                check_and_update_git()
                return False
            
            logger.info("交易脚本执行完成")
            return True
            
        except subprocess.TimeoutExpired:
            logger.warning("交易脚本执行超时，强制终止")
            # 确保子进程也被终止
            try:
                process.kill()
                # 在Windows上，可能需要额外的步骤来确保所有相关进程都被终止
                subprocess.run(['taskkill', '/F', '/T', '/PID', str(process.pid)], capture_output=True)
            except Exception as e:
                logger.error(f"终止超时进程时出错: {e}")
            
            # 交易脚本超时时检查Git更新
            check_and_update_git()
            return False
        
    except Exception as e:
        logger.error(f"执行交易脚本时出错: {e}", exc_info=True)
        # 发生异常时检查Git更新
        check_and_update_git()
        return False


def check_git_updates():
    """检查Git仓库是否有更新，只要有新提交就返回True"""
    logger.info("检查Git仓库更新...")
    
    try:
        # 获取当前目录
        current_dir = os.path.dirname(os.path.abspath(__file__))
        
        # 执行git fetch
        fetch_process = subprocess.run(
            ["git", "fetch"], 
            cwd=current_dir,
            capture_output=True, 
            text=True
        )
        
        if fetch_process.returncode != 0:
            logger.error(f"Git fetch失败: {fetch_process.stderr}")
            return False
        
        # 检查是否有新提交
        diff_process = subprocess.run(
            ["git", "rev-list", "HEAD..origin/master", "--count"], 
            cwd=current_dir,
            capture_output=True, 
            text=True
        )
        
        if diff_process.returncode != 0:
            # 尝试使用main分支
            diff_process = subprocess.run(
                ["git", "rev-list", "HEAD..origin/main", "--count"], 
                cwd=current_dir,
                capture_output=True, 
                text=True
            )
            
            if diff_process.returncode != 0:
                logger.error(f"Git rev-list失败: {diff_process.stderr}")
                return False
        
        # 如果有新提交，计数会大于0
        commit_count = diff_process.stdout.strip()
        if commit_count and int(commit_count) > 0:
            logger.info(f"检测到Git仓库有{commit_count}个新提交")
            return True
        
        # 作为备用检查，也检查状态输出
        status_process = subprocess.run(
            ["git", "status", "-uno"], 
            cwd=current_dir,
            capture_output=True, 
            text=True
        )
        
        # 检查输出中是否包含更新相关的文本
        has_updates = any(text in status_process.stdout for text in [
            "Your branch is behind", 
            "可以快进",
            "需要更新",
            "落后"
        ])
        
        if has_updates:
            logger.info("通过状态检查检测到Git仓库有更新")
            return True
        else:
            logger.info("Git仓库已是最新")
            return False
        
    except Exception as e:
        logger.error(f"检查Git更新时出错: {e}", exc_info=True)
        return False


def check_and_update_git():
    """检查Git更新并在有更新时执行更新"""
    try:
        if check_git_updates():
            logger.info("检测到代码更新，准备更新仓库")
            update_repository()
        else:
            logger.info("没有检测到代码更新")
    except Exception as e:
        logger.error(f"检查和更新Git时出错: {e}", exc_info=True)

def calculate_next_run_time():
    """计算下一次执行时间，确保每15分钟的第14分钟55秒执行"""
    now = datetime.datetime.now()
    
    # 计算当前分钟在15分钟周期中的位置
    current_minute = now.minute
    current_second = now.second
    
    # 计算当前15分钟周期的第14分钟
    current_cycle = current_minute // 15  # 当前是第几个15分钟周期
    target_minute = current_cycle * 15 + 14  # 当前周期的第14分钟
    
    # 计算下一次执行时间
    if current_minute < target_minute or (current_minute == target_minute and current_second < 55):
        # 当前周期的第14分钟55秒
        next_time = now.replace(minute=target_minute, second=55, microsecond=0)
    else:
        # 下一个周期的第14分钟55秒
        next_cycle = current_cycle + 1
        next_minute = (next_cycle * 15 + 14) % 60  # 确保分钟在0-59范围内
        
        # 如果下一个周期跨小时了
        if next_minute < current_minute:
            next_time = now + datetime.timedelta(hours=1)
            next_time = next_time.replace(minute=next_minute, second=55, microsecond=0)
        else:
            next_time = now.replace(minute=next_minute, second=55, microsecond=0)
    
    return next_time
    
def update_repository():
    """更新代码仓库"""
    logger.info("开始更新代码仓库...")
    
    try:
        current_dir = os.path.dirname(os.path.abspath(__file__))
        update_bat = os.path.join(current_dir, "update.bat")
        
        if not os.path.exists(update_bat):
            logger.error(f"更新脚本不存在: {update_bat}")
            return False
        
        # 创建标记文件
        update_flag_file = os.path.join(current_dir, "updating.flag")
        try:
            with open(update_flag_file, 'w') as f:
                f.write(f"Update started at {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        except Exception as e:
            logger.error(f"创建更新标记文件失败: {e}")
        
        # 执行update.bat
        logger.info("执行update.bat...")
        try:
            # 确保没有遗留的Python进程
            logger.info("确保没有遗留的Python进程...")
            try:
                # 使用隐藏窗口方式终止进程
                startupinfo = subprocess.STARTUPINFO()
                startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
                startupinfo.wShowWindow = subprocess.SW_HIDE
                
                subprocess.run(['taskkill', '/F', '/IM', 'python.exe'], 
                             capture_output=True,
                             startupinfo=startupinfo)
                time.sleep(1)
            except Exception as e:
                logger.error(f"终止Python进程时出错: {e}")
                pass
            
            # 使用Windows特定的方式启动隐藏进程
            si = subprocess.STARTUPINFO()
            si.dwFlags |= subprocess.STARTF_USESHOWWINDOW
            si.wShowWindow = subprocess.SW_HIDE
            
            # 使用subprocess.CREATE_NO_WINDOW标志
            CREATE_NO_WINDOW = 0x08000000
            
            process = subprocess.Popen(
                [update_bat],
                cwd=current_dir,
                shell=True,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                startupinfo=si,
                creationflags=CREATE_NO_WINDOW
            )
            
            # 给update.bat一些时间启动
            time.sleep(3)
            
            # 检查进程是否正常启动
            if process.poll() is not None:
                logger.error(f"更新脚本启动失败，返回码: {process.returncode}")
                if os.path.exists(update_flag_file):
                    try:
                        os.remove(update_flag_file)
                    except:
                        pass
                return False
                
            logger.info("更新脚本已启动，程序即将重启")
            time.sleep(2)
            sys.exit(0)
            
        except Exception as e:
            if os.path.exists(update_flag_file):
                try:
                    os.remove(update_flag_file)
                except:
                    pass
            logger.error(f"执行update.bat时出错: {e}", exc_info=True)
            return False
            
    except Exception as e:
        logger.error(f"更新代码仓库时出错: {e}", exc_info=True)
        return False

def main():
    """主函数"""
    logger.info("调度器启动")
    
    try:
        # 检查是否存在更新标记文件，如果存在则删除
        current_dir = os.path.dirname(os.path.abspath(__file__))
        update_flag_file = os.path.join(current_dir, "updating.flag")
        if os.path.exists(update_flag_file):
            logger.info("发现未完成的更新标记文件，清理...")
            try:
                os.remove(update_flag_file)
            except Exception as e:
                logger.error(f"清理更新标记文件失败: {e}")
        
        # 启动时先检查一次Git更新
        check_and_update_git()
        
        while True:
            try:
                # 计算下一次执行时间
                next_run = calculate_next_run_time()
                now = datetime.datetime.now()
                wait_seconds = (next_run - now).total_seconds()
                
                if wait_seconds < 0:
                    logger.warning(f"计算的等待时间为负值: {wait_seconds}秒，重新计算")
                    time.sleep(1)  # 等待1秒后重新计算
                    continue
                
                logger.info(f"下次执行时间: {next_run.strftime('%Y-%m-%d %H:%M:%S')} (等待 {int(wait_seconds)} 秒)")
                
                # 等待到下一个执行时间
                time.sleep(wait_seconds)
                
                # 执行交易脚本
                run_trader()
                
                # 执行完成后再次检查Git更新
                check_and_update_git()
                
            except Exception as e:
                logger.error(f"执行周期内出错: {e}", exc_info=True)
                # 出错时检查Git更新
                check_and_update_git()
                time.sleep(60)  # 出错后等待1分钟再继续
    
    except KeyboardInterrupt:
        logger.info("收到中断信号，退出程序")
    except Exception as e:
        logger.error(f"调度器运行出错: {e}", exc_info=True)
    finally:
        logger.info("调度器已停止")


if __name__ == "__main__":
    main()