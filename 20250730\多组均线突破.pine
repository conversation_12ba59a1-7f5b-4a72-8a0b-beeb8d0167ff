//@version=5
strategy("多组均线突破", overlay=true, initial_capital=100000, default_qty_type=strategy.percent_of_equity, default_qty_value=100)

// === 均线参数 ===
ema8_len = input(8, "8日EMA")  // 新增 8 日均线
ema21_len = input(21, "21日EMA")
ema55_len = input(55, "55日EMA")
ema89_len = input(89, "89日EMA")
ema233_len = input(233, "233日EMA")
ema144_len = input(144, "144日EMA")
ema576_len = input(576, "576日EMA")  // 更新为576日均线

// === 计算均线 ===
ema8 = ta.ema(close, ema8_len)  // 计算 8 日均线
ema21 = ta.ema(close, ema21_len)
ema55 = ta.ema(close, ema55_len)
ema89 = ta.ema(close, ema89_len)
ema233 = ta.ema(close, ema233_len)
ema144 = ta.ema(close, ema144_len)
ema576 = ta.ema(close, ema576_len)  // 计算 576 日均线

// === 开关参数 ===
use8EMA = input.bool(true, "启用8日均线进场")
use21EMA = input.bool(true, "启用21日均线进场")
use55EMA = input.bool(true, "启用55日均线进场")
use144EMA = input.bool(true, "启用144日均线进场")
use576EMA = input.bool(true, "启用576日均线进场")  // 启用576日均线进场
useReverseEntry = input.bool(true, "启用反转入场")  // 控制反转入场的开关

// === 进场条件 ===
// 8日均线进场
longCondition_8EMA = use8EMA and close > ema21 and ta.crossover(close, ema8)
exitCondition_8EMA = ta.crossunder(close, ema8)  // 价格跌破8日均线

// 21日均线进场
longCondition_21_89 = use21EMA and close > ema89 and ta.crossover(close, ema21)
exitCondition_21_89 = ta.crossunder(close, ema21)

// 55日均线进场
longCondition_55_233 = use55EMA and close > ema233 and ta.crossover(close, ema55)
exitCondition_55_233 = ta.crossunder(close, ema55)

// 144日均线进场
longCondition_144_576 = use144EMA and close > ema576 and ta.crossover(close, ema144)
exitCondition_144_576 = ta.crossunder(close, ema144)

// === 反转入场条件 ===
// 当价格突破233日均线并且位于21、55、89均线上方
reverseLongCondition = useReverseEntry and close > ema233 and close > ema21 and close > ema55 and close > ema89 and ta.crossover(close, ema233)
exitReverseCondition = ta.crossunder(close, ema55)  // 反转出场：价格跌破55日均线时离场

// === 反转止盈条件 ===
// 反转止盈条件：仅当价格跌破89日均线时止盈
reverseExitCondition = ta.crossunder(close, ema89)  // 价格跌破89日均线时离场

// === 交易执行 ===
if (longCondition_21_89)
    strategy.entry("Buy_21_89", strategy.long)

if (longCondition_55_233)
    strategy.entry("Buy_55_233", strategy.long)

if (longCondition_144_576)
    strategy.entry("Buy_144_576", strategy.long)

if (longCondition_8EMA)
    strategy.entry("Buy_8EMA", strategy.long)  // 8日均线突破入场

if (reverseLongCondition)
    strategy.entry("Buy_Reverse", strategy.long)  // 反转入场条件触发时入场

// 出场条件
if (exitCondition_21_89)
    strategy.close("Buy_21_89")

if (exitCondition_55_233)
    strategy.close("Buy_55_233")

if (exitCondition_144_576)
    strategy.close("Buy_144_576")

if (exitCondition_8EMA)
    strategy.close("Buy_8EMA")  // 8日均线跌破时离场

if (exitReverseCondition)
    strategy.close("Buy_Reverse")  // 反转入场条件触发时离场

// 反转止盈条件
if (reverseExitCondition)
    strategy.close("Buy_Reverse")  // 反转止盈：价格跌破89日均线时离场

// === 用户可调止盈止损比例 ===
stopLossPercent = input.float(5.0, title="固定止盈止损回撤 (%)", minval=0.1, maxval=100)

// === 画图 ===
plot(ema21, "EMA 21", color=color.blue, linewidth=2)
plot(ema55, "EMA 55", color=color.green, linewidth=2)
plot(ema89, "EMA 89", color=color.red, linewidth=2)
plot(ema233, "EMA 233", color=color.orange, linewidth=2)
plot(ema144, "EMA 144", color=color.purple, linewidth=2)
plot(ema576, "EMA 576", color=color.yellow, linewidth=2)  // 画出576日均线
plot(ema8, "EMA 8", color=color.aqua, linewidth=2)  // 画出8日均线

// === 交易信号标记 ===
plotshape(series=longCondition_21_89, style=shape.triangleup, location=location.belowbar, color=color.blue, size=size.small, title="买入信号 21EMA & 89EMA")
plotshape(series=longCondition_55_233, style=shape.triangleup, location=location.belowbar, color=color.green, size=size.small, title="买入信号 55EMA & 233EMA")
plotshape(series=longCondition_144_576, style=shape.triangleup, location=location.belowbar, color=color.purple, size=size.small, title="买入信号 144EMA & 576EMA")
plotshape(series=longCondition_8EMA, style=shape.triangleup, location=location.belowbar, color=color.aqua, size=size.small, title="买入信号 8EMA突破")
plotshape(series=reverseLongCondition, style=shape.triangleup, location=location.belowbar, color=color.yellow, size=size.small, title="反转入场信号")
plotshape(series=exitCondition_21_89, style=shape.triangledown, location=location.abovebar, color=color.blue, size=size.small, title="卖出信号 21EMA & 89EMA")
plotshape(series=exitCondition_55_233, style=shape.triangledown, location=location.abovebar, color=color.green, size=size.small, title="卖出信号 55EMA & 233EMA")
plotshape(series=exitCondition_144_576, style=shape.triangledown, location=location.abovebar, color=color.purple, size=size.small, title="卖出信号 144EMA & 576EMA")
plotshape(series=exitCondition_8EMA, style=shape.triangledown, location=location.abovebar, color=color.aqua, size=size.small, title="卖出信号 8EMA跌破")
plotshape(series=exitReverseCondition, style=shape.triangledown, location=location.abovebar, color=color.yellow, size=size.small, title="卖出信号 反转入场")
plotshape(series=reverseExitCondition, style=shape.triangledown, location=location.abovebar, color=color.red, size=size.small, title="反转止盈信号")