//+------------------------------------------------------------------+
//|                                           Analysis_Volatility.mqh |
//|                                         Copyright © 2025, htazq |
//|                                         https://github.com/htazq |
//+------------------------------------------------------------------+
#property copyright "Copyright © 2025, htazq"
#property link      "https://github.com/htazq"

#include "Defines.mqh"
#include "Context_Session.mqh"

//+------------------------------------------------------------------+
//| ATR波动率分析命名空间                                              |
//+------------------------------------------------------------------+
namespace Volatility
{
    //+------------------------------------------------------------------+
    //| 私有变量                                                          |
    //+------------------------------------------------------------------+
    static int atr_handle = INVALID_HANDLE;
    static int atr_period = ATR_PERIOD;
    static double atr_buffer[];
    static double session_atr_history[][4]; // [天数][时段] 存储历史ATR数据
    static int history_days_stored = 0;
    
    // ATR统计数据结构
    struct ATRStats
    {
        double current_value;       // 当前ATR值
        double daily_average;       // 日平均ATR
        double session_average;     // 当前时段平均ATR
        double percentile_rank;     // 百分位排名 (0-100)
        string volatility_level;    // 波动率水平描述
    };
    
    static ATRStats current_stats;
    
    //+------------------------------------------------------------------+
    //| 初始化ATR分析模块                                                  |
    //+------------------------------------------------------------------+
    void Initialize(int period = ATR_PERIOD)
    {
        atr_period = period;
        
        // 创建ATR指标句柄
        atr_handle = iATR(_Symbol, _Period, atr_period);
        if(atr_handle == INVALID_HANDLE)
        {
            Print("错误：无法创建ATR指标句柄");
            return;
        }
        
        // 初始化缓冲区
        ArraySetAsSeries(atr_buffer, true);
        ArrayResize(atr_buffer, 1000);
        
        // 初始化历史数据数组
        ArrayResize(session_atr_history, ATR_HISTORY_DAYS);
        ArrayInitialize(session_atr_history, 0.0);
        
        // 初始化统计数据
        current_stats.current_value = 0.0;
        current_stats.daily_average = 0.0;
        current_stats.session_average = 0.0;
        current_stats.percentile_rank = 0.0;
        current_stats.volatility_level = "未知";
        
        DEBUG_PRINT(StringFormat("ATR波动率分析模块初始化完成，周期: %d", atr_period));
    }
    
    //+------------------------------------------------------------------+
    //| 清理ATR分析模块                                                    |
    //+------------------------------------------------------------------+
    void Deinitialize()
    {
        if(atr_handle != INVALID_HANDLE)
        {
            IndicatorRelease(atr_handle);
            atr_handle = INVALID_HANDLE;
        }
        
        ArrayFree(atr_buffer);
        ArrayFree(session_atr_history);
        
        DEBUG_PRINT("ATR波动率分析模块已清理");
    }
    
    //+------------------------------------------------------------------+
    //| 获取当前ATR值                                                      |
    //+------------------------------------------------------------------+
    double GetCurrentValue()
    {
        if(atr_handle == INVALID_HANDLE) return 0.0;
        
        // 复制ATR数据
        int copied = CopyBuffer(atr_handle, 0, 0, 1, atr_buffer);
        if(copied <= 0)
        {
            DEBUG_PRINT("警告：无法获取ATR数据");
            return current_stats.current_value; // 返回上次的值
        }
        
        current_stats.current_value = atr_buffer[0];
        return current_stats.current_value;
    }
    
    //+------------------------------------------------------------------+
    //| 计算日平均ATR                                                      |
    //+------------------------------------------------------------------+
    double CalculateDailyAverage(int days_back = 30)
    {
        if(atr_handle == INVALID_HANDLE) return 0.0;
        
        // 计算需要的K线数量（基于当前时间框架）
        int bars_needed = days_back * 24 * 60 / PeriodSeconds(_Period) * 60;
        if(bars_needed > 1000) bars_needed = 1000;
        
        // 复制ATR历史数据
        int copied = CopyBuffer(atr_handle, 0, 0, bars_needed, atr_buffer);
        if(copied <= 0) return current_stats.daily_average;
        
        // 计算平均值
        double sum = 0.0;
        int valid_count = 0;
        
        for(int i = 0; i < copied; i++)
        {
            if(atr_buffer[i] > 0)
            {
                sum += atr_buffer[i];
                valid_count++;
            }
        }
        
        if(valid_count > 0)
        {
            current_stats.daily_average = sum / valid_count;
        }
        
        return current_stats.daily_average;
    }
    
    //+------------------------------------------------------------------+
    //| 计算指定时段的历史平均ATR                                          |
    //+------------------------------------------------------------------+
    double GetHistoricalAverage(ENUM_TRADING_SESSION session, int days_to_look_back = 10)
    {
        if(atr_handle == INVALID_HANDLE) return 0.0;
        
        double sum = 0.0;
        int count = 0;
        
        // 获取足够的历史数据
        int bars_needed = days_to_look_back * 24 * 60 / PeriodSeconds(_Period) * 60;
        if(bars_needed > 1000) bars_needed = 1000;
        
        int copied = CopyBuffer(atr_handle, 0, 0, bars_needed, atr_buffer);
        if(copied <= 0) return 0.0;
        
        // 遍历历史数据，只计算指定时段的ATR
        for(int i = 0; i < copied; i++)
        {
            datetime bar_time = iTime(_Symbol, _Period, i);
            ENUM_TRADING_SESSION bar_session = Session::GetCurrent(bar_time);
            
            if(bar_session == session && atr_buffer[i] > 0)
            {
                sum += atr_buffer[i];
                count++;
            }
        }
        
        if(count > 0)
        {
            current_stats.session_average = sum / count;
            return current_stats.session_average;
        }
        
        return 0.0;
    }
    
    //+------------------------------------------------------------------+
    //| 计算ATR百分位排名                                                  |
    //+------------------------------------------------------------------+
    double CalculatePercentileRank(double current_atr, int lookback_days = 30)
    {
        if(atr_handle == INVALID_HANDLE || current_atr <= 0) return 0.0;
        
        int bars_needed = lookback_days * 24 * 60 / PeriodSeconds(_Period) * 60;
        if(bars_needed > 1000) bars_needed = 1000;
        
        int copied = CopyBuffer(atr_handle, 0, 0, bars_needed, atr_buffer);
        if(copied <= 0) return current_stats.percentile_rank;
        
        // 计算有多少个值小于当前ATR
        int lower_count = 0;
        int valid_count = 0;
        
        for(int i = 0; i < copied; i++)
        {
            if(atr_buffer[i] > 0)
            {
                valid_count++;
                if(atr_buffer[i] < current_atr)
                    lower_count++;
            }
        }
        
        if(valid_count > 0)
        {
            current_stats.percentile_rank = (double)lower_count / valid_count * 100.0;
        }
        
        return current_stats.percentile_rank;
    }
    
    //+------------------------------------------------------------------+
    //| 获取波动率水平描述                                                 |
    //+------------------------------------------------------------------+
    string GetVolatilityLevel(double percentile_rank)
    {
        if(percentile_rank >= 80)
            return "极高";
        else if(percentile_rank >= 60)
            return "高";
        else if(percentile_rank >= 40)
            return "中等";
        else if(percentile_rank >= 20)
            return "低";
        else
            return "极低";
    }
    
    //+------------------------------------------------------------------+
    //| 获取波动率水平颜色                                                 |
    //+------------------------------------------------------------------+
    color GetVolatilityColor(double percentile_rank)
    {
        if(percentile_rank >= 80)
            return COLOR_DANGER;        // 极高 - 红色
        else if(percentile_rank >= 60)
            return COLOR_WARNING;       // 高 - 橙色
        else if(percentile_rank >= 40)
            return COLOR_ACCENT;        // 中等 - 金色
        else if(percentile_rank >= 20)
            return COLOR_SUCCESS;       // 低 - 绿色
        else
            return COLOR_INFO;          // 极低 - 白色
    }
    
    //+------------------------------------------------------------------+
    //| 更新ATR统计数据                                                    |
    //+------------------------------------------------------------------+
    void UpdateStats()
    {
        // 获取当前ATR值
        current_stats.current_value = GetCurrentValue();
        if(current_stats.current_value <= 0) return;
        
        // 计算日平均ATR
        current_stats.daily_average = CalculateDailyAverage();
        
        // 计算当前时段平均ATR
        ENUM_TRADING_SESSION current_session = Session::GetCurrent(TimeCurrent());
        current_stats.session_average = GetHistoricalAverage(current_session);
        
        // 计算百分位排名
        current_stats.percentile_rank = CalculatePercentileRank(current_stats.current_value);
        
        // 更新波动率水平描述
        current_stats.volatility_level = GetVolatilityLevel(current_stats.percentile_rank);
        
        DEBUG_PRINT(StringFormat("ATR统计更新: 当前=%.5f, 日均=%.5f, 时段均=%.5f, 百分位=%.1f%%, 水平=%s",
                    current_stats.current_value, current_stats.daily_average, 
                    current_stats.session_average, current_stats.percentile_rank, 
                    current_stats.volatility_level));
    }
    
    //+------------------------------------------------------------------+
    //| 获取ATR统计数据                                                    |
    //+------------------------------------------------------------------+
    ATRStats GetStats()
    {
        return current_stats;
    }
    
    //+------------------------------------------------------------------+
    //| 检查是否为高波动率环境                                             |
    //+------------------------------------------------------------------+
    bool IsHighVolatility(double threshold_percentile = 70.0)
    {
        return current_stats.percentile_rank >= threshold_percentile;
    }
    
    //+------------------------------------------------------------------+
    //| 检查是否为低波动率环境                                             |
    //+------------------------------------------------------------------+
    bool IsLowVolatility(double threshold_percentile = 30.0)
    {
        return current_stats.percentile_rank <= threshold_percentile;
    }
    
    //+------------------------------------------------------------------+
    //| 获取ATR倍数（相对于日平均）                                        |
    //+------------------------------------------------------------------+
    double GetATRMultiple()
    {
        if(current_stats.daily_average <= 0) return 1.0;
        return current_stats.current_value / current_stats.daily_average;
    }
    
    //+------------------------------------------------------------------+
    //| 获取风险调整后的止损距离                                           |
    //+------------------------------------------------------------------+
    double GetRiskAdjustedStopDistance(double risk_multiplier = 2.0)
    {
        return current_stats.current_value * risk_multiplier;
    }
    
    //+------------------------------------------------------------------+
    //| 获取动态仓位大小建议                                               |
    //+------------------------------------------------------------------+
    double GetDynamicPositionSize(double account_risk_percent = 2.0, double stop_distance = 0.0)
    {
        if(stop_distance <= 0)
            stop_distance = GetRiskAdjustedStopDistance();
        
        if(stop_distance <= 0) return 0.0;
        
        double account_balance = AccountInfoDouble(ACCOUNT_BALANCE);
        double risk_amount = account_balance * account_risk_percent / 100.0;
        
        // 计算每点价值
        double point_value = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_VALUE);
        if(point_value <= 0) point_value = 1.0;
        
        // 计算建议仓位大小
        double position_size = risk_amount / (stop_distance / _Point * point_value);
        
        // 标准化到最小交易量
        double min_lot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
        double lot_step = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);
        
        if(min_lot > 0 && lot_step > 0)
        {
            position_size = MathFloor(position_size / lot_step) * lot_step;
            if(position_size < min_lot) position_size = min_lot;
        }
        
        return position_size;
    }
    
    //+------------------------------------------------------------------+
    //| 格式化ATR显示文本                                                  |
    //+------------------------------------------------------------------+
    string FormatATRDisplay()
    {
        string display_text = "";
        
        display_text += StringFormat("当前ATR: %s\n", FORMAT_PRICE(current_stats.current_value));
        display_text += StringFormat("日均ATR: %s\n", FORMAT_PRICE(current_stats.daily_average));
        display_text += StringFormat("时段均: %s\n", FORMAT_PRICE(current_stats.session_average));
        display_text += StringFormat("百分位: %.1f%%\n", current_stats.percentile_rank);
        display_text += StringFormat("波动率: %s", current_stats.volatility_level);
        
        return display_text;
    }
}