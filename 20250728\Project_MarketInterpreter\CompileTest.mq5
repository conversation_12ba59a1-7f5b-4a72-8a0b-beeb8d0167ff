//+------------------------------------------------------------------+
//|                                                  CompileTest.mq5 |
//|                                         Copyright © 2025, htazq |
//|                                         https://github.com/htazq |
//+------------------------------------------------------------------+
#property copyright "Copyright © 2025, htazq"
#property link      "https://github.com/htazq"
#property version   "1.00"
#property description "编译测试文件 - 验证所有模块是否正常"

#property indicator_chart_window
#property indicator_buffers 0
#property indicator_plots   0

// 包含所有模块进行编译测试
#include "Include/Defines.mqh"
#include "Include/Context_Session.mqh"
#include "Include/Analysis_KeyLevels.mqh"
#include "Include/Analysis_Volatility.mqh"
#include "Include/Analysis_PriceAction.mqh"
#include "Include/UI_Dashboard.mqh"
#include "Include/System_Alerts.mqh"

//+------------------------------------------------------------------+
//| 输入参数                                                          |
//+------------------------------------------------------------------+
input bool InpTestMode = true;  // 测试模式

//+------------------------------------------------------------------+
//| 自定义指标初始化函数                                               |
//+------------------------------------------------------------------+
int OnInit()
{
    Print("=== 市场解读家仪表盘编译测试 ===");
    
    // 测试所有模块的初始化
    Session::Initialize();
    KeyLevels::Initialize();
    Volatility::Initialize(14);
    PriceAction::Initialize();
    Alerts::Initialize();
    Dashboard::Initialize(20, 30, 10);
    
    Print("所有模块初始化完成 - 编译测试成功！");
    return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| 自定义指标去初始化函数                                             |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    // 清理所有模块
    Dashboard::Deinitialize();
    Alerts::Deinitialize();
    PriceAction::Deinitialize();
    Volatility::Deinitialize();
    KeyLevels::Deinitialize();
    Session::Deinitialize();
    
    Print("编译测试清理完成");
}

//+------------------------------------------------------------------+
//| 自定义指标计算函数                                                 |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[])
{
    // 简单的功能测试
    if(InpTestMode && rates_total > 100)
    {
        // 测试趋势分析
        ENUM_TREND_STATE trend = PriceAction::GetTrendFromVegasIndicator();
        
        // 测试时段分析
        ENUM_TRADING_SESSION session = Session::GetCurrent(TimeCurrent());
        
        // 测试ATR分析
        double atr_value = Volatility::GetCurrentValue();
        
        // 测试关键价位
        double daily_open = KeyLevels::GetDailyOpen();
        
        // 输出测试结果（仅第一次）
        static bool first_test = true;
        if(first_test)
        {
            Print("=== 功能测试结果 ===");
            Print("趋势状态: ", EnumToString(trend));
            Print("交易时段: ", EnumToString(session));
            Print("ATR值: ", DoubleToString(atr_value, 5));
            Print("日开盘价: ", DoubleToString(daily_open, 5));
            Print("=== 测试完成 ===");
            first_test = false;
        }
    }
    
    return(rates_total);
}