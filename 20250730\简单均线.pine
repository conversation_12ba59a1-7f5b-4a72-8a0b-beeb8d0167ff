//@version=5
strategy("多组均线突破策略（5/20/60）", overlay=true, initial_capital=10000, default_qty_type=strategy.percent_of_equity, default_qty_value=100)

// ================================
// 输入参数
// ================================
// 均线参数
ema5_len = input.int(5, title="5日均线周期")
ema20_len = input.int(20, title="20日均线周期")
ema60_len = input.int(60, title="60日均线周期")

// 开关参数
use5_20 = input.bool(true, title="启用5/20日均线策略")
use20_60 = input.bool(true, title="启用20/60日均线策略")

// ================================
// 计算均线
// ================================
ema5 = ta.ema(close, ema5_len)
ema20 = ta.ema(close, ema20_len)
ema60 = ta.ema(close, ema60_len)

// ================================
// 进场条件
// ================================
longCondition_5_20 = use5_20 and close > ema20 and ta.crossover(close, ema5)
longCondition_20_60 = use20_60 and close > ema60 and ta.crossover(close, ema20)

// ================================
// 离场条件
// ================================
exitCondition = ta.crossunder(close, ema5)  // 统一离场条件：跌破5日均线

// ================================
// 交易执行
// ================================
// 进场逻辑
if (longCondition_5_20)
    strategy.entry("Buy_5_20", strategy.long)

if (longCondition_20_60)
    strategy.entry("Buy_20_60", strategy.long)

// 离场逻辑
if (exitCondition and strategy.position_size > 0)
    strategy.close_all()  // 统一平仓所有持仓

// ================================
// 可视化设置
// ================================
// 绘制均线
plot(ema5, title="EMA 5", color=color.blue, linewidth=1)
plot(ema20, title="EMA 20", color=color.orange, linewidth=1)
plot(ema60, title="EMA 60", color=color.green, linewidth=1)

// ================================
// 背景填充（可选）
// ================================
// 填充5日均线和20日均线之间的区域
fill(plot(ema5), plot(ema20), title="5/20填充", color=close > ema20 ? color.new(color.green, 90) : na)

// 填充20日均线和60日均线之间的区域
fill(plot(ema20), plot(ema60), title="20/60填充", color=close > ema60 ? color.new(color.green, 90) : na)