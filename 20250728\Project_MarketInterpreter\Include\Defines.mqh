//+------------------------------------------------------------------+
//|                                                      Defines.mqh |
//|                                         Copyright © 2025, htazq |
//|                                         https://github.com/htazq |
//+------------------------------------------------------------------+
#property copyright "Copyright © 2025, htazq"
#property link      "https://github.com/htazq"

//+------------------------------------------------------------------+
//| 交易时段枚举                                                      |
//+------------------------------------------------------------------+
enum ENUM_TRADING_SESSION
{
    SESSION_ASIA,    // 亚洲时段 (00:00-09:00 GMT)
    SESSION_LONDON,  // 伦敦时段 (08:00-17:00 GMT)
    SESSION_NEWYORK, // 纽约时段 (13:00-22:00 GMT)
    SESSION_OVERLAP, // 伦敦/纽约重叠时段 (13:00-17:00 GMT)
    SESSION_NONE     // 其他时段
};

//+------------------------------------------------------------------+
//| 趋势状态枚举                                                      |
//+------------------------------------------------------------------+
enum ENUM_TREND_STATE
{
    TREND_UP,       // 上升趋势 - 短期>中期>长期均线，且均线向上倾斜
    TREND_DOWN,     // 下降趋势 - 短期<中期<长期均线，且均线向下倾斜
    TREND_RANGE     // 震荡/无序 - 不满足明确趋势条件
};

//+------------------------------------------------------------------+
//| 价格行为信号类型枚举                                               |
//+------------------------------------------------------------------+
enum ENUM_PA_SIGNAL_TYPE
{
    PA_SIGNAL_NONE,     // 无信号
    PA_SIGNAL_L1,       // L1做多信号 - 上升趋势中第一次回调失败
    PA_SIGNAL_L2,       // L2做多信号 - 上升趋势中第二次回调失败
    PA_SIGNAL_H1,       // H1做空信号 - 下降趋势中第一次反弹失败
    PA_SIGNAL_H2        // H2做空信号 - 下降趋势中第二次反弹失败
};

//+------------------------------------------------------------------+
//| 形态识别类型枚举                                                   |
//+------------------------------------------------------------------+
enum ENUM_PATTERN_TYPE
{
    PATTERN_NONE,           // 无形态
    PATTERN_THREE_PUSHES,   // 三推楔形 - 趋势末端动能衰竭信号
    PATTERN_FLAG_BULL,      // 牛旗 - 上升趋势中继形态
    PATTERN_FLAG_BEAR       // 熊旗 - 下降趋势中继形态
};

//+------------------------------------------------------------------+
//| 图形对象名称常量                                                   |
//+------------------------------------------------------------------+
const string OBJ_PREFIX = "MarketInterpreter_";

// 仪表盘UI对象名称
const string OBJ_DASHBOARD_BACKGROUND = OBJ_PREFIX + "Dashboard_BG";
const string OBJ_TREND_PANEL = OBJ_PREFIX + "Trend_Panel";
const string OBJ_SIGNAL_PANEL = OBJ_PREFIX + "Signal_Panel";
const string OBJ_SESSION_INDICATOR = OBJ_PREFIX + "Session_Indicator";
const string OBJ_ATR_GAUGE = OBJ_PREFIX + "ATR_Gauge";
const string OBJ_ALERT_PANEL = OBJ_PREFIX + "Alert_Panel";

// 关键价位线对象名称
const string OBJ_DAILY_OPEN = OBJ_PREFIX + "Daily_Open";
const string OBJ_PREV_HIGH = OBJ_PREFIX + "Prev_High";
const string OBJ_PREV_LOW = OBJ_PREFIX + "Prev_Low";
const string OBJ_WEEKLY_OPEN = OBJ_PREFIX + "Weekly_Open";
const string OBJ_MONTHLY_OPEN = OBJ_PREFIX + "Monthly_Open";

// 价格行为信号标记对象名称
const string OBJ_L1_SIGNAL = OBJ_PREFIX + "L1_Signal_";
const string OBJ_L2_SIGNAL = OBJ_PREFIX + "L2_Signal_";
const string OBJ_H1_SIGNAL = OBJ_PREFIX + "H1_Signal_";
const string OBJ_H2_SIGNAL = OBJ_PREFIX + "H2_Signal_";

//+------------------------------------------------------------------+
//| 颜色定义常量                                                       |
//+------------------------------------------------------------------+
// 主题颜色
const color COLOR_BACKGROUND = C'41,35,47';        // 深色背景 #292329
const color COLOR_ACCENT = C'176,185,11';          // 金色强调 #F0B90B
const color COLOR_SUCCESS = C'118,192,2';          // 绿色 #02C076
const color COLOR_DANGER = C'96,73,248';           // 红色 #F84960
const color COLOR_WARNING = C'0,191,255';          // 橙色警告
const color COLOR_INFO = C'255,255,255';           // 白色信息

// 趋势状态颜色
const color COLOR_TREND_UP = COLOR_SUCCESS;        // 上升趋势
const color COLOR_TREND_DOWN = COLOR_DANGER;       // 下降趋势
const color COLOR_TREND_RANGE = COLOR_WARNING;     // 震荡趋势

// 交易时段颜色
const color COLOR_SESSION_ASIA = C'255,165,0';     // 亚洲时段 - 蓝色
const color COLOR_SESSION_LONDON = COLOR_SUCCESS;   // 伦敦时段 - 绿色
const color COLOR_SESSION_NEWYORK = C'0,165,255';  // 纽约时段 - 橙色
const color COLOR_SESSION_OVERLAP = C'128,0,128';  // 重叠时段 - 紫色

//+------------------------------------------------------------------+
//| 仪表盘布局常量                                                     |
//+------------------------------------------------------------------+
// 仪表盘位置和尺寸
const int DASHBOARD_X = 20;                 // 仪表盘X坐标
const int DASHBOARD_Y = 30;                 // 仪表盘Y坐标
const int DASHBOARD_WIDTH = 300;            // 仪表盘宽度
const int DASHBOARD_HEIGHT = 400;           // 仪表盘高度

// 面板尺寸
const int PANEL_HEIGHT = 60;                // 单个面板高度
const int PANEL_MARGIN = 10;                // 面板间距
const int FONT_SIZE_TITLE = 12;             // 标题字体大小
const int FONT_SIZE_CONTENT = 10;           // 内容字体大小

//+------------------------------------------------------------------+
//| Vegas Tunnel 均线参数常量                                          |
//+------------------------------------------------------------------+
// 默认Vegas Tunnel均线周期（可通过输入参数调整）
const int VEGAS_EMA_FAST = 12;              // 快速EMA
const int VEGAS_EMA_MEDIUM = 21;            // 中速EMA
const int VEGAS_EMA_SLOW = 55;              // 慢速EMA

//+------------------------------------------------------------------+
//| 价格行为分析参数常量                                               |
//+------------------------------------------------------------------+
const int PA_LOOKBACK_BARS = 100;           // 价格行为分析回看K线数
const double PA_SIGNAL_MIN_BODY_RATIO = 0.3; // 信号K线最小实体比例
const int PA_MAX_SIGNAL_COUNT = 5;          // 最大信号计数

//+------------------------------------------------------------------+
//| ATR分析参数常量                                                    |
//+------------------------------------------------------------------+
const int ATR_PERIOD = 14;                  // ATR计算周期
const int ATR_HISTORY_DAYS = 30;            // ATR历史数据天数

//+------------------------------------------------------------------+
//| 警报系统常量                                                       |
//+------------------------------------------------------------------+
const int MAX_ALERT_HISTORY = 10;           // 最大警报历史记录数
const int ALERT_COOLDOWN_SECONDS = 60;      // 警报冷却时间（秒）

//+------------------------------------------------------------------+
//| 调试和日志常量                                                     |
//+------------------------------------------------------------------+
#ifdef _DEBUG
    const bool DEBUG_MODE = true;
    #define DEBUG_PRINT(msg) Print("[DEBUG] ", msg)
#else
    const bool DEBUG_MODE = false;
    #define DEBUG_PRINT(msg) 
#endif

//+------------------------------------------------------------------+
//| 工具函数宏定义                                                     |
//+------------------------------------------------------------------+
// 安全的数组访问宏
#define SAFE_ARRAY_GET(array, index, default_value) \
    ((index >= 0 && index < ArraySize(array)) ? array[index] : default_value)

// 价格格式化宏
#define FORMAT_PRICE(price) DoubleToString(price, _Digits)

// 时间格式化宏
#define FORMAT_TIME(time) TimeToString(time, TIME_DATE|TIME_MINUTES)