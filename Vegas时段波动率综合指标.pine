//@version=6
indicator("Vegas时段波动率综合指标", "Vegas波动", overlay=true, max_labels_count=500, max_lines_count=500, max_boxes_count=500)

// ============================================================================
// 输入参数设置
// ============================================================================

// EMA周期设置
ema1_period = input.int(20, "EMA 1 (短期)", minval=1, group="EMA周期")
ema2_period = input.int(144, "EMA 2 (中期1)", minval=1, group="EMA周期")
ema3_period = input.int(169, "EMA 3 (中期2)", minval=1, group="EMA周期")
ema4_period = input.int(288, "EMA 4 (长期1)", minval=1, group="EMA周期")
ema5_period = input.int(338, "EMA 5 (长期2)", minval=1, group="EMA周期")

// 显示设置
show_ema1 = input.bool(true, "显示 EMA20", group="显示设置")
show_ema2 = input.bool(true, "显示 EMA144", group="显示设置")
show_ema3 = input.bool(true, "显示 EMA169", group="显示设置")
show_ema4 = input.bool(true, "显示 EMA288", group="显示设置")
show_ema5 = input.bool(true, "显示 EMA338", group="显示设置")
show_countdown = input.bool(true, "显示倒计时", group="显示设置")
countdown_color = input.color(color.white, "倒计时颜色", group="显示设置")

// 关键价位线设置
show_daily_open = input.bool(true, "显示日线开盘价", group="关键价位线设置")
daily_open_color = input.color(color.yellow, "日线开盘价颜色", group="关键价位线设置")
show_prev_high = input.bool(true, "显示前一日最高点", group="关键价位线设置")
prev_high_color = input.color(color.red, "前一日最高点颜色", group="关键价位线设置")
show_prev_low = input.bool(true, "显示前一日最低点", group="关键价位线设置")
prev_low_color = input.color(color.lime, "前一日最低点颜色", group="关键价位线设置")

// 时段颜色主题
color_asia = input.color(color.new(color.gray, 30), "亚洲时段", group="时段颜色主题")
color_london = input.color(color.new(color.green, 30), "伦敦时段", group="时段颜色主题")
color_overlap = input.color(color.new(color.orange, 30), "重叠时段", group="时段颜色主题")
color_newyork = input.color(color.new(color.blue, 30), "纽约时段", group="时段颜色主题")

// 波动率分析设置
show_volatility = input.bool(true, "显示波动率分析", group="波动率分析设置")
avg_days = input.int(20, "计算日均波幅的天数", minval=5, maxval=50, group="波动率分析设置")
show_dashboard = input.bool(true, "显示仪表盘", group="波动率分析设置")

// ============================================================================
// EMA计算和绘制
// ============================================================================

ema1 = ta.ema(close, ema1_period)
ema2 = ta.ema(close, ema2_period)
ema3 = ta.ema(close, ema3_period)
ema4 = ta.ema(close, ema4_period)
ema5 = ta.ema(close, ema5_period)

plot(show_ema1 ? ema1 : na, "EMA20", color.yellow, 1)
plot(show_ema2 ? ema2 : na, "EMA144", color.new(color.lime, 0), 1)
plot(show_ema3 ? ema3 : na, "EMA169", color.green, 1)
plot(show_ema4 ? ema4 : na, "EMA288", color.new(color.blue, 20), 1)
plot(show_ema5 ? ema5 : na, "EMA338", color.blue, 1)

// ============================================================================
// 关键价位线
// ============================================================================

// 获取日线数据
daily_open = request.security(syminfo.tickerid, "1D", open, lookahead=barmerge.lookahead_off)
prev_daily_high = request.security(syminfo.tickerid, "1D", high[1], lookahead=barmerge.lookahead_off)
prev_daily_low = request.security(syminfo.tickerid, "1D", low[1], lookahead=barmerge.lookahead_off)

// 绘制关键价位线
plot(show_daily_open ? daily_open : na, "日线开盘价", daily_open_color, 1, plot.style_line)
plot(show_prev_high ? prev_daily_high : na, "前一日最高点", prev_high_color, 1, plot.style_line)
plot(show_prev_low ? prev_daily_low : na, "前一日最低点", prev_low_color, 1, plot.style_line)

// ============================================================================
// 倒计时功能
// ============================================================================

// 计算下一根K线的剩余时间
get_countdown_text() =>
    current_time = time
    next_bar_time = current_time + (timeframe.in_seconds() * 1000)
    remaining_ms = next_bar_time - timenow
    remaining_seconds = math.max(0, math.floor(remaining_ms / 1000))
    
    hours = math.floor(remaining_seconds / 3600)
    minutes = math.floor((remaining_seconds % 3600) / 60)
    seconds = remaining_seconds % 60
    
    countdown_text = ""
    if hours > 0
        countdown_text := "倒计时: " + str.tostring(hours) + "时" + str.format("{0,number,00}", minutes) + "分" + str.format("{0,number,00}", seconds) + "秒"
    else if minutes > 0
        countdown_text := "倒计时: " + str.format("{0,number,00}", minutes) + "分" + str.format("{0,number,00}", seconds) + "秒"
    else
        countdown_text := "倒计时: " + str.format("{0,number,00}", seconds) + "秒"
    
    countdown_text

// 显示倒计时
if show_countdown and barstate.islast
    countdown_text = get_countdown_text()
    var label countdown_label = na
    label.delete(countdown_label)
    countdown_label := label.new(bar_index + 1, close, countdown_text, color=color.new(color.black, 80), textcolor=countdown_color, style=label.style_label_left, size=size.normal)

// ============================================================================
// 夏令时判断函数
// ============================================================================

// 判断纽约是否为夏令时
is_newyork_dst(timestamp) =>
    year_val = year(timestamp)
    
    // 三月第二个星期日
    march_first = timestamp(year_val, 3, 1, 0, 0, 0)
    march_first_dow = dayofweek(march_first)
    days_to_first_sunday = march_first_dow == 1 ? 0 : 8 - march_first_dow
    second_sunday_day = 1 + days_to_first_sunday + 7
    dst_start = timestamp(year_val, 3, second_sunday_day, 7, 0, 0) // 2AM EST = 7AM GMT
    
    // 十一月第一个星期日
    nov_first = timestamp(year_val, 11, 1, 0, 0, 0)
    nov_first_dow = dayofweek(nov_first)
    days_to_nov_first_sunday = nov_first_dow == 1 ? 0 : 8 - nov_first_dow
    first_sunday_day = 1 + days_to_nov_first_sunday
    dst_end = timestamp(year_val, 11, first_sunday_day, 6, 0, 0) // 2AM EST = 6AM GMT
    
    timestamp >= dst_start and timestamp < dst_end

// 判断伦敦是否为夏令时
is_london_dst(timestamp) =>
    year_val = year(timestamp)
    
    // 三月最后一个星期日
    march_31 = timestamp(year_val, 3, 31, 0, 0, 0)
    march_31_dow = dayofweek(march_31)
    days_back_to_sunday = march_31_dow == 1 ? 0 : march_31_dow - 1
    last_sunday_day = 31 - days_back_to_sunday
    dst_start = timestamp(year_val, 3, last_sunday_day, 1, 0, 0) // 1AM GMT
    
    // 十月最后一个星期日
    oct_31 = timestamp(year_val, 10, 31, 0, 0, 0)
    oct_31_dow = dayofweek(oct_31)
    oct_days_back_to_sunday = oct_31_dow == 1 ? 0 : oct_31_dow - 1
    oct_last_sunday_day = 31 - oct_days_back_to_sunday
    dst_end = timestamp(year_val, 10, oct_last_sunday_day, 1, 0, 0) // 1AM GMT
    
    timestamp >= dst_start and timestamp < dst_end

// ============================================================================
// 交易时段识别
// ============================================================================

// 获取当前交易时段
get_current_session() =>
    current_gmt = time
    london_dst = is_london_dst(current_gmt)
    newyork_dst = is_newyork_dst(current_gmt)
    
    // 获取当天0点GMT时间
    today_start = math.floor(current_gmt / 86400000) * 86400000
    
    // 计算关键时间点
    london_open = today_start + (london_dst ? 7 : 8) * 3600000
    london_close = today_start + (london_dst ? 16 : 17) * 3600000
    newyork_open = today_start + (newyork_dst ? 13 : 14) * 3600000 + 30 * 60000
    newyork_close = today_start + 22 * 3600000
    asia_start = today_start - 2 * 3600000
    
    session_name = ""
    session_color = color.gray
    
    // 重叠时段优先判断
    if current_gmt >= newyork_open and current_gmt < london_close
        session_name := "重叠时段(多方角力)"
        session_color := color_overlap
    // 亚洲时段
    else if current_gmt >= asia_start and current_gmt < london_open
        session_name := "亚洲时段"
        session_color := color_asia
    // 伦敦时段
    else if current_gmt >= london_open and current_gmt < newyork_open
        session_name := "伦敦时段"
        session_color := color_london
    // 纽约后半段
    else if current_gmt >= london_close and current_gmt < newyork_close
        session_name := "纽约后半段(一方主导)"
        session_color := color_newyork
    // 跨日亚洲时段
    else if current_gmt >= newyork_close
        session_name := "亚洲时段"
        session_color := color_asia
    else
        session_name := "非主要时段"
        session_color := color.gray
    
    [session_name, session_color]

// ============================================================================
// 波动率分析
// ============================================================================

// 计算真实波幅
calc_true_range() =>
    tr1 = high - low
    tr2 = math.abs(high - close[1])
    tr3 = math.abs(low - close[1])
    math.max(tr1, math.max(tr2, tr3))

// 获取日均真实波幅
get_daily_avg_range() =>
    // 使用ta.atr函数计算平均真实波幅，这是更标准的方法
    ta.atr(avg_days)

// 获取今日真实波幅
get_today_range() =>
    daily_high = request.security(syminfo.tickerid, "1D", high, lookahead=barmerge.lookahead_off)
    daily_low = request.security(syminfo.tickerid, "1D", low, lookahead=barmerge.lookahead_off)
    prev_close = request.security(syminfo.tickerid, "1D", close[1], lookahead=barmerge.lookahead_off)

    if daily_high > 0 and daily_low > 0 and prev_close > 0
        tr1 = daily_high - daily_low
        tr2 = math.abs(daily_high - prev_close)
        tr3 = math.abs(daily_low - prev_close)
        math.max(tr1, math.max(tr2, tr3))
    else
        0

// 计算全局变量，避免在条件语句中重复调用
daily_avg_range = get_daily_avg_range()
today_range = get_today_range()

// ============================================================================
// 仪表盘显示
// ============================================================================

if show_dashboard and barstate.islast
    [session_name, session_color] = get_current_session()
    remaining_range = daily_avg_range - today_range
    percentage = daily_avg_range > 0 ? (today_range / daily_avg_range * 100) : 0
    
    // 创建仪表盘文本
    dashboard_text = "[ 交易时段与波动率监控 ]\n"
    dashboard_text := dashboard_text + "当前时段: " + session_name + "\n"
    dashboard_text := dashboard_text + "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n"
    dashboard_text := dashboard_text + "日均波幅(" + str.tostring(avg_days) + "D): " + str.tostring(daily_avg_range, "#.#####") + "\n"
    dashboard_text := dashboard_text + "今日已走: " + str.tostring(today_range, "#.#####") + " (" + str.tostring(percentage, "#.#") + "%)\n"
    
    remaining_text = ""
    if remaining_range < 0
        remaining_text := "预估剩余: 已超出 " + str.tostring(math.abs(remaining_range), "#.#####")
    else if remaining_range < daily_avg_range * 0.1
        remaining_text := "预估剩余: " + str.tostring(remaining_range, "#.#####") + " (即将完成)"
    else
        remaining_text := "预估剩余: " + str.tostring(remaining_range, "#.#####")
    
    dashboard_text := dashboard_text + remaining_text
    
    // 删除旧的仪表盘标签
    var label dashboard_label = na
    label.delete(dashboard_label)
    
    // 创建新的仪表盘标签
    dashboard_label := label.new(x=bar_index + 10, y=high, text=dashboard_text, color=color.new(color.black, 20), textcolor=color.white, style=label.style_label_left, size=size.normal)

// ============================================================================
// 时段背景着色
// ============================================================================

[current_session_name, current_session_color] = get_current_session()

// 为当前时段添加背景色
var box session_box = na
if barstate.islast and current_session_name != "非主要时段"
    box.delete(session_box)
    session_box := box.new(left=bar_index - 50, top=high * 1.01, right=bar_index + 10, bottom=low * 0.99, bgcolor=current_session_color, border_color=color.new(current_session_color, 50), border_width=1)

// ============================================================================
// 时段信息标签
// ============================================================================

if barstate.islast and current_session_name != "非主要时段"
    var label session_label = na
    label.delete(session_label)
    session_label := label.new(x=bar_index, y=high * 1.005, text=current_session_name, color=current_session_color, textcolor=color.white, style=label.style_label_down, size=size.small)

// ============================================================================
// 提醒和警报
// ============================================================================

// EMA排列状态检测
ema_bullish = ema1 > ema2 and ema2 > ema3 and ema3 > ema4 and ema4 > ema5
ema_bearish = ema1 < ema2 and ema2 < ema3 and ema3 < ema4 and ema4 < ema5

// 波动率警报
high_volatility = show_volatility and today_range > daily_avg_range * 1.2
low_volatility = show_volatility and today_range < daily_avg_range * 0.5

// 绘制EMA排列状态
plotshape(ema_bullish and not ema_bullish[1], "多头排列", shape.triangleup, location.belowbar, color.lime, size=size.small)
plotshape(ema_bearish and not ema_bearish[1], "空头排列", shape.triangledown, location.abovebar, color.red, size=size.small)

// 波动率提醒
plotshape(high_volatility and not high_volatility[1], "高波动率", shape.diamond, location.abovebar, color.orange, size=size.small)
plotshape(low_volatility and not low_volatility[1], "低波动率", shape.circle, location.belowbar, color.gray, size=size.small)

// 警报条件
alertcondition(ema_bullish and not ema_bullish[1], "EMA多头排列", "EMA线形成多头排列")
alertcondition(ema_bearish and not ema_bearish[1], "EMA空头排列", "EMA线形成空头排列")
alertcondition(high_volatility and not high_volatility[1], "高波动率警报", "今日波动率超过日均20%")
alertcondition(low_volatility and not low_volatility[1], "低波动率警报", "今日波动率低于日均50%")