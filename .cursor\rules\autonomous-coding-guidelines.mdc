---
description: 
globs: 
alwaysApply: false
---
# 自主编程指导原则（高复杂度任务适用）

## 任务规划与透明汇报

### 自动规划生成
- **精确理解**: 准确的需求理解和范围定义
- **技术栈选择**: 必须通过Context7验证其适用性和最新状态
- **架构设计**: 初步架构草案和关键组件识别  
- **模块划分**: 关键功能模块划分和实现优先级排序

### 强制汇报节点
- **前后对比**: 技术栈选择和核心架构设计决策
- **里程碑**: 每个主要功能模块或重要里程碑完成后
- **技术挑战**: 遇到AI无法自主解决的技术问题时
- **依赖变更**: 引入计划外的外部依赖或服务时
- **需求冲突**: AI对需求理解发生重大调整或发现歧义冲突时

## Context7支持的自主决策

### 基于证据的技术选择
- **验证支持**: 所有重要的自主技术决策必须有明确的Context7验证支持
- **决策透明**: 记录选择理由和验证摘要，包括版本、日期、关键特性
- **风险评估**: 重大决策前评估潜在风险并向用户声明

### 决策文档格式
```typescript
// 技术选择: 库名@版本 (验证日期)
// 选择理由: 具体推理
// Context7验证: 验证摘要
// 风险评估: 潜在风险（如有）
```

## 依赖管理与错误恢复

### 依赖管理
- **主动识别**: 主动识别和声明代码依赖关系
- **兼容性验证**: 引入或更新依赖时，通过Context7验证兼容性、安全性和最新版本
- **标准工具**: 鼓励使用项目标准工具进行依赖管理

### 错误恢复
- **基础错误处理**: 实现基础的错误处理和回退机制
- **复杂错误升级**: 无法自主解决的复杂错误，立即停止并清晰报告问题
- **策略遵循**: 遵循用户确认的错误处理策略

## 自动化质量行动（可选增强）

### 最佳实践遵循
- **最新标准**: 生成的代码自动遵循相关语言和框架的最新最佳实践（Context7验证）
- **代码模式**: 使用已建立的、经过验证的模式，避免实验性方法

### 文档与注释
- **标准文档**: 为关键代码自动生成符合标准的文档注释
- **逻辑解释**: 为复杂逻辑添加清晰的行内注释
- **架构注释**: 记录重要的设计决策和权衡

### 安全性能
- **安全扫描**: 基于Context7安全知识库，进行基础代码安全检查
- **性能意识**: 考虑基本性能影响，避免明显瓶颈
- **优化标准**: 核心功能稳定后，进行非破坏性、收益明确的性能优化（重大优化需通知用户）

## 自我管理标准
- **范围纪律**: 严格遵守定义的任务范围，避免功能蔓延
- **进度跟踪**: 保持清晰的进度跟踪和里程碑文档
- **质量门禁**: 在主要开发阶段实施质量检查点
- **回滚准备**: 如果出现问题，保持回滚更改的能力

