from typing import Dict, List, <PERSON>ple
import pandas as pd
import numpy as np
from sklearn.linear_model import TheilSenRegressor
from scipy.stats import gaussian_kde
from scipy.signal import argrelextrema
import logging
from utils.time_utils import timeit

# 设置日志
logger = logging.getLogger(__name__)

class PatternAnalyzer:
    """
    技术形态分析器 - 平台无关的核心分析逻辑（做空版本）
    
    该类实现了各种技术分析形态的识别，包括：
    - 趋势线跌破
    - 顶部箱体跌破
    - 成交量放大
    - 收敛三角形
    - 头肩顶形态
    - K线组合
    - 回调卖点
    - 倒老鸭头形态
    - 倒茶杯带柄形态
    - 破顶翻形态
    - 亚当理论
    """
    
    def __init__(self):
        self.min_pattern_bars = 15
        self.error_margin = 0.001
        self.pullback_bars = 3

    def analyze_trend_line_breakout_short(self, data: pd.DataFrame) -> Tuple[bool, str]:
        """趋势线反转（做空）分析"""
        try:
            if len(data) < 20:
                return False, ""
            
            recent_data = data.tail(20).copy()
            current_price = recent_data['close'].iloc[-1]
            
            # 使用K线高点拟合趋势线（与做多方法不同）
            X = np.arange(len(recent_data)).reshape(-1, 1)
            y = recent_data['high'].values
            
            model = TheilSenRegressor(random_state=42)
            model.fit(X, y)
            
            trend_line = model.predict(X)
            trend_value = trend_line[-1]
            slope = model.coef_[0]
            score = model.score(X, y)
            
            info = (
                f"最新收盘价: {current_price:.8f}, "
                f"趋势线值: {trend_value:.8f}, "
                f"斜率: {slope:.8f}, "
                f"R方: {score:.4f}, "
                f"使用方法: Theil-Sen"
            )
            
            # 做空逻辑：收盘价低于趋势线，且趋势向下
            is_breakout = (
                current_price < trend_value and
                slope < 0 and
                score > -0.5
            )
            
            return is_breakout, info
            
        except Exception as e:
            logger.error(f"趋势线反转分析出错: {str(e)}")
            return False, ""

    def analyze_resistance_box_breakout(self, data: pd.DataFrame) -> Tuple[bool, str]:
        """
        顶部箱体跌破分析 - 使用KDE优化（做空信号）
        """
        try:
            # 获取最近80根K线的收盘价
            recent_data = data['close'].values[-80:]
            if len(recent_data) < 5:
                return False, "数据点数不足"

            # 使用KDE识别价格密集区域
            kde = gaussian_kde(recent_data)
            price_range = np.linspace(min(recent_data), max(recent_data), 100)
            density = kde(price_range)
            
            # 找到密度最高的区域作为箱体范围
            high_density_mask = density > np.percentile(density, 70)  # 取密度前30%的区域
            box_bottom = price_range[high_density_mask][0]
            box_top = price_range[high_density_mask][-1]
            
            # 使用局部极值点计算有效触点
            extrema_idx = np.concatenate([
                argrelextrema(recent_data, np.less)[0],    # 局部最小值
                argrelextrema(recent_data, np.greater)[0]  # 局部最大值
            ])
            touch_points = sum(1 for idx in extrema_idx 
                            if box_bottom * (1 - self.error_margin) <= recent_data[idx] <= box_top * (1 + self.error_margin))

            latest_close = data['close'].iloc[-1]
            
            # 判断是否跌破（做空信号）：价格跌破箱体下边界外一定幅度且触点数量足够
            result = latest_close < box_bottom * (1 - self.error_margin) and touch_points >= 3
            
            # 计算当前价格的密度得分
            density_score = kde(latest_close)[0] / np.max(density)
            
            # 格式化输出时保持完整精度
            info = (
                f"箱体范围: {box_bottom:.8f} - {box_top:.8f}, "
                f"触点数量: {touch_points}, "
                f"价格密度: {density_score:.2f}"
            )
            
            return result, info
            
        except Exception as e:
            logger.error(f"底部箱体跌破分析出错: {str(e)}")
            return False, ""

    def analyze_volume_surge(self, data: pd.DataFrame) -> Tuple[bool, str]:
        """
        成交量放大分析（做空信号）

        检查是否出现放量阴线，通过比较当前成交量与前期平均成交量。

        参数:
            data (pd.DataFrame): 包含OHLCV数据的DataFrame
                必须包含的列：'open', 'close', 'tick_volume'

        返回:
            Tuple[bool, str]: (是否放量, 详细信息)
                - 第一个元素表示是否出现放量
                - 第二个元素包含详细的分析信息

        分析逻辑:
        1. 检查最新K线是否为阴线
        2. 比较当前成交量与前5根K线的平均成交量
        3. 当前成交量超过1.5倍平均值视为放量
        """
        try:
            # 获取最近6根K线数据
            recent_data = data[-6:]
            if len(recent_data) < 6:
                return False, ""

            # 检查是否是阴线
            latest_close = recent_data['close'].iloc[-1]
            latest_open = recent_data['open'].iloc[-1]
            if latest_close >= latest_open:
                return False, ""

            # 计算成交量变化
            latest_volume = recent_data['tick_volume'].iloc[-1]
            previous_volumes = recent_data['tick_volume'].iloc[-6:-1]
            average_previous_volume = previous_volumes.mean()

            # 判断是否放量（放量为最新成交量超过1.5倍平均值）
            result = latest_volume > 1.5 * average_previous_volume
            info = (f"当前成交量: {latest_volume}, "
                    f"平均成交量: {average_previous_volume:.2f}, "
                    f"放大倍数: {latest_volume/average_previous_volume:.2f}")
            return result, info

        except Exception as e:
            logger.error(f"成交量分析出错: {str(e)}")
            return False, ""

    def analyze_converging_triangle(self, data: pd.DataFrame) -> Tuple[bool, str]:
        """
        收敛三角形形态分析（做空信号）

        使用改进的算法检测收敛三角形形态，并判断价格是否跌破下轨趋势线，作为顶部反转的做空信号。

        参数:
            data (pd.DataFrame): 包含OHLCV数据的DataFrame
                必须包含的列：'high', 'low', 'close'

        返回:
            Tuple[bool, str]: (是否形成有效顶部反转形态, 详细信息)
                - 第一个元素表示是否形成顶部反转信号
                - 第二个元素包含详细的分析信息
        """
        try:
            recent_data = data[-60:]  # 保持60根K线窗口
            if len(recent_data) < self.min_pattern_bars:
                return False, ""

            highs = recent_data['high'].values
            lows = recent_data['low'].values
            
            # 1. 识别关键高低点（保持原有思路）
            high_peaks = []
            low_troughs = []
            window_size = 5
            
            for i in range(window_size, len(recent_data) - window_size):
                local_high_window = highs[i - window_size:i + window_size + 1]
                if highs[i] == max(local_high_window):
                    high_peaks.append((i, highs[i]))
                
                local_low_window = lows[i - window_size:i + window_size + 1]
                if lows[i] == min(local_low_window):
                    low_troughs.append((i, lows[i]))

            if len(high_peaks) >= 4 and len(low_troughs) >= 4:
                # 2. 选取最近的4个关键点
                recent_highs = high_peaks[-4:]
                recent_lows = low_troughs[-4:]
                
                # 3. 验证高低点趋势
                # 对于顶部反转，要求高点呈上升趋势（价格在顶部区域整理后逐步回落，出现反转前会有短暂上冲）
                # 低点则应呈下降趋势
                high_prices = [h[1] for h in recent_highs]
                low_prices = [l[1] for l in recent_lows]
                if not (all(high_prices[i] < high_prices[i+1] for i in range(len(high_prices)-1)) and
                        all(low_prices[i] > low_prices[i+1] for i in range(len(low_prices)-1))):
                    return False, ""
                
                # 4. 验证点位时间间隔均匀性
                high_intervals = [recent_highs[i+1][0] - recent_highs[i][0] for i in range(len(recent_highs)-1)]
                low_intervals = [recent_lows[i+1][0] - recent_lows[i][0] for i in range(len(recent_lows)-1)]
                avg_interval = (sum(high_intervals) + sum(low_intervals)) / (len(high_intervals) + len(low_intervals))
                if any(abs(interval - avg_interval) > avg_interval * 0.5 for interval in high_intervals + low_intervals):
                    return False, ""
                
                # 5. 计算趋势线：使用Theil-Sen回归拟合关键点
                high_x = np.array([p[0] for p in recent_highs]).reshape(-1, 1)
                high_y = np.array([p[1] for p in recent_highs])
                low_x = np.array([p[0] for p in recent_lows]).reshape(-1, 1)
                low_y = np.array([p[1] for p in recent_lows])
                
                high_reg = TheilSenRegressor(random_state=0)
                low_reg = TheilSenRegressor(random_state=0)
                high_reg.fit(high_x, high_y)
                low_reg.fit(low_x, low_y)
                
                high_slope = high_reg.coef_[0]
                low_slope = low_reg.coef_[0]
                
                # 6. 计算形态特征
                high_score = high_reg.score(high_x, high_y)
                low_score = low_reg.score(low_x, low_y)
                
                start_width = abs(recent_highs[0][1] - recent_lows[0][1])
                end_width = abs(recent_highs[-1][1] - recent_lows[-1][1])
                width_ratio = end_width / start_width if start_width != 0 else 1.0
                
                # 7. 判断图形特征满足条件
                pattern_ok = (
                    low_slope < -0.0002 and         # 低点趋势向下
                    high_slope > 0.0002 and          # 高点趋势向上
                    width_ratio < 0.6 and
                    high_score > 0.7 and
                    low_score > 0.7 and
                    abs(low_slope) > abs(high_slope) * 0.5 and
                    abs(low_slope) < abs(high_slope) * 2
                )
                
                if pattern_ok:
                    # 8. 判断做空跌破：当收盘价跌破拟合的下轨趋势线一定幅度时，视为顶部反转信号
                    last_idx = len(recent_data) - 1
                    predicted_low = low_reg.predict([[last_idx]])[0]
                    latest_close = recent_data['close'].iloc[-1]
                    breakout = latest_close < predicted_low * (1 - self.error_margin)
                    
                    result = breakout
                    info = (
                        f"收敛比: {width_ratio:.2f}, "
                        f"趋势线拟合度: {min(high_score, low_score):.2f}, "
                        f"下轨预测: {predicted_low:.8f}, "
                        f"最新收盘: {latest_close:.8f}"
                    )
                    return result, info

            return False, ""
            
        except Exception as e:
            logger.error(f"收敛三角形分析出错: {str(e)}")
            return False, ""

    def analyze_head_shoulders_top(self, data: pd.DataFrame) -> Tuple[bool, str]:
        """
        头肩顶形态分析 - 简化版

        主要判断:
        1. 有三个高点
        2. 中间高点为头部且最高
        3. 左右肩大致对称
        4. 有基本的回落确认
        """
        try:
            recent_data = data[-30:]  # 只看最近30根K线
            if len(recent_data) < 10:  # 至少需要10根K线
                return False, ""

            highs = recent_data['high'].values
            lows = recent_data['low'].values
            volumes = recent_data['tick_volume'].values
            high_peaks = []
            
            # 寻找高点 - 使用较小的窗口
            window_size = 3  # 左右各看3根K线
            for i in range(window_size, len(highs) - window_size):
                local_window = highs[i - window_size:i + window_size + 1]
                if highs[i] == max(local_window):
                    # 记录高点信息，同时记录后续小幅回落的最低价作为回落确认
                    high_peaks.append({
                        'index': i,
                        'price': highs[i],
                        'volume': volumes[i],
                        'low_after': min(lows[i:i + window_size])
                    })

            if len(high_peaks) >= 3:
                # 选取最近三个高点作为左肩、头部、右肩
                last_three = high_peaks[-3:]
                left_shoulder = last_three[0]
                head = last_three[1]
                right_shoulder = last_three[2]
                
                # 1. 头部必须最高
                if not (head['price'] > left_shoulder['price'] and head['price'] > right_shoulder['price']):
                    return False, ""
                
                # 2. 计算肩部间高度差（要求左右肩高度接近）
                shoulders_diff = abs(left_shoulder['price'] - right_shoulder['price'])
                
                # 3. 计算头肩深度：以头部与较高肩部之差占头部价格的比例
                pattern_depth = (head['price'] - max(left_shoulder['price'], right_shoulder['price'])) / head['price']
                
                # 4. 计算肩部回落比例（确认下跌动能）
                left_pullback = 1 - left_shoulder['low_after'] / left_shoulder['price']
                right_pullback = 1 - right_shoulder['low_after'] / right_shoulder['price']
                
                # 简化的判断条件
                is_valid = (
                    pattern_depth > 0.003 and             # 形态深度至少0.3%
                    shoulders_diff < min(left_shoulder['price'], right_shoulder['price']) * 0.02 and  # 肩部差异小于2%
                    min(left_pullback, right_pullback) > 0.001  # 确保有最小回落（0.1%）
                )
                
                if is_valid:
                    info = (
                        f"形态深度: {pattern_depth:.3f}, "
                        f"肩部差异: {shoulders_diff:.4f}, "
                        f"左肩回落: {left_pullback:.3f}, "
                        f"右肩回落: {right_pullback:.3f}"
                    )
                    return True, info

            return False, ""
        
        except Exception as e:
            logger.error(f"头肩顶分析出错: {str(e)}")
            return False, ""

    #以下是旧的分析方法，暂时保留
    # def analyze_head_shoulders_top(self, data: pd.DataFrame) -> Tuple[bool, str]:
    #     """
    #     头肩底形态分析
        
    #     使用改进的算法检测头肩底形态，包括成交量确认。
        
    #     参数:
    #         data (pd.DataFrame): 包含OHLCV数据的DataFrame
    #             必须包含的列：'low', 'tick_volume'
        
    #     返回:
    #         Tuple[bool, str]: (是否形成头肩底, 详细信息)
    #             - 第一个元素表示是否形成有效的头肩底
    #             - 第二个元素包含详细的分析信息
        
    #     分析逻辑:
    #     1. 识别关键低点
    #     2. 验证时间间隔均匀性
    #     3. 验证形态特征和深度
    #     4. 确认成交量特征
    #     """
    #     try:
    #         recent_data = data[-50:]
    #         if len(recent_data) < self.min_pattern_bars:
    #             return False, ""

    #         lows = recent_data['low'].values
    #         volumes = recent_data['tick_volume'].values
    #         low_peaks = []
            
    #         # 1. 识别低点
    #         window_size = 3
    #         for i in range(window_size, len(lows)-window_size):
    #             local_window = lows[i-window_size:i+window_size+1]
    #             if lows[i] == min(local_window):
    #                 low_peaks.append((i, lows[i]))

    #         if len(low_peaks) >= 3:
    #             # 2. 选取最近三个低点
    #             last_three = low_peaks[-3:]
    #             time_indexes = [p[0] for p in last_three]
    #             left_shoulder, head, right_shoulder = [p[1] for p in last_three]
                
    #             # 3. 验证时间隔
    #             left_span = time_indexes[1] - time_indexes[0]
    #             right_span = time_indexes[2] - time_indexes[1]
    #             if abs(left_span - right_span) > min(left_span, right_span) * 0.5:
    #                 return False, ""
                
    #             # 4. 计算形态特征
    #             head_to_left = abs(head - left_shoulder)
    #             head_to_right = abs(head - right_shoulder)
    #             shoulders_diff = abs(left_shoulder - right_shoulder)
    #             pattern_depth = min(head_to_left, head_to_right) / head
                
    #             # 5. 验证成交量
    #             head_volume = volumes[time_indexes[1]]
    #             shoulders_volume = (volumes[time_indexes[0]] + volumes[time_indexes[2]]) / 2
    #             volume_pattern = head_volume > shoulders_volume * 1.2
                
    #             # 6. 综合判断
    #             is_valid = (
    #                 head < left_shoulder and 
    #                 head < right_shoulder and 
    #                 shoulders_diff < min(head_to_left, head_to_right) * 0.3 and
    #                 pattern_depth > 0.005 and
    #                 volume_pattern
    #             )
                
    #             if is_valid:
    #                 info = (
    #                     f"形态深度: {pattern_depth:.3f}, "
    #                     f"肩部差异: {shoulders_diff/min(head_to_left, head_to_right):.2f}, "
    #                     f"时间均匀性: {min(left_span, right_span)/max(left_span, right_span):.2f}"
    #                 )
    #                 return True, info

    #         return False, ""
            
    #     except Exception as e:
    #         logger.error(f"头肩底分析出错: {str(e)}")
    #         return False, ""

    def analyze_k_line_pattern(self, data: pd.DataFrame) -> Tuple[bool, str]:
        """
        K线组合形态分析（做空信号）

        分析K线组合形态，特别关注大阴线及其特征。

        参数:
            data (pd.DataFrame): 包含OHLCV数据的DataFrame
                必须包含的列：'open', 'close', 'high', 'low', 'tick_volume'

        返回:
            Tuple[bool, str]: (是否形成有效K线组合, 详细信息)
                - 第一个元素表示是否形成有效的K线组合
                - 第二个元素包含详细的分析信息

        分析逻辑:
        1. 分析最近8根K线的走势
        2. 验证当前K线的实体和影线特征（必须为阴线）
        3. 确认成交量放大情况
        4. 考虑前期走势背景（要求前期为上涨行情）
        """
        try:
            recent_data = data[-8:]  # 增加到8根K线以便分析前期走势
            if len(recent_data) < 8:
                return False, ""

            # 1. 基础数据准备
            latest = recent_data.iloc[-1]
            latest_open = latest['open']
            latest_close = latest['close']
            latest_high = latest['high']
            latest_low = latest['low']
            latest_volume = latest['tick_volume']

            # 2. 计算K线特征（需为阴线）
            current_body = latest_open - latest_close
            if current_body <= 0:
                return False, ""  # 不是阴线

            body_size = current_body
            upper_shadow = latest_high - latest_open
            lower_shadow = latest_close - latest_low
            
            # 3. 分析前期走势（要求为上涨行情）
            previous_closes = recent_data['close'].iloc[-8:-1]
            previous_trend = (previous_closes.iloc[-1] - previous_closes.iloc[0]) / previous_closes.iloc[0]
            
            # 4. 计算相对大小
            previous_bodies = (recent_data['open'].iloc[-8:-1] - recent_data['close'].iloc[-8:-1]).abs()
            avg_body = previous_bodies.mean()
            body_ratio = body_size / (latest_high - latest_low)
            
            # 5. 成交量分析
            previous_volumes = recent_data['tick_volume'].iloc[-8:-1]
            avg_volume = previous_volumes.mean()
            volume_ratio = latest_volume / avg_volume
            
            # 6. 综合判断：条件调整为寻找大阴线，并要求前期为上涨行情（作为做空反转信号）
            is_valid = (
                body_ratio >= 0.6 and                # 实体占比要求至少60%
                body_size > 1.5 * avg_body and         # 当前阴线实体比前期均值大
                upper_shadow < body_size * 0.3 and     # 上影线较短
                lower_shadow < body_size * 0.3 and     # 下影线较短
                volume_ratio > 1.2 and                 # 成交量放大
                previous_trend > 0                    # 前期为上涨行情
            )
            
            if is_valid:
                info = (
                    f"实体比例: {body_ratio:.2f}, "
                    f"量比: {volume_ratio:.2f}, "
                    f"影线比例: {max(upper_shadow, lower_shadow)/body_size:.2f}, "
                    f"前期涨幅: {previous_trend*100:.2f}%"
                )
                return True, info

            return False, ""
            
        except Exception as e:
            logger.error(f"K线组合分析出错: {str(e)}")
            return False, ""
        
    def analyze_pullback_short_entry(self, data: pd.DataFrame) -> Tuple[bool, str]:
        """
        回调卖点分析（做空信号）
    
        分析价格回调到斐波那契回调位的卖点机会，适用于下跌趋势中的回调顶部。
    
        参数:
            data (pd.DataFrame): 包含OHLCV数据的DataFrame
                必须包含的列：'open', 'close', 'high', 'low'
        
        返回:
            Tuple[bool, str]: (是否形成回调卖点, 详细信息)
                - 第一个元素表示是否形成有效的回调卖点
                - 第二个元素包含详细的分析信息
    
        分析逻辑:
        1. 确定前期下跌趋势：找出前期高点（起跌点）与后期低点（跌破低点）
        2. 计算关键斐波那契区域（382%、500%、618%回调位）
        3. 检查最新K线是否在回调区域内（顶部区域）
        4. 确认回落跌破：要求当前K线收盘价低于前一根K线的低点
        5. 额外要求：成交量放大或K线形态确认
        """
        try:
            if len(data) < 20:
                return False, ""
            
            recent_data = data.tail(20)
            
            # 1. 确定前期下跌趋势：从前期找出起始高点与回调低点
            start_point = recent_data['high'].iloc[:-5].max()
            breakout_low = recent_data['low'].iloc[-5:-1].min()
            
            # 必须满足是下跌趋势
            if start_point <= breakout_low:
                return False, ""
            
            downtrend_range = start_point - breakout_low
            
            # 2. 计算斐波那契回调位（从高点开始的回调上部区域）
            fib_382 = breakout_low + downtrend_range * 0.382
            fib_500 = breakout_low + downtrend_range * 0.500
            fib_618 = breakout_low + downtrend_range * 0.618
            
            # 3. 获取最新K线及前一根K线
            current_bar = recent_data.iloc[-1]
            prev_bar = recent_data.iloc[-2]
            
            # 检查是否处于回调顶部区域：以最新K线的高点或开盘价落在fib_382 ~ fib_618之间
            in_fib_zone = (
                (current_bar['high'] >= fib_382 * 0.99 and current_bar['high'] <= fib_618 * 1.01) or
                (current_bar['open'] >= fib_382 * 0.99 and current_bar['open'] <= fib_618 * 1.01)
            )
            
            # 4. 确认回落跌破：最新收盘价低于前一根K线的低点
            stands_firm = current_bar['close'] < prev_bar['low']
            
            # 5. 检查是否有放量或K线组合确认（使用之前的做空版本函数）
            has_volume = self.analyze_volume_surge(recent_data.tail(6))[0]
            has_k_pattern = self.analyze_k_line_pattern(recent_data.tail(6))[0]
            
            if in_fib_zone and stands_firm and (has_volume or has_k_pattern):
                retracement = (current_bar['high'] - current_bar['close']) / downtrend_range
                info = (
                    f"回调位置: {retracement:.2f}, "
                    f"回落确认: {'是' if stands_firm else '否'}, "
                    f"成交量确认: {'是' if has_volume else '否'}, "
                    f"K线确认: {'是' if has_k_pattern else '否'}"
                )
                return True, info
            
            return False, ""
        
        except Exception as e:
            logger.error(f"回调卖点分析出错: {str(e)}")
            return False, ""

    def analyze_inverted_duck_head(self, data: pd.DataFrame) -> Tuple[bool, str]:
        """
        倒老鸭头形态分析（做空信号）
    
        分析顶部回调反转形态（倒老鸭头），主要识别：
        1. 前段顶部（左侧峰值，视为形态起点）
        2. 中段形成明显回调低点（头部）
        3. 后段部分反弹但未能恢复到原高（颈部）
        4. 颈部回升幅度占回调深度的比例在50%-70%
        
        参数:
            data (pd.DataFrame): 包含OHLCV数据的DataFrame
                必须包含的列：'high', 'low'
        
        返回:
            Tuple[bool, str]: (是否形成倒老鸭头形态, 详细信息)
                - 第一个元素表示是否形成有效的做空形态
                - 第二个元素包含详细的分析信息
        """
        try:
            if len(data) < 30:
                return False, ""
            
            recent_data = data.tail(30)
            
            # 1. 找到起始顶部（顶嘴）：前15根K线内最高点
            left_segment = recent_data.iloc[:-15]
            start_point = left_segment['high'].max()
            start_index = left_segment['high'].idxmax()
            
            # 2. 找到回调低点（头部）：中间区间（倒数15根至倒数5根）内最低点
            mid_segment = recent_data.iloc[-15:-5]
            head_low = mid_segment['low'].min()
            head_index = mid_segment['low'].idxmin()
            
            # 3. 找到反弹高点（颈部）：最后5根K线内最高点
            right_segment = recent_data.iloc[-5:]
            neck_high = right_segment['high'].max()
            neck_index = right_segment['high'].idxmax()
            
            # 4. 检查时间顺序：必须依次为 顶嘴 -> 头部 -> 颈部
            if not (start_index < head_index < neck_index):
                return False, ""
            
            # 5. 判断形态比例
            # 头部回调深度
            head_depth = start_point - head_low
            if head_depth <= 0:
                return False, ""
            # 颈部反弹幅度
            neck_retracement = neck_high - head_low
            # 颈部未能回到顶嘴水平
            if neck_high >= start_point:
                return False, ""
            ratio = neck_retracement / head_depth
            
            is_duck_head = (0.5 <= ratio <= 0.7)
            
            # 6. 辅助确认：检查成交量放大或K线组合（做空版）是否确认信号
            has_volume = self.analyze_volume_surge(recent_data.tail(6))[0]
            has_k_pattern = self.analyze_k_line_pattern(recent_data.tail(6))[0]
            
            if is_duck_head and (has_volume or has_k_pattern):
                info = (
                    f"顶嘴: {start_point:.2f}, "
                    f"头部: {head_low:.2f}, "
                    f"颈部: {neck_high:.2f}, "
                    f"回调比例: {ratio:.2f}"
                )
                return True, info
            
            return False, ""
            
        except Exception as e:
            logger.error(f"倒老鸭头形态分析出错: {str(e)}")
            return False, ""

    def analyze_inverted_cup_handle   (self, data: pd.DataFrame) -> Tuple[bool, str]:
        """
        倒茶杯带柄形态分析（做空信号）
    
        分析顶部回调反转的倒茶杯带柄形态，
        识别过程：
        1. 识别左侧杯底（最低点）
        2. 确认杯顶位置（中段最高点）
        3. 识别右侧杯底（另一最低点）
        4. 检查手柄峰（反弹但未能冲破杯顶）
        5. 验证整体形态的比例关系，及辅助成交量或K线组合确认
    
        参数:
            data (pd.DataFrame): 包含OHLCV数据的DataFrame
                必须包含的列：'high', 'low'
        
        返回:
            Tuple[bool, str]: (是否形成倒茶杯带柄形态, 详细信息)
        """
        try:
            if len(data) < 40:
                return False, ""
            
            recent_data = data.tail(40)
            
            # 1. 找到左侧杯底（最低点）
            left_low = recent_data['low'].iloc[:-25].min()
            left_index = recent_data['low'].iloc[:-25].idxmin()
            
            # 2. 找到杯顶（中段最高点）
            cup_high = recent_data['high'].iloc[-25:-10].max()
            cup_index = recent_data['high'].iloc[-25:-10].idxmax()
            
            # 3. 找到右侧杯底（中后段最低点）
            right_low = recent_data['low'].iloc[-10:-5].min()
            right_index = recent_data['low'].iloc[-10:-5].idxmin()
            
            # 4. 找到手柄峰（最后5根K线的最高点）
            handle_high = recent_data['high'].iloc[-5:].max()
            handle_index = recent_data['high'].iloc[-5:].idxmax()
            
            # 验证关键点位顺序：左杯底 -> 杯顶 -> 右杯底 -> 手柄峰
            if not (left_index < cup_index < right_index < handle_index):
                return False, ""
            
            # 5. 计算杯体与手柄比例
            # 定义杯底均价（两侧杯底的平均水平）作为参考
            rim_level = (left_low + right_low) / 2
            cup_depth = cup_high - rim_level  # 杯体高度
            handle_gap = handle_high - rim_level  # 手柄反弹幅度
            
            if cup_depth <= 0 or handle_gap <= 0:
                return False, ""
            
            # 手柄峰不应冲破杯顶（否则无顶部回调意义）
            if handle_high >= cup_high:
                return False, ""
            
            # 检查杯体两侧的对称性（相对误差小于10%）
            symmetry_ok = abs(left_low - right_low) / min(left_low, right_low) < 0.1
            
            # 检查杯深比例（杯深相对于杯顶至少20%）
            cup_ratio_ok = (cup_depth / cup_high) >= 0.2
            
            # 检查手柄反弹幅度占杯深的比例，要求在50%-70%之间（顶部未能恢复）
            handle_ratio_ok = 0.5 <= (handle_gap / cup_depth) <= 0.7
            
            # 检查手柄宽度：手柄所在区间宽度应窄于杯宽的一半
            cup_width = right_index - left_index
            handle_width = handle_index - right_index
            width_ok = handle_width < cup_width * 0.5
            
            is_inverted_cup_handle = symmetry_ok and cup_ratio_ok and handle_ratio_ok and width_ok
            
            # 6. 辅助信号：成交量或K线组合确认（做空版本）
            has_volume = self.analyze_volume_surge(recent_data.tail(6))[0]
            has_k_pattern = self.analyze_k_line_pattern(recent_data.tail(6))[0]
            
            if is_inverted_cup_handle and (has_volume or has_k_pattern):
                info = (
                    f"左杯底: {left_low:.2f}, "
                    f"杯顶: {cup_high:.2f}, "
                    f"右杯底: {right_low:.2f}, "
                    f"手柄峰: {handle_high:.2f}, "
                    f"杯深比例: {(cup_depth/cup_high):.2f}, "
                    f"手柄比例: {(handle_gap/cup_depth):.2f}"
                )
                return True, info
            
            return False, ""
            
        except Exception as e:
            logger.error(f"倒茶杯带柄形态分析出错: {str(e)}")
            return False, ""

    def analyze_break_top_reversal(self, data: pd.DataFrame) -> Tuple[bool, str]:
        """
        破顶翻形态分析（做空反转机会）
        
        分析破顶翻形态，当市场价格在上升过程中创出关键高点，
        随后迅速出现反转阴线（最高高点之后出现明显回落）时视为卖出信号。
        
        参数:
            data (pd.DataFrame): 包含OHLCV数据的DataFrame，
                必须包含的列：'open', 'close', 'high', 'low', 'tick_volume'
        
        返回:
            Tuple[bool, str]: (是否形成破顶翻形态, 详细信息)
                - 第一个元素表示是否形成有效的破顶翻形态
                - 第二个元素包含详细的分析信息
        """
        try:
            # 至少需要20根K线用于识别顶部形态
            if len(data) < 20:
                return False, "数据量不足"
    
            recent_data = data.tail(20).reset_index(drop=True)
            
            # 1. 找出这段时间内的最高高点及其位置（视为“破顶”）
            highest_high = recent_data['high'].max()
            highest_pos = int(recent_data['high'].idxmax())
            
            # 若最高高点处于最后一个K线，则无法判断后续反转
            if highest_pos >= len(recent_data) - 1:
                return False, "未能形成反转信号"
    
            reversal_bar = recent_data.iloc[highest_pos + 1]
            
            # 2. 判断反转K线是否为阴线且有足够下跌（回落幅度至少0.5%）
            reversal_valid = (
                reversal_bar['close'] < reversal_bar['open'] and
                reversal_bar['close'] <= highest_high * 0.995
            )
            
            # 3. 结合成交量确认：计算最近20根K线平均成交量
            avg_volume = recent_data['tick_volume'].mean()
            volume_valid = reversal_bar['tick_volume'] > avg_volume * 1.2
            
            # 4. 综合判断：最高高点后出现反转阴线且下跌幅度和量能均满足要求
            is_valid = reversal_valid and volume_valid
            
            info = (
                f"最高高点: {highest_high:.8f} (位置: {highest_pos}),\n"
                f"反转K线: 开:{reversal_bar['open']:.8f}, 收:{reversal_bar['close']:.8f},\n"
                f"回落幅度: {(1 - reversal_bar['close']/highest_high)*100:.2f}%,\n"
                f"反转量能: {reversal_bar['tick_volume']:.2f} (均量: {avg_volume:.2f})"
            )
            
            return is_valid, info
            
        except Exception as e:
            logger.error(f"破顶翻形态分析出错: {str(e)}")
            return False, f"分析出错: {str(e)}"

    def analyze_support_resistance(self, data: pd.DataFrame, min_strength: float = 0.3) -> Tuple[bool, str]:
        """
        分析支撑阻力区间
        
        参数:
            data: OHLCV数据
            min_strength: 最小强度阈值，默认0.3
            
        强度等级:
            > 0.7: 非常强
            0.5-0.7: 强
            0.3-0.5: 中等
            < 0.3: 弱
        """
        try:
            if len(data) < 150:
                return False, "数据不足150根K线，无法进行支撑阻力分析"
                
            current_price = data['close'].iloc[-1]
            
            # 计算价格区间 - 使用收盘价范围
            price_range = data['close'].max() - data['close'].min()
            zone_width = price_range * 0.001  # 保持0.1%的区间宽度
            
            # 创建价格区间 - 主要关注收盘价
            price_zones = {}
            for i, row in enumerate(data.itertuples()):
                # 以收盘价为主，其他价格权重降低
                prices = [
                    (row.close, 1.0),  # 收盘价权重1.0
                    (row.open, 0.5),   # 开盘价权重0.5
                    (row.high, 0.3),   # 最高价权重0.3
                    (row.low, 0.3)     # 最低价权重0.3
                ]
                
                for price, weight in prices:
                    zone_key = round(price / zone_width) * zone_width
                    
                    if zone_key not in price_zones:
                        price_zones[zone_key] = {
                            'volume': 0,
                            'touches': 0,
                            'close_touches': 0,  # 新增：收盘价触及次数
                            'last_touch': i,
                            'price_range': (zone_key - zone_width/2, zone_key + zone_width/2)
                        }
                    
                    price_zones[zone_key]['volume'] += row.tick_volume * weight
                    price_zones[zone_key]['touches'] += weight
                    if price == row.close:  # 如果是收盘价触及
                        price_zones[zone_key]['close_touches'] += 1
                    price_zones[zone_key]['last_touch'] = i

            # 计算区间强度 - 加入收盘价触及因子
            for zone in price_zones.values():
                # 时间衰减调整 - 使用更合理的衰减
                days_passed = len(data) - zone['last_touch']
                time_factor = 1 / (1 + np.log1p(days_passed * 0.1))
                
                # 成交量因子
                max_volume = max(z['volume'] for z in price_zones.values())
                volume_factor = zone['volume'] / max_volume
                
                # 触及次数因子 - 加入收盘价触及权重
                max_touches = max(z['touches'] for z in price_zones.values())
                touch_factor = zone['touches'] / max_touches
                
                # 收盘价触及因子
                max_close_touches = max(z['close_touches'] for z in price_zones.values())
                close_touch_factor = zone['close_touches'] / max(max_close_touches, 1)
                
                # 新的强度计算公式
                zone['strength'] = (
                    volume_factor * 0.3 +
                    touch_factor * 0.3 +
                    close_touch_factor * 0.3 +
                    time_factor * 0.1
                )
                zone['strength'] = round(zone['strength'], 3)

            # 筛选重要区间时增加强度过滤
            support_zones = []
            resistance_zones = []
            
            # 先收集所有区间
            for price, zone in price_zones.items():
                if zone['strength'] < min_strength:
                    continue
                    
                if price < current_price:
                    support_zones.append({
                        'price': price,
                        'range': zone['price_range'],
                        'strength': round(zone['strength'], 3)
                    })
                elif price > current_price:
                    resistance_zones.append({
                        'price': price,
                        'range': zone['price_range'],
                        'strength': round(zone['strength'], 3)
                    })
            
            # 分别对支撑位和阻力位按照价格排序
            support_zones.sort(key=lambda x: x['price'], reverse=True)  # 支撑位从高到低
            resistance_zones.sort(key=lambda x: x['price'])  # 阻力位从低到高
            
            # 只保留最强的两个区间
            support_zones = support_zones[:2]
            resistance_zones = resistance_zones[:2]
            
            # 如果没有足够强的区间
            if not (support_zones or resistance_zones):
                return False, "未找到强度大于0.3的支撑阻力区间"
                
            # 格式化输出信息
            resistance_info = []
            support_info = []
            for i, z in enumerate(resistance_zones):
                strength_level = (
                    "非常强" if z['strength'] > 0.7 else
                    "强" if z['strength'] > 0.5 else
                    "中等"
                )
                resistance_info.append(
                    f"向上第{i+1}区间: {z['range'][0]:.8f}-{z['range'][1]:.8f} "
                    f"(强度:{z['strength']:.2f} - {strength_level}, "
                    f"收盘触及:{price_zones[z['price']]['close_touches']}次)"
                )
                
            for i, z in enumerate(support_zones):
                strength_level = (
                    "非常强" if z['strength'] > 0.7 else
                    "强" if z['strength'] > 0.5 else
                    "中等"
                )
                support_info.append(
                    f"向下第{i+1}区间: {z['range'][0]:.8f}-{z['range'][1]:.8f} "
                    f"(强度:{z['strength']:.2f} - {strength_level}, "
                    f"收盘触及:{price_zones[z['price']]['close_touches']}次)"
                )
            
            info = "\n".join(resistance_info) + "\n" + "\n".join(support_info)
            return True, info  # 只有成功分析出区间时才返回True
            
        except Exception as e:
            logger.error(f"支撑阻力分析出错: {str(e)}")
            return False, f"支撑阻力分析出错: {str(e)}"  # 出错时返回False
        
    def analyze_adam_theory(self, data: pd.DataFrame) -> Tuple[bool, str]:
        """亚当理论分析 - 仅分析做空机会"""
        try:
            if len(data) < 80:
                return False, "数据量不足"
            
            # 1. 识别跌破点
            recent_data = data.tail(80)
            
            # 使用更精确的横盘区间识别方法：取20根K线的滚动高低
            highs = recent_data['high'].rolling(window=20).max()
            lows = recent_data['low'].rolling(window=20).min()
            consolidation_high = highs.iloc[-2]  # 使用前一个周期的高点作为横盘上沿
            consolidation_low = lows.iloc[-2]    # 横盘下沿
            current_close = recent_data['close'].iloc[-1]
            
            # 计算横盘区间的波动范围
            range_size = consolidation_high - consolidation_low
            
            # 2. 计算对称性特征
            # 使用更多历史数据来认识价格波动特征
            pre_breakout_data = data.iloc[-30:-1]  # 最近30根K线，不包含当前K线
            swing_high = pre_breakout_data['high'].max()
            swing_low = pre_breakout_data['low'].min()
            
            pre_breakout_range = swing_high - swing_low
            percentage_range = (pre_breakout_range / swing_high) * 100  # 以最高价计算百分比波动
            
            # 3. 预测目标位（做空目标价，下行空间）
            target_price = current_close - pre_breakout_range
            target_percentage = ((current_close - target_price) / current_close) * 100
            
            # 4. 其他确认指标（成交量分析）
            if 'tick_volume' in recent_data.columns:
                volume_data = recent_data['tick_volume']  # MT5数据
            elif 'volume' in recent_data.columns:
                volume_data = recent_data['volume']       # Binance数据
            else:
                raise ValueError("找不到成交量数据")
            
            volume_ma = volume_data.rolling(20).mean()
            volume_std = volume_data.rolling(20).std()
            current_volume = volume_data.iloc[-1]
            volume_zscore = (current_volume - volume_ma.iloc[-1]) / volume_std.iloc[-1]
            volume_breakout = volume_zscore > 1.0  # 强量支持下行卖单
            
            # 计算横盘时间和稳定性
            consolidation_bars = 0
            price_range = []
            for i in range(2, len(recent_data)):
                price = recent_data['close'].iloc[-i]
                if consolidation_low * 0.95 <= price <= consolidation_high * 1.05:
                    consolidation_bars += 1
                    price_range.append(price)
                else:
                    break
            
            if price_range:
                price_std = np.std(price_range)
                price_stability = price_std / np.mean(price_range)
            else:
                price_stability = 1.0
            
            # 5. 综合判断（做空机会要求价格处于横盘区间上沿附近，且出现足够横盘稳定性）
            is_valid = (
                current_close < consolidation_high and  # 当前价格低于横盘上沿
                consolidation_bars >= 3 and              # 横盘时间至少3根K线
                pre_breakout_range > 0 and               # 有效的波动范围
                price_stability < 0.1                    # 横盘足够稳定
            )
            
            # 6. 生成详细分析信息
            info = (
                f"当前价格: {current_close:.8f}\n"
                f"目标位: {target_price:.8f} (下跌空间: {target_percentage:.8f}%)\n"
                f"横盘区间: {consolidation_low:.8f} - {consolidation_high:.8f} "
                f"(波动: {(range_size/consolidation_high*100):.8f}%)\n"
                f"横盘时间: {consolidation_bars}根K线 (稳定性: {price_stability:.8f})\n"
                f"历史波动范围: {pre_breakout_range:.8f} ({percentage_range:.8f}%)\n"
                f"量能分析: {'强势放量' if volume_zscore > 2 else '温和放量' if volume_zscore > 1 else '无明显放量'} "
                f"(Z-Score: {volume_zscore:.8f})"
            )
            
            return is_valid, info
            
        except Exception as e:
            logger.error(f"亚当理论分析出错: {str(e)}")
            return False, f"分析出错: {str(e)}"
    
    
    @timeit
    def analyze_all_patterns(self, data: pd.DataFrame) -> Dict[str, Tuple[bool, str]]:
        """分析所有技术形态（做空信号版本）"""
        try:
            results = {}
            # 修改这里，使用正确的方法名
            results["趋势线跌破"] = self.analyze_trend_line_breakout_short(data)
            results["顶部箱体跌破"] = self.analyze_resistance_box_breakout(data)
            results["放量"] = self.analyze_volume_surge(data)
            results["收敛三角形"] = self.analyze_converging_triangle(data)
            results["头肩顶"] = self.analyze_head_shoulders_top(data)
            results["K线组合"] = self.analyze_k_line_pattern(data)
            results["回调卖点"] = self.analyze_pullback_short_entry(data)
            results["倒老鸭头"] = self.analyze_inverted_duck_head(data)
            results["倒茶杯带柄"] = self.analyze_inverted_cup_handle   (data)
            results["破顶翻"] = self.analyze_break_top_reversal(data)
            results["亚当理论"] = self.analyze_adam_theory(data)
            return results
        except Exception as e:
            logger.error(f"形态分析出错: {str(e)}")
            return {}

    @timeit
    def detect_trading_signals(self, data: pd.DataFrame) -> List[Dict]:
        """
        检测交易信号 - 双跌破系统（做空信号）
        
        根据各形态分析结果, 按照以下逻辑组合信号:
        1. 完美形态：趋势跌破 + 顶部箱体跌破 + (收敛三角形或头肩顶) + K线组合 + 放量
        2. 趋势跌破组合：趋势跌破 + 任意一个确认信号
        3. 形态跌破确认：(顶部箱体/收敛三角形/头肩顶) + (K线/放量)确认
        4. 回调卖点：回调卖点 + (K线/放量)确认
        5. 倒老鸭头：倒老鸭头 + (K线/放量)确认
        6. 倒茶杯带柄：倒茶杯带柄 + (K线/放量)确认
        """
        signals = []
        pattern_results = self.analyze_all_patterns(data)
        
        # 提取各形态的结果（已转换为做空版本）
        trend_break      = pattern_results["趋势线跌破"][0]
        box_break        = pattern_results["顶部箱体跌破"][0]
        volume           = pattern_results["放量"][0]
        triangle         = pattern_results["收敛三角形"][0]
        head_shoulders   = pattern_results["头肩顶"][0]
        k_line           = pattern_results["K线组合"][0]
        pullback         = pattern_results["回调卖点"][0]
        duck_head        = pattern_results["倒老鸭头"][0]
        cup              = pattern_results["倒茶杯带柄"][0]
        break_top        = pattern_results["破顶翻"][0]
    
        # 分析支撑阻力和亚当理论（均为做空版本）
        _, sr_info = self.analyze_support_resistance(data)
        _, adam_info = self.analyze_adam_theory(data)
    
        extra_analysis = (
            "\n=== 支撑阻力分析 ===\n"
            f"{sr_info}\n"
            "\n=== 亚当理论分析 ===\n"
            f"{adam_info}"
        )
    
        # 1. 完美形态：趋势跌破 + 顶部箱体跌破 + (收敛三角形或头肩顶) + K线组合 + 放量
        if all([trend_break, box_break, (triangle or head_shoulders), k_line, volume, break_top]):
            signals.append({
                'type': 'A',  # 完美形态
                'strength': 'HIGH',
                'patterns': pattern_results,
                'details': "完美形态：趋势跌破 + 顶部箱体跌破 + 形态确认（收敛三角形或头肩顶） + K线组合 + 放量",
                'extra_analysis': extra_analysis
            })
        # 2. 趋势跌破组合：趋势跌破 + 任意一个确认信号
        elif trend_break and any([box_break, triangle, head_shoulders, k_line, volume, break_top]):
            signals.append({
                'type': 'B',  # 趋势跌破组合
                'strength': 'MEDIUM',
                'patterns': pattern_results,
                'details': "趋势跌破组合：趋势跌破 + 确认信号",
                'extra_analysis': extra_analysis
            })
        # 3. 形态跌破确认：(顶部箱体/收敛三角形/头肩顶) + (K线/放量)确认
        elif (box_break or triangle or head_shoulders or break_top) and (k_line or volume):
            signals.append({
                'type': 'C',  # 形态跌破确认
                'strength': 'MEDIUM',
                'patterns': pattern_results,
                'details': "形态跌破确认：形态 + 确认信号",
                'extra_analysis': extra_analysis
            })
        # 4. 其他交易机会：回调卖点 / 倒老鸭头 / 倒茶杯带柄 + (K线/放量)确认
        elif any([pullback, duck_head, cup]) and (k_line or volume):
            signals.append({
                'type': 'D',  # 其他交易机会
                'strength': 'MEDIUM',
                'patterns': pattern_results,
                'details': "其他交易机会：回调卖点/倒老鸭头/倒茶杯带柄 + 确认信号",
                'extra_analysis': extra_analysis
            })
    
        # 为每个信号添加详细的形态信息
        for signal in signals:
            satisfied_patterns = []
            for pattern_name, (is_valid, info) in pattern_results.items():
                if is_valid:
                    satisfied_patterns.append(f"{pattern_name}: {info}")
            signal['satisfied_patterns'] = satisfied_patterns
    
        return signals