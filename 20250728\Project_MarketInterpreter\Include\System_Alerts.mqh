//+------------------------------------------------------------------+
//|                                               System_Alerts.mqh |
//|                                         Copyright © 2025, htazq |
//|                                         https://github.com/htazq |
//+------------------------------------------------------------------+
#property copyright "Copyright © 2025, htazq"
#property link      "https://github.com/htazq"

#include "Defines.mqh"

//+------------------------------------------------------------------+
//| 警报系统命名空间                                                   |
//+------------------------------------------------------------------+
namespace Alerts
{
    //+------------------------------------------------------------------+
    //| 警报类型枚举                                                       |
    //+------------------------------------------------------------------+
    enum ENUM_ALERT_TYPE
    {
        ALERT_PRICE_ACTION,     // 价格行为信号警报
        ALERT_TREND_CHANGE,     // 趋势变化警报
        ALERT_KEY_LEVEL,        // 关键价位警报
        ALERT_HIGH_VOLATILITY,  // 高波动率警报
        ALERT_SESSION_CHANGE    // 时段变化警报
    };
    
    //+------------------------------------------------------------------+
    //| 警报记录结构                                                       |
    //+------------------------------------------------------------------+
    struct AlertRecord
    {
        datetime time;              // 警报时间
        ENUM_ALERT_TYPE type;      // 警报类型
        string message;             // 警报消息
        color alert_color;          // 警报颜色
        bool is_active;             // 是否激活
        bool sound_played;          // 是否已播放声音
        bool notification_sent;     // 是否已发送通知
    };
    
    //+------------------------------------------------------------------+
    //| 私有变量                                                          |
    //+------------------------------------------------------------------+
    static AlertRecord alert_history[MAX_ALERT_HISTORY];
    static int alert_count = 0;
    static datetime last_alert_times[5]; // 每种警报类型的最后触发时间
    
    // 警报状态跟踪
    static string last_pa_alerted = "";
    static ENUM_TREND_STATE last_trend_alerted = TREND_RANGE;
    static bool last_high_volatility_alerted = false;
    static ENUM_TRADING_SESSION last_session_alerted = SESSION_NONE;
    static double last_key_level_alerted = 0.0;
    
    //+------------------------------------------------------------------+
    //| 初始化警报系统                                                     |
    //+------------------------------------------------------------------+
    void Initialize()
    {
        // 初始化警报历史
        for(int i = 0; i < MAX_ALERT_HISTORY; i++)
        {
            alert_history[i].time = 0;
            alert_history[i].type = ALERT_PRICE_ACTION;
            alert_history[i].message = "";
            alert_history[i].alert_color = COLOR_INFO;
            alert_history[i].is_active = false;
            alert_history[i].sound_played = false;
            alert_history[i].notification_sent = false;
        }
        
        // 初始化最后警报时间
        for(int i = 0; i < 5; i++)
        {
            last_alert_times[i] = 0;
        }
        
        alert_count = 0;
        last_pa_alerted = "";
        last_trend_alerted = TREND_RANGE;
        last_high_volatility_alerted = false;
        last_session_alerted = SESSION_NONE;
        last_key_level_alerted = 0.0;
        
        DEBUG_PRINT("警报系统初始化完成");
    }
    
    //+------------------------------------------------------------------+
    //| 清理警报系统                                                       |
    //+------------------------------------------------------------------+
    void Deinitialize()
    {
        DEBUG_PRINT("警报系统已清理");
    }
    
    //+------------------------------------------------------------------+
    //| 检查警报冷却时间                                                   |
    //+------------------------------------------------------------------+
    bool IsAlertCooldownActive(ENUM_ALERT_TYPE alert_type)
    {
        datetime current_time = TimeCurrent();
        datetime last_time = last_alert_times[(int)alert_type];
        
        return (current_time - last_time) < ALERT_COOLDOWN_SECONDS;
    }
    
    //+------------------------------------------------------------------+
    //| 添加警报记录                                                       |
    //+------------------------------------------------------------------+
    void AddAlertRecord(ENUM_ALERT_TYPE alert_type, string message, color alert_color = COLOR_INFO)
    {
        // 如果警报历史已满，移除最旧的记录
        if(alert_count >= MAX_ALERT_HISTORY)
        {
            for(int i = 0; i < MAX_ALERT_HISTORY - 1; i++)
            {
                alert_history[i] = alert_history[i + 1];
            }
            alert_count = MAX_ALERT_HISTORY - 1;
        }
        
        // 添加新的警报记录
        alert_history[alert_count].time = TimeCurrent();
        alert_history[alert_count].type = alert_type;
        alert_history[alert_count].message = message;
        alert_history[alert_count].alert_color = alert_color;
        alert_history[alert_count].is_active = true;
        alert_history[alert_count].sound_played = false;
        alert_history[alert_count].notification_sent = false;
        
        alert_count++;
        
        // 更新最后警报时间
        last_alert_times[(int)alert_type] = TimeCurrent();
        
        DEBUG_PRINT(StringFormat("添加警报记录: %s - %s", EnumToString(alert_type), message));
    }
    
    //+------------------------------------------------------------------+
    //| 播放警报声音                                                       |
    //+------------------------------------------------------------------+
    void PlayAlertSound(ENUM_ALERT_TYPE alert_type)
    {
        string sound_file = "";
        
        switch(alert_type)
        {
            case ALERT_PRICE_ACTION:
                sound_file = "alert.wav";
                break;
            case ALERT_TREND_CHANGE:
                sound_file = "news.wav";
                break;
            case ALERT_KEY_LEVEL:
                sound_file = "tick.wav";
                break;
            case ALERT_HIGH_VOLATILITY:
                sound_file = "timeout.wav";
                break;
            case ALERT_SESSION_CHANGE:
                sound_file = "connect.wav";
                break;
        }
        
        if(sound_file != "")
        {
            PlaySound(sound_file);
        }
    }
    
    //+------------------------------------------------------------------+
    //| 发送推送通知                                                       |
    //+------------------------------------------------------------------+
    void SendPushNotification(string message)
    {
        // 发送手机推送通知
        if(!SendNotification(message))
        {
            DEBUG_PRINT("推送通知发送失败: " + message);
        }
        else
        {
            DEBUG_PRINT("推送通知发送成功: " + message);
        }
    }
    
    //+------------------------------------------------------------------+
    //| 检查并触发价格行为警报                                             |
    //+------------------------------------------------------------------+
    void CheckPriceActionAlert(string pa_signal, bool pa_alert_enabled, bool sound_enabled, bool push_enabled)
    {
        if(!pa_alert_enabled || pa_signal == "" || pa_signal == "无信号") return;
        
        // 检查是否为新信号
        if(pa_signal == last_pa_alerted) return;
        
        // 检查冷却时间
        if(IsAlertCooldownActive(ALERT_PRICE_ACTION)) return;
        
        // 确定警报颜色
        color alert_color = COLOR_INFO;
        if(StringFind(pa_signal, "L1") >= 0 || StringFind(pa_signal, "L2") >= 0)
            alert_color = COLOR_SUCCESS;
        else if(StringFind(pa_signal, "H1") >= 0 || StringFind(pa_signal, "H2") >= 0)
            alert_color = COLOR_DANGER;
        
        // 创建警报消息
        string alert_message = StringFormat("价格行为信号: %s", pa_signal);
        
        // 添加警报记录
        AddAlertRecord(ALERT_PRICE_ACTION, alert_message, alert_color);
        
        // 播放声音
        if(sound_enabled)
        {
            PlayAlertSound(ALERT_PRICE_ACTION);
        }
        
        // 发送推送通知
        if(push_enabled)
        {
            string push_message = StringFormat("%s - %s", _Symbol, alert_message);
            SendPushNotification(push_message);
        }
        
        // 更新最后警报状态
        last_pa_alerted = pa_signal;
        
        Print("价格行为警报: ", alert_message);
    }
    
    //+------------------------------------------------------------------+
    //| 检查并触发趋势变化警报                                             |
    //+------------------------------------------------------------------+
    void CheckTrendChangeAlert(ENUM_TREND_STATE current_trend, bool trend_alert_enabled, bool sound_enabled, bool push_enabled)
    {
        if(!trend_alert_enabled) return;
        
        // 检查趋势是否发生变化
        if(current_trend == last_trend_alerted) return;
        
        // 检查冷却时间
        if(IsAlertCooldownActive(ALERT_TREND_CHANGE)) return;
        
        // 确定警报颜色
        color alert_color = COLOR_INFO;
        switch(current_trend)
        {
            case TREND_UP: alert_color = COLOR_TREND_UP; break;
            case TREND_DOWN: alert_color = COLOR_TREND_DOWN; break;
            case TREND_RANGE: alert_color = COLOR_TREND_RANGE; break;
        }
        
        // 创建警报消息
        string trend_name = "";
        switch(current_trend)
        {
            case TREND_UP: trend_name = "上升趋势"; break;
            case TREND_DOWN: trend_name = "下降趋势"; break;
            case TREND_RANGE: trend_name = "震荡区间"; break;
        }
        
        string alert_message = StringFormat("趋势变化: %s", trend_name);
        
        // 添加警报记录
        AddAlertRecord(ALERT_TREND_CHANGE, alert_message, alert_color);
        
        // 播放声音
        if(sound_enabled)
        {
            PlayAlertSound(ALERT_TREND_CHANGE);
        }
        
        // 发送推送通知
        if(push_enabled)
        {
            string push_message = StringFormat("%s - %s", _Symbol, alert_message);
            SendPushNotification(push_message);
        }
        
        // 更新最后警报状态
        last_trend_alerted = current_trend;
        
        Print("趋势变化警报: ", alert_message);
    }
    
    //+------------------------------------------------------------------+
    //| 检查并触发关键价位警报                                             |
    //+------------------------------------------------------------------+
    void CheckKeyLevelAlert(double current_price, double key_level, string level_name, 
                           double threshold_points, bool key_level_alert_enabled, bool sound_enabled, bool push_enabled)
    {
        if(!key_level_alert_enabled || key_level <= 0) return;
        
        // 计算距离
        double distance = MathAbs(current_price - key_level);
        double threshold = threshold_points * _Point;
        
        // 检查是否接近关键价位
        if(distance > threshold) return;
        
        // 检查是否已经为此价位发出过警报
        if(MathAbs(key_level - last_key_level_alerted) < _Point) return;
        
        // 检查冷却时间
        if(IsAlertCooldownActive(ALERT_KEY_LEVEL)) return;
        
        // 创建警报消息
        string alert_message = StringFormat("接近关键价位: %s %.5f (距离: %.1f点)", 
                                           level_name, key_level, distance / _Point);
        
        // 添加警报记录
        AddAlertRecord(ALERT_KEY_LEVEL, alert_message, COLOR_WARNING);
        
        // 播放声音
        if(sound_enabled)
        {
            PlayAlertSound(ALERT_KEY_LEVEL);
        }
        
        // 发送推送通知
        if(push_enabled)
        {
            string push_message = StringFormat("%s - %s", _Symbol, alert_message);
            SendPushNotification(push_message);
        }
        
        // 更新最后警报状态
        last_key_level_alerted = key_level;
        
        Print("关键价位警报: ", alert_message);
    }
    
    //+------------------------------------------------------------------+
    //| 检查并触发高波动率警报                                             |
    //+------------------------------------------------------------------+
    void CheckHighVolatilityAlert(double atr_percentile, double threshold_percentile, 
                                 bool volatility_alert_enabled, bool sound_enabled, bool push_enabled)
    {
        if(!volatility_alert_enabled) return;
        
        bool is_high_volatility = (atr_percentile >= threshold_percentile);
        
        // 检查状态是否发生变化
        if(is_high_volatility == last_high_volatility_alerted) return;
        
        // 只在进入高波动率时警报，退出时不警报
        if(!is_high_volatility) 
        {
            last_high_volatility_alerted = false;
            return;
        }
        
        // 检查冷却时间
        if(IsAlertCooldownActive(ALERT_HIGH_VOLATILITY)) return;
        
        // 创建警报消息
        string alert_message = StringFormat("高波动率警报: ATR百分位 %.1f%%", atr_percentile);
        
        // 添加警报记录
        AddAlertRecord(ALERT_HIGH_VOLATILITY, alert_message, COLOR_DANGER);
        
        // 播放声音
        if(sound_enabled)
        {
            PlayAlertSound(ALERT_HIGH_VOLATILITY);
        }
        
        // 发送推送通知
        if(push_enabled)
        {
            string push_message = StringFormat("%s - %s", _Symbol, alert_message);
            SendPushNotification(push_message);
        }
        
        // 更新最后警报状态
        last_high_volatility_alerted = true;
        
        Print("高波动率警报: ", alert_message);
    }
    
    //+------------------------------------------------------------------+
    //| 检查并触发时段变化警报                                             |
    //+------------------------------------------------------------------+
    void CheckSessionChangeAlert(ENUM_TRADING_SESSION current_session, bool session_alert_enabled, 
                                bool sound_enabled, bool push_enabled)
    {
        if(!session_alert_enabled) return;
        
        // 检查时段是否发生变化
        if(current_session == last_session_alerted) return;
        
        // 只在进入重要时段时警报
        if(current_session == SESSION_NONE) 
        {
            last_session_alerted = current_session;
            return;
        }
        
        // 检查冷却时间
        if(IsAlertCooldownActive(ALERT_SESSION_CHANGE)) return;
        
        // 确定时段名称和颜色
        string session_name = "";
        color session_color = COLOR_INFO;
        
        switch(current_session)
        {
            case SESSION_ASIA:
                session_name = "亚洲时段";
                session_color = COLOR_SESSION_ASIA;
                break;
            case SESSION_LONDON:
                session_name = "伦敦时段";
                session_color = COLOR_SESSION_LONDON;
                break;
            case SESSION_NEWYORK:
                session_name = "纽约时段";
                session_color = COLOR_SESSION_NEWYORK;
                break;
            case SESSION_OVERLAP:
                session_name = "重叠时段";
                session_color = COLOR_SESSION_OVERLAP;
                break;
        }
        
        // 创建警报消息
        string alert_message = StringFormat("时段变化: 进入%s", session_name);
        
        // 添加警报记录
        AddAlertRecord(ALERT_SESSION_CHANGE, alert_message, session_color);
        
        // 播放声音
        if(sound_enabled)
        {
            PlayAlertSound(ALERT_SESSION_CHANGE);
        }
        
        // 发送推送通知
        if(push_enabled)
        {
            string push_message = StringFormat("%s - %s", _Symbol, alert_message);
            SendPushNotification(push_message);
        }
        
        // 更新最后警报状态
        last_session_alerted = current_session;
        
        Print("时段变化警报: ", alert_message);
    }
    
    //+------------------------------------------------------------------+
    //| 主警报检查函数                                                     |
    //+------------------------------------------------------------------+
    void CheckAndTrigger(string pa_signal, ENUM_TREND_STATE current_trend, ENUM_TRADING_SESSION current_session,
                        double current_price, double atr_percentile, double daily_open, double prev_high, double prev_low,
                        bool pa_alert_enabled = true, bool trend_alert_enabled = true, bool key_level_alert_enabled = true,
                        bool volatility_alert_enabled = true, bool session_alert_enabled = true,
                        bool sound_enabled = true, bool push_enabled = false,
                        double key_level_threshold = 50.0, double volatility_threshold = 80.0)
    {
        // 检查价格行为信号警报
        CheckPriceActionAlert(pa_signal, pa_alert_enabled, sound_enabled, push_enabled);
        
        // 检查趋势变化警报
        CheckTrendChangeAlert(current_trend, trend_alert_enabled, sound_enabled, push_enabled);
        
        // 检查关键价位警报
        if(key_level_alert_enabled)
        {
            CheckKeyLevelAlert(current_price, daily_open, "日开盘价", key_level_threshold, 
                              key_level_alert_enabled, sound_enabled, push_enabled);
            CheckKeyLevelAlert(current_price, prev_high, "昨日最高", key_level_threshold, 
                              key_level_alert_enabled, sound_enabled, push_enabled);
            CheckKeyLevelAlert(current_price, prev_low, "昨日最低", key_level_threshold, 
                              key_level_alert_enabled, sound_enabled, push_enabled);
        }
        
        // 检查高波动率警报
        CheckHighVolatilityAlert(atr_percentile, volatility_threshold, volatility_alert_enabled, sound_enabled, push_enabled);
        
        // 检查时段变化警报
        CheckSessionChangeAlert(current_session, session_alert_enabled, sound_enabled, push_enabled);
    }
    
    //+------------------------------------------------------------------+
    //| 获取最新警报消息                                                   |
    //+------------------------------------------------------------------+
    string GetLatestAlertMessage()
    {
        if(alert_count == 0) return "";
        
        AlertRecord latest = alert_history[alert_count - 1];
        return StringFormat("[%s] %s", TimeToString(latest.time, TIME_MINUTES), latest.message);
    }
    
    //+------------------------------------------------------------------+
    //| 获取警报历史                                                       |
    //+------------------------------------------------------------------+
    string GetAlertHistory(int max_records = 3)
    {
        if(alert_count == 0) return "无警报历史";
        
        string history_text = "";
        int records_to_show = MathMin(alert_count, max_records);
        
        for(int i = alert_count - records_to_show; i < alert_count; i++)
        {
            if(i >= 0 && alert_history[i].is_active)
            {
                history_text += StringFormat("%s: %s\n", 
                    TimeToString(alert_history[i].time, TIME_MINUTES),
                    alert_history[i].message);
            }
        }
        
        return history_text;
    }
    
    //+------------------------------------------------------------------+
    //| 清除警报历史                                                       |
    //+------------------------------------------------------------------+
    void ClearAlertHistory()
    {
        for(int i = 0; i < alert_count; i++)
        {
            alert_history[i].is_active = false;
        }
        alert_count = 0;
        
        DEBUG_PRINT("警报历史已清除");
    }
    
    //+------------------------------------------------------------------+
    //| 获取警报统计信息                                                   |
    //+------------------------------------------------------------------+
    string GetAlertStats()
    {
        int pa_count = 0, trend_count = 0, key_level_count = 0, volatility_count = 0, session_count = 0;
        
        for(int i = 0; i < alert_count; i++)
        {
            if(!alert_history[i].is_active) continue;
            
            switch(alert_history[i].type)
            {
                case ALERT_PRICE_ACTION: pa_count++; break;
                case ALERT_TREND_CHANGE: trend_count++; break;
                case ALERT_KEY_LEVEL: key_level_count++; break;
                case ALERT_HIGH_VOLATILITY: volatility_count++; break;
                case ALERT_SESSION_CHANGE: session_count++; break;
            }
        }
        
        return StringFormat("警报统计 - 价格行为:%d 趋势:%d 价位:%d 波动:%d 时段:%d", 
                           pa_count, trend_count, key_level_count, volatility_count, session_count);
    }
}