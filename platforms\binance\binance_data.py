from typing import Dict, List
import pandas as pd
from datetime import datetime
import logging
import sys
import time
import concurrent.futures
from threading import Lock
import queue
from binance.client import Client
from utils.log_utils import setup_logger

# 设置日志
logger = setup_logger(
    'binance_data', 
    os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), 'logs', 'binance', 'data.log'),
    level=logging.INFO
)

class BinanceDataFetcher:
    """币安数据获取器 - 负责所有数据获取相关的功能"""
    
    def __init__(self, api_key: str, api_secret: str, max_workers: int = 5):
        """
        初始化数据获取器
        
        参数:
            api_key: Binance API key
            api_secret: Binance API secret
            max_workers: 最大线程数，用于并发请求
        """
        self.client = Client(api_key, api_secret)
        self.max_workers = max_workers
        self.rate_limit_lock = Lock()
        self.request_queue = queue.Queue()
        self.last_request_time = time.time()
        self.request_interval = 0.1  # 请求间隔(秒)
        
        # K线周期映射
        self.timeframe_map = {
            '1m': Client.KLINE_INTERVAL_1MINUTE,
            '5m': Client.KLINE_INTERVAL_5MINUTE,
            '15m': Client.KLINE_INTERVAL_15MINUTE,
            '30m': Client.KLINE_INTERVAL_30MINUTE,
            '1h': Client.KLINE_INTERVAL_1HOUR,
            '4h': Client.KLINE_INTERVAL_4HOUR,
            '1d': Client.KLINE_INTERVAL_1DAY
        }

    def _rate_limit(self):
        """请求频率限制"""
        with self.rate_limit_lock:
            elapsed = time.time() - self.last_request_time
            if elapsed < self.request_interval:
                time.sleep(self.request_interval - elapsed)
            self.last_request_time = time.time()

    def get_filtered_symbols(self, 
                           limit: int = 50,
                           quote_asset: str = 'USDT',
                           min_volume: float = 1000000,
                           market_cap_tiers: List[str] = None,
                           spot_only: bool = True) -> List[Dict]:
        """
        获取符合条件的交易对
        
        参数:
            limit: 返回的最大数量
            quote_asset: 计价货币
            min_volume: 最小24h成交额
            market_cap_tiers: 市值层级过滤 ['Large', 'Medium', 'Small']
            spot_only: 是否只返回现货交易对
        """
        try:
            self._rate_limit()
            tickers = self.client.get_ticker()
            
            if spot_only:
                exchange_info = self.client.get_exchange_info()
                spot_symbols = {s['symbol'] for s in exchange_info['symbols'] 
                              if s['status'] == 'TRADING'}
            
            filtered_tickers = []
            for t in tickers:
                if not t['symbol'].endswith(quote_asset):
                    continue
                    
                if spot_only and t['symbol'] not in spot_symbols:
                    continue
                    
                price = t['lastPrice']
                volume_usdt = float(t['volume']) * float(price)
                
                if volume_usdt < min_volume:
                    continue
                    
                market_cap_tier = self._get_market_cap_tier(volume_usdt)
                if market_cap_tiers and market_cap_tier not in market_cap_tiers:
                    continue
                    
                filtered_tickers.append({
                    'symbol': t['symbol'],
                    'price': price,
                    'volume': volume_usdt,
                    'market_cap_tier': market_cap_tier
                })
            
            filtered_tickers.sort(key=lambda x: x['volume'], reverse=True)
            return filtered_tickers[:limit]
            
        except Exception as e:
            logger.error(f"获取交易对列表时出错: {str(e)}")
            return []

    def get_klines(self, 
                   symbol: str, 
                   interval: str, 
                   limit: int = 200) -> pd.DataFrame:
        """
        获取K线数据
        
        参数:
            symbol: 交易对
            interval: K线周期 ('1m','5m','15m','30m','1h','4h','1d')
            limit: K线数量
        """
        try:
            self._rate_limit()
            klines = self.client.get_klines(
                symbol=symbol,
                interval=self.timeframe_map[interval],
                limit=limit
            )
            
            df = pd.DataFrame(klines, columns=[
                'timestamp', 'open', 'high', 'low', 'close', 'volume',
                'close_time', 'quote_volume', 'trades',
                'taker_buy_base', 'taker_buy_quote', 'ignore'
            ])
            
            # 数据处理
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            for col in ['open', 'high', 'low', 'close', 'volume']:
                df[col] = df[col].astype(float)
                
            df.set_index('timestamp', inplace=True)
            return df
            
        except Exception as e:
            logger.error(f"获取{symbol} {interval}K线数据时出错: {str(e)}")
            return None

    def _get_market_cap_tier(self, volume_usdt: float) -> str:
        """根据成交量确定市值层级"""
        if volume_usdt > 50_000_000:  # 5000万美元以上
            return 'Large'
        elif volume_usdt > 10_000_000:  # 1000万到5000万美元
            return 'Medium'
        return 'Small'

if __name__ == "__main__":

    # 替换为您的API密钥
    API_KEY = "Mf1zIJlnuy2DOTY7UI1Jo4ER6MAYAcj9IbpuCrOWkKLpegzbazRaTQr8F39Pps4y"
    API_SECRET = "5wB8Sk4tSYFYDtG2OpOJuPf5GwLX54jOR0TsLdZ3p5ClFjLnc93enr1tYNqkzMnj"
    
    # 创建数据获取器实例
    fetcher = BinanceDataFetcher(API_KEY, API_SECRET)
    
    try:
        # 1. 测试获取交易对
        print("\n=== 测试获取交易对 ===")
        symbols = fetcher.get_filtered_symbols(
            limit=5,  # 只获取5个作为测试
            min_volume=1000000,
            market_cap_tiers=['Large']
        )
        
        if symbols:
            print("\n获取到的交易对:")
            for s in symbols:
                print(f"Symbol: {s['symbol']}, Volume: ${s['volume']:,.2f}, Tier: {s['market_cap_tier']}")
        
        # 2. 测试获取K线数据
        if symbols:
            print("\n=== 测试获取K线数据 ===")
            symbol = symbols[0]['symbol']  # 使用第一个交易对
            timeframes = ['15m', '1h', '4h']
            
            for tf in timeframes:
                print(f"\n获取 {symbol} 的 {tf} K线数据:")
                df = fetcher.get_klines(symbol, tf, limit=10)  # 只获取10根K线作为测试
                if df is not None:
                    print(df.tail(3))  # 显示最后3根K线
                    
    except Exception as e:
        print(f"测试过程出错: {str(e)}")