"""
MT5持仓管理模块
负责管理所有持仓的止损、止盈、部分平仓等操作
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

import MetaTrader5 as mt5
import pandas as pd
import numpy as np
import logging
from datetime import datetime, timedelta
import time
from typing import Dict, List, Optional, Tuple, Union

from platforms.mt5.mt5_config import (
    TIMEFRAME_MAP, 
    TRADING_CONFIG, 
    SIGNAL_CONFIG, 
    SYMBOLS, 
    TIMEFRAME_STRINGS,
    TIMEFRAME_CHECK_MAP,
    TIMEFRAME_COMMENT_MAP,
    TIMEFRAMES,
    POSITION_MANAGEMENT_CONFIG,
    MT5_PATH
)
from platforms.mt5.mt5_data import MT5DataFetcher
from utils.log_utils import setup_logger

# MT5错误代码映射
MT5_ERROR_CODES = {
    10004: "交易服务器已满",
    10006: "请求被拒绝",
    10007: "请求已取消",
    10008: "订单已完成",
    10009: "请求已完成",
    10010: "只有部分请求完成",
    10011: "请求处理错误",
    10012: "请求超时",
    10013: "无效请求",
    10014: "无效交易量",
    10015: "无效价格",
    10016: "无效止损",
    10017: "交易被禁用",
    10018: "Market closed",
    10019: "资金不足",
    10020: "价格已改变",
    10021: "没有报价",
    10022: "无效的到期日",
    10023: "订单状态已改变",
    10024: "请求太频繁",
    10025: "没有修改",
    10026: "经纪商太忙",
    10027: "用户请求超时",
    10028: "与交易服务器连接断开",
    10029: "最大挂单数量已达到",
    10030: "最大订单数量已达到",
}

# 设置日志
logger = setup_logger(
    'mt5_position_manager', 
    os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), 'logs', 'mt5', 'position_manager.log'),
    level=logging.INFO
)

class MT5PositionManager:
    """MT5持仓管理器"""
    
    def __init__(self):
        """初始化持仓管理器"""
        self.signal_config = SIGNAL_CONFIG
        self.position_config = POSITION_MANAGEMENT_CONFIG
        self.trading_config = TRADING_CONFIG
        
        # 初始化持仓管理状态
        self.last_check_time = {}  # 记录每个品种最后检查时间
        self.partial_close_history = {}  # 记录部分平仓历史
        
        # 存储当前有效信号
        self.current_signals = {}
        
        logger.info("持仓管理器初始化完成")
    
    def update_signals(self, signals: List[Dict]):
        """更新当前有效信号"""
        # 清空旧信号
        self.current_signals = {}
        
        # 处理新信号
        for signal in signals:
            symbol = signal.get('symbol')
            timeframe = signal.get('timeframe')
            direction = signal.get('direction')
            
            if not all([symbol, timeframe, direction]):
                continue
                
            # 初始化品种字典
            if symbol not in self.current_signals:
                self.current_signals[symbol] = {}
                
            # 初始化时间周期字典
            if timeframe not in self.current_signals[symbol]:
                self.current_signals[symbol][timeframe] = []
                
            # 添加信号
            self.current_signals[symbol][timeframe].append(signal)
            
        logger.info(f"更新信号完成，当前有 {len(signals)} 个有效信号")
        
        # 输出详细信号信息
        for symbol in self.current_signals:
            for timeframe in self.current_signals[symbol]:
                directions = [s['direction'] for s in self.current_signals[symbol][timeframe]]
                logger.info(f"品种 {symbol} 时间周期 {TIMEFRAME_STRINGS.get(timeframe, str(timeframe))} 有 {len(directions)} 个信号，方向: {directions}")

    def manage_all_positions(self, signals: List[Dict] = None):
        """管理所有持仓"""
        try:
            # 如果提供了信号，则更新信号
            if signals is not None:
                self.update_signals(signals)
                    
            # 获取所有持仓
            positions = mt5.positions_get()
            if positions is None or len(positions) == 0:
                logger.info("当前无持仓")
                return
                    
            logger.info(f"开始管理 {len(positions)} 个持仓")
            
            # 检查MT5连接状态
            if not mt5.terminal_info():
                logger.error("MT5终端未连接，无法管理持仓")
                return
                
            # 获取账户信息
            account_info = mt5.account_info()
            if account_info is None:
                logger.error("无法获取账户信息")
                return
                
            # 输出MT5终端信息
            terminal_info = mt5.terminal_info()
            logger.info(f"MT5终端连接状态: {terminal_info.connected}, 交易允许: {terminal_info.trade_allowed}")
            
            # 初始化数据获取器，用于检查交易时间
            data_fetcher = MT5DataFetcher()
            if not data_fetcher.initialize():
                logger.error("MT5数据获取器初始化失败，无法管理持仓")
                return
            
            # 输出持仓表头
            logger.info("=" * 120)
            logger.info(f"{'交易品种':<10} {'订单号':<10} {'时间':<20} {'类型':<6} {'交易量':<8} {'开仓价':<10} {'止损':<10} {'止盈':<10} {'当前价':<10} {'盈亏':<10} {'注释':<30}")
            logger.info("-" * 120)
            
            # 逐个管理持仓并输出详细信息
            for position in positions:
                # 获取品种信息
                symbol_info = mt5.symbol_info(position.symbol)
                if symbol_info is None:
                    logger.warning(f"无法获取品种信息: {position.symbol}，跳过此持仓")
                    continue
                    
                # 检查是否在交易时间内
                if not data_fetcher.is_trading_time(position.symbol):
                    logger.info(f"品种 {position.symbol} 当前非交易时间，跳过持仓管理")
                    
                    # 仍然显示持仓信息，但不进行管理
                    # 获取当前价格
                    tick = mt5.symbol_info_tick(position.symbol)
                    if tick is None:
                        logger.warning(f"无法获取价格信息: {position.symbol}")
                        continue
                        
                    current_price = tick.bid if position.type == mt5.POSITION_TYPE_BUY else tick.ask
                    
                    # 格式化时间
                    open_time = datetime.fromtimestamp(position.time).strftime('%Y-%m-%d %H:%M:%S')
                    
                    # 格式化类型
                    position_type = "buy" if position.type == mt5.POSITION_TYPE_BUY else "sell"
                    
                    # 输出详细持仓信息
                    logger.info(f"{position.symbol:<10} {position.ticket:<10} {open_time:<20} {position_type:<6} "
                            f"{position.volume:<8.2f} {position.price_open:<10.5f} {position.sl:<10.5f} {position.tp:<10.5f} "
                            f"{current_price:<10.5f} {position.profit:<10.2f} {position.comment:<30}")
                    continue
                    
                # 检查交易状态
                trade_mode = symbol_info.trade_mode
                trade_mode_str = "未知"
                if trade_mode == mt5.SYMBOL_TRADE_MODE_DISABLED:
                    trade_mode_str = "禁用"
                elif trade_mode == mt5.SYMBOL_TRADE_MODE_LONGONLY:
                    trade_mode_str = "仅多"
                elif trade_mode == mt5.SYMBOL_TRADE_MODE_SHORTONLY:
                    trade_mode_str = "仅空"
                elif trade_mode == mt5.SYMBOL_TRADE_MODE_CLOSEONLY:
                    trade_mode_str = "仅平"
                elif trade_mode == mt5.SYMBOL_TRADE_MODE_FULL:
                    trade_mode_str = "完全"
                    
                logger.info(f"品种 {position.symbol} 交易状态: {trade_mode_str}")

                # 获取当前价格
                tick = mt5.symbol_info_tick(position.symbol)
                if tick is None:
                    logger.warning(f"无法获取价格信息: {position.symbol}，跳过此持仓")
                    continue
                    
                current_price = tick.bid if position.type == mt5.POSITION_TYPE_BUY else tick.ask
                
                # 格式化时间
                open_time = datetime.fromtimestamp(position.time).strftime('%Y-%m-%d %H:%M:%S')
                
                # 格式化类型
                position_type = "buy" if position.type == mt5.POSITION_TYPE_BUY else "sell"
                
                # 输出详细持仓信息
                logger.info(f"{position.symbol:<10} {position.ticket:<10} {open_time:<20} {position_type:<6} "
                        f"{position.volume:<8.2f} {position.price_open:<10.5f} {position.sl:<10.5f} {position.tp:<10.5f} "
                        f"{current_price:<10.5f} {position.profit:<10.2f} {position.comment:<30}")
                
                # 管理持仓
                self.manage_single_position(position, account_info.balance)
                    
            logger.info("=" * 120)
            logger.info("所有持仓管理完成")
                
        except Exception as e:
            logger.error(f"管理所有持仓出错: {str(e)}", exc_info=True)
            
    def manage_single_position(self, position, account_balance):
        """管理单个持仓"""
        try:
            # 添加详细日志
            logger.debug(f"开始管理持仓: {position.symbol} #{position.ticket}")
            
            # 检查是否需要紧急平仓
            if self.check_emergency_close(position, account_balance):
                logger.warning(f"触发紧急平仓: Ticket {position.ticket}, 品种 {position.symbol}")
                self.close_position(position.ticket)
                return
            
            # 从注释中提取时间周期
            timeframe = self._get_timeframe_from_comment(position.comment)
            if timeframe is None:
                logger.warning(f"无法从注释中提取时间周期，跳过此持仓管理: {position.comment}")
                return
                
            # 检查最小持仓时间
            min_hold_time = self.position_config.get("signal_based_management", {}).get("min_hold_time", 60)
            position_time = datetime.fromtimestamp(position.time)
            current_time = datetime.now()
            hold_time_minutes = (current_time - position_time).total_seconds() / 60
            
            if hold_time_minutes < min_hold_time:
                logger.info(f"持仓时间不足{min_hold_time}分钟，暂不管理: Ticket {position.ticket}, 已持仓{hold_time_minutes:.1f}分钟")
                return
                
            # 检查是否有对应的信号
            has_signal, _ = self.check_position_signals(position, timeframe)
            
            # 如果没有同向信号，平仓
            if not has_signal and self.position_config.get("signal_based_management", {}).get("close_on_no_signal", True):
                logger.info(f"没有同向信号，平仓: Ticket {position.ticket}, 品种 {position.symbol}, 方向: {'多' if position.type == mt5.POSITION_TYPE_BUY else '空'}")
                self.close_position(position.ticket)
                
            # 如果有同向信号，保持持仓
            logger.info(f"持仓 {position.symbol} #{position.ticket} 有同向信号，保持持仓")
            
        except Exception as e:
            logger.error(f"管理单个持仓出错: {str(e)}", exc_info=True)

    def check_position_signals(self, position, timeframe):
        """检查持仓是否有对应的信号，不限制周期"""
        symbol = position.symbol
        position_type = position.type
        
        # 初始化返回值
        has_signal = False
        has_reverse_signal = False
        
        # 检查是否有任何周期的同向信号
        if symbol in self.current_signals:
            # 检查所有周期的信号
            for tf in self.current_signals[symbol]:
                for signal in self.current_signals[symbol][tf]:
                    # 检查信号方向
                    signal_direction = signal.get('direction')
                    
                    # 多单检查
                    if position_type == mt5.POSITION_TYPE_BUY and signal_direction == 'LONG':
                        has_signal = True
                        logger.debug(f"持仓 {symbol} #{position.ticket} 找到同向多单信号，周期: {tf}")
                    
                    # 空单检查
                    elif position_type == mt5.POSITION_TYPE_SELL and signal_direction == 'SHORT':
                        has_signal = True
                        logger.debug(f"持仓 {symbol} #{position.ticket} 找到同向空单信号，周期: {tf}")
        
        # 检查是否存在任何反向信号
        # 注意：我们不再将反向信号视为平仓信号，而是检查是否完全没有同向信号
        
        return has_signal, has_reverse_signal

    def _get_timeframe_from_comment(self, comment):
        """从注释中提取时间周期"""
        if not comment:
            return None
            
        # 遍历时间周期检查映射
        for tf_str, patterns in TIMEFRAME_CHECK_MAP.items():
            for pattern in patterns:
                if pattern in comment:
                    # 找到匹配的时间周期字符串，转换为MT5时间周期
                    tf_value = TIMEFRAME_COMMENT_MAP.get(tf_str)
                    if tf_value:
                        return TIMEFRAME_MAP.get(tf_value)
        
        return None

    def _get_atr_from_comment(self, comment):
        """从注释中提取ATR值"""
        if not comment:
            return None
            
        try:
            # 尝试查找ATR=数字 格式
            import re
            atr_match = re.search(r'ATR=([0-9.]+)', comment)
            if atr_match:
                return float(atr_match.group(1))
        except Exception as e:
            logger.error(f"从注释中提取ATR值出错: {str(e)}")
            
        return None

    def check_emergency_close(self, position, account_balance):
        """检查是否需要紧急平仓"""
        # 如果未启用紧急平仓，直接返回False
        if not self.position_config.get("emergency_close", {}).get("enabled", False):
            return False
            
        # 获取最大允许亏损百分比
        max_loss_percent = self.position_config.get("emergency_close", {}).get("max_loss_percent", 2.0)
        
        # 计算最大允许亏损金额
        max_loss_amount = account_balance * max_loss_percent / 100
        
        # 如果亏损超过最大允许亏损，触发紧急平仓
        if position.profit < -max_loss_amount:
            logger.warning(f"持仓 {position.symbol} #{position.ticket} 亏损 {position.profit} 超过最大允许亏损 {max_loss_amount}，触发紧急平仓")
            return True
            
        return False

    def close_position(self, ticket):
        """平仓"""
        try:
            # 获取持仓信息
            position = mt5.positions_get(ticket=ticket)
            if not position:
                logger.error(f"无法获取持仓信息: Ticket {ticket}")
                return False
                
            position = position[0]
            
            # 检查是否可以平仓
            data_fetcher = MT5DataFetcher()
            if not data_fetcher.initialize():
                logger.error(f"MT5初始化失败，无法平仓: Ticket {ticket}")
                return False
                
            # 使用新方法检查是否可以平仓
            if not data_fetcher.can_close_position(position.symbol):
                logger.warning(f"当前市场状态不允许平仓: Ticket {ticket}, 品种 {position.symbol}")
                return False
            
            # 创建平仓请求
            request = {
                "action": mt5.TRADE_ACTION_DEAL,
                "position": ticket,
                "symbol": position.symbol,
                "volume": position.volume,
                "type": mt5.ORDER_TYPE_SELL if position.type == mt5.POSITION_TYPE_BUY else mt5.ORDER_TYPE_BUY,
                "price": mt5.symbol_info_tick(position.symbol).bid if position.type == mt5.POSITION_TYPE_BUY else mt5.symbol_info_tick(position.symbol).ask,
                "deviation": 20,
                "magic": 234000,
                "comment": "平仓",
                "type_time": mt5.ORDER_TIME_GTC,
                "type_filling": mt5.ORDER_FILLING_IOC,
            }
            
            # 发送平仓请求
            result = mt5.order_send(request)
            if result.retcode != mt5.TRADE_RETCODE_DONE:
                error_code = result.retcode
                error_message = f"平仓失败: Ticket {ticket}, 错误代码: {error_code}"
                
                # 获取错误描述
                if error_code in MT5_ERROR_CODES:
                    error_message += f", 注释: {MT5_ERROR_CODES[error_code]}"
                    
                logger.error(error_message)
                return False
                
            logger.info(f"平仓成功: Ticket {ticket}, 品种 {position.symbol}, 交易量 {position.volume}")
            return True
            
        except Exception as e:
            logger.error(f"平仓出错: Ticket {ticket}, 错误: {str(e)}", exc_info=True)
            return False

    def adjust_stop_loss(self, symbol, direction, entry_price, atr_value, risk_factor=1.0):
        """调整止损价格"""
        try:
            # 获取品种信息
            symbol_info = mt5.symbol_info(symbol)
            if symbol_info is None:
                logger.error(f"无法获取品种信息: {symbol}")
                return None
                
            # 获取点值
            point = symbol_info.point
            
            # 检查参数类型
            if not isinstance(direction, str) and not isinstance(direction, int):
                logger.error(f"无效的交易方向类型: {type(direction)}, 值: {direction}")
                return None
                
            # 如果direction是数值，可能是价格被错误传入，记录错误并返回None
            if isinstance(direction, (int, float)):
                logger.error(f"无效的交易方向: {direction}")
                return None
                
            # 根据方向计算止损价格
            if direction == "LONG" or direction == mt5.POSITION_TYPE_BUY:
                # 多单止损 = 入场价 - ATR值 * 风险因子
                sl_price = entry_price - (atr_value * risk_factor)
                # 四舍五入到合适的小数位
                digits = symbol_info.digits
                sl_price = round(sl_price, digits)
                
                logger.info(f"{symbol} 多单止损计算: 入场价 {entry_price} - (ATR {atr_value} * 风险因子 {risk_factor}) = {sl_price}")
                
            elif direction == "SHORT" or direction == mt5.POSITION_TYPE_SELL:
                # 空单止损 = 入场价 + ATR值 * 风险因子
                sl_price = entry_price + (atr_value * risk_factor)
                # 四舍五入到合适的小数位
                digits = symbol_info.digits
                sl_price = round(sl_price, digits)
                
                logger.info(f"{symbol} 空单止损计算: 入场价 {entry_price} + (ATR {atr_value} * 风险因子 {risk_factor}) = {sl_price}")
                
            else:
                logger.error(f"无效的交易方向: {direction}")
                return None
                
            return sl_price
            
        except Exception as e:
            logger.error(f"调整止损价格出错: {str(e)}", exc_info=True)
            return None

    def _get_direction_from_comment(self, comment):
        """从注释中提取交易方向"""
        if not comment:
            return None
            
        # 检查注释中的方向标识
        if 'L' in comment:
            return 'buy'
        elif 'S' in comment:
            return 'sell'
            
        return None