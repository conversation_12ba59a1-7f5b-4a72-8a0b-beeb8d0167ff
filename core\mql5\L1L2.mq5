//+------------------------------------------------------------------+
//|                                               HighLowPattern.mq5 |
//|                                  Copyright 2024, MetaQuotes Ltd. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, MetaQuotes Ltd."
#property link      "https://www.mql5.com"
#property version   "1.00"
#property indicator_chart_window
#property indicator_buffers 2
#property indicator_plots   2

//--- 绘制高点形态
#property indicator_label1  "Higher High"
#property indicator_type1   DRAW_ARROW
#property indicator_color1  clrLime
#property indicator_style1  STYLE_SOLID
#property indicator_width1  2

//--- 绘制低点形态
#property indicator_label2  "Lower Low"
#property indicator_type2   DRAW_ARROW
#property indicator_color2  clrRed
#property indicator_style2  STYLE_SOLID
#property indicator_width2  2

//--- 输入参数
input int      LookbackBars = 5;       // 回溯分析的K线数量
input bool     ShowLabels = true;      // 显示文本标签
input color    HHColor = clrLime;      // 高一高二颜色
input color    LLColor = clrRed;       // 低一低二颜色
input int      MaxPatterns = 4;        // 最大保留的形态数量
input int      LabelDistance = 25;     // 标签与K线的距离(点数)
input int      FontSize = 6;           // 标签字体大小
input int      LabelShift = 2;         // 标签水平偏移(柱数)

//--- 指标缓冲区
double         HigherHighBuffer[];
double         LowerLowBuffer[];

//--- 全局变量
int            arrow_up_code = 233;    // 上箭头代码
int            arrow_down_code = 234;  // 下箭头代码
string         prefix = "HL_Pattern_"; // 对象前缀

//+------------------------------------------------------------------+
//| Custom indicator initialization function                         |
//+------------------------------------------------------------------+
int OnInit()
{
   //--- 指标缓冲区映射
   SetIndexBuffer(0, HigherHighBuffer, INDICATOR_DATA);
   SetIndexBuffer(1, LowerLowBuffer, INDICATOR_DATA);
   
   //--- 设置箭头代码
   PlotIndexSetInteger(0, PLOT_ARROW, arrow_up_code);
   PlotIndexSetInteger(1, PLOT_ARROW, arrow_down_code);
   
   //--- 设置指标绘制的偏移
   PlotIndexSetInteger(0, PLOT_ARROW_SHIFT, 5);
   PlotIndexSetInteger(1, PLOT_ARROW_SHIFT, -5);
   
   //--- 设置指标颜色
   PlotIndexSetInteger(0, PLOT_LINE_COLOR, HHColor);
   PlotIndexSetInteger(1, PLOT_LINE_COLOR, LLColor);
   
   //--- 设置空值
   PlotIndexSetDouble(0, PLOT_EMPTY_VALUE, 0.0);
   PlotIndexSetDouble(1, PLOT_EMPTY_VALUE, 0.0);
   
   //--- 设置指标名称
   IndicatorSetString(INDICATOR_SHORTNAME, "High Low Pattern Detector");
   
   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Custom indicator iteration function                              |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[])
{
   //--- 检查数据点是否足够
   if(rates_total < LookbackBars + 2)
      return(0);
   
   //--- 计算起始位置
   int start;
   if(prev_calculated == 0)
   {
      start = LookbackBars + 2;
      
      // 初始化缓冲区
      ArrayInitialize(HigherHighBuffer, 0.0);
      ArrayInitialize(LowerLowBuffer, 0.0);
      
      // 清除旧的对象
      DeleteAllObjects();
   }
   else
   {
      start = prev_calculated - 1;
   }
   
   // 用于跟踪已找到的形态数量
   static int hh_count = 0;
   static int ll_count = 0;
   
   // 存储最近形态的时间
   static datetime hh_times[];
   static datetime ll_times[];
   
   // 初始化数组
   if(prev_calculated == 0)
   {
      ArrayResize(hh_times, MaxPatterns);
      ArrayResize(ll_times, MaxPatterns);
      ArrayInitialize(hh_times, 0);
      ArrayInitialize(ll_times, 0);
      hh_count = 0;
      ll_count = 0;
   }
   
   //--- 主循环
   for(int i = start; i < rates_total; i++)
   {
      //--- 初始化缓冲区值
      HigherHighBuffer[i] = 0.0;
      LowerLowBuffer[i] = 0.0;
      
      //--- 寻找高一高二形态
      bool higher_high = false;
      int hh_index1 = 0, hh_index2 = 0;
      
      if(IsHigherHigh(high, i, LookbackBars, hh_index1, hh_index2))
      {
         HigherHighBuffer[i] = high[i];
         higher_high = true;
         
         if(ShowLabels)
         {
            // 检查是否已经有这个时间点的标记
            bool exists = false;
            for(int j = 0; j < MaxPatterns; j++)
            {
               if(hh_times[j] == time[i])
               {
                  exists = true;
                  break;
               }
            }
            
            if(!exists)
            {
               // 删除最旧的标记，如果已达到最大数量
               if(hh_count >= MaxPatterns)
               {
                  // 找到最旧的时间
                  datetime oldest_time = hh_times[0];
                  int oldest_idx = 0;
                  
                  for(int j = 1; j < MaxPatterns; j++)
                  {
                     if(hh_times[j] < oldest_time)
                     {
                        oldest_time = hh_times[j];
                        oldest_idx = j;
                     }
                  }
                  
                  // 删除最旧的标记
                  string old_name = prefix + "HH_" + TimeToString(oldest_time);
                  if(ObjectFind(0, old_name) >= 0)
                     ObjectDelete(0, old_name);
                  
                  // 更新时间数组
                  hh_times[oldest_idx] = time[i];
               }
               else
               {
                  // 添加新时间
                  hh_times[hh_count] = time[i];
                  hh_count++;
               }
               
               // 创建新标记
               string name = prefix + "HH_" + TimeToString(time[i]);
               double label_price = high[i] + LabelDistance * _Point;
               // 检查是否与现有标签太近
               for(int j = 0; j < hh_count; j++)
               {
                  if(hh_times[j] != time[i] && MathAbs(iBarShift(Symbol(), PERIOD_CURRENT, hh_times[j]) - iBarShift(Symbol(), PERIOD_CURRENT, time[i])) < 3)
                  {
                     // 如果太近，增加垂直距离
                     label_price += 10 * _Point;
                  }
               }
               CreateLabel(name, "H1H2", time[i], label_price, HHColor, FontSize);
            }
         }
      }
      
      //--- 寻找低一低二形态
      bool lower_low = false;
      int ll_index1 = 0, ll_index2 = 0;
      
      if(IsLowerLow(low, i, LookbackBars, ll_index1, ll_index2))
      {
         LowerLowBuffer[i] = low[i];
         lower_low = true;
         
         if(ShowLabels)
         {
            // 检查是否已经有这个时间点的标记
            bool exists = false;
            for(int j = 0; j < MaxPatterns; j++)
            {
               if(ll_times[j] == time[i])
               {
                  exists = true;
                  break;
               }
            }
            
            if(!exists)
            {
               // 删除最旧的标记，如果已达到最大数量
               if(ll_count >= MaxPatterns)
               {
                  // 找到最旧的时间
                  datetime oldest_time = ll_times[0];
                  int oldest_idx = 0;
                  
                  for(int j = 1; j < MaxPatterns; j++)
                  {
                     if(ll_times[j] < oldest_time)
                     {
                        oldest_time = ll_times[j];
                        oldest_idx = j;
                     }
                  }
                  
                  // 删除最旧的标记
                  string old_name = prefix + "LL_" + TimeToString(oldest_time);
                  if(ObjectFind(0, old_name) >= 0)
                     ObjectDelete(0, old_name);
                  
                  // 更新时间数组
                  ll_times[oldest_idx] = time[i];
               }
               else
               {
                  // 添加新时间
                  ll_times[ll_count] = time[i];
                  ll_count++;
               }
               
               // 创建新标记
               string name = prefix + "LL_" + TimeToString(time[i]);
               double label_price = low[i] - LabelDistance * _Point;
               // 检查是否与现有标签太近
               for(int j = 0; j < ll_count; j++)
               {
                  if(ll_times[j] != time[i] && MathAbs(iBarShift(Symbol(), PERIOD_CURRENT, ll_times[j]) - iBarShift(Symbol(), PERIOD_CURRENT, time[i])) < 3)
                  {
                     // 如果太近，增加垂直距离
                     label_price -= 10 * _Point;
                  }
               }
               CreateLabel(name, "L1L2", time[i], label_price, LLColor, FontSize);
            }
         }
      }
   }
   
   //--- 返回计算的K线数量
   return(rates_total);
}

//+------------------------------------------------------------------+
//| 检查是否形成高一高二形态                                           |
//+------------------------------------------------------------------+
bool IsHigherHigh(const double &high[], int index, int lookback, int &index1, int &index2)
{
   // 找到当前K线之前的最高点
   double prev_high = 0;
   int prev_high_index = 0;
   
   for(int i = 1; i <= lookback; i++)
   {
      if(index - i < 0) break;
      
      if(high[index - i] > prev_high)
      {
         prev_high = high[index - i];
         prev_high_index = index - i;
      }
   }
   
   // 如果当前高点不高于前一个高点，返回false
   if(high[index] <= prev_high)
      return false;
      
   // 找到前一个高点之前的最高点
   double prev_prev_high = 0;
   int prev_prev_high_index = 0;
   
   for(int i = 1; i <= lookback; i++)
   {
      if(prev_high_index - i < 0) break;
      
      if(high[prev_high_index - i] > prev_prev_high)
      {
         prev_prev_high = high[prev_high_index - i];
         prev_prev_high_index = prev_high_index - i;
      }
   }
   
   // 如果前一个高点不高于再前一个高点，返回false
   if(prev_high <= prev_prev_high)
      return false;
      
   // 保存找到的索引
   index1 = prev_high_index;
   index2 = prev_prev_high_index;
   
   return true;
}

//+------------------------------------------------------------------+
//| 检查是否形成低一低二形态                                           |
//+------------------------------------------------------------------+
bool IsLowerLow(const double &low[], int index, int lookback, int &index1, int &index2)
{
   // 找到当前K线之前的最低点
   double prev_low = DBL_MAX;
   int prev_low_index = 0;
   
   for(int i = 1; i <= lookback; i++)
   {
      if(index - i < 0) break;
      
      if(low[index - i] < prev_low)
      {
         prev_low = low[index - i];
         prev_low_index = index - i;
      }
   }
   
   // 如果当前低点不低于前一个低点，返回false
   if(low[index] >= prev_low)
      return false;
      
   // 找到前一个低点之前的最低点
   double prev_prev_low = DBL_MAX;
   int prev_prev_low_index = 0;
   
   for(int i = 1; i <= lookback; i++)
   {
      if(prev_low_index - i < 0) break;
      
      if(low[prev_low_index - i] < prev_prev_low)
      {
         prev_prev_low = low[prev_low_index - i];
         prev_prev_low_index = prev_low_index - i;
      }
   }
   
   // 如果前一个低点不低于再前一个低点，返回false
   if(prev_low >= prev_prev_low)
      return false;
      
   // 保存找到的索引
   index1 = prev_low_index;
   index2 = prev_prev_low_index;
   
   return true;
}

//+------------------------------------------------------------------+
//| 创建文本标签                                                      |
//+------------------------------------------------------------------+
void CreateLabel(string name, string text, datetime time, double price, color clr, int font_size)
{
   if(ObjectFind(0, name) >= 0)
      ObjectDelete(0, name);
      
   // 使用OBJ_LABEL而不是OBJ_TEXT，更好地控制位置
   ObjectCreate(0, name, OBJ_LABEL, 0, time, price);
   ObjectSetString(0, name, OBJPROP_TEXT, text);
   ObjectSetString(0, name, OBJPROP_FONT, "Arial Bold");
   ObjectSetInteger(0, name, OBJPROP_FONTSIZE, font_size);
   ObjectSetInteger(0, name, OBJPROP_COLOR, clr);
   ObjectSetInteger(0, name, OBJPROP_CORNER, CORNER_LEFT_UPPER);
   ObjectSetInteger(0, name, OBJPROP_ANCHOR, ANCHOR_LEFT);
   
   // 设置标签背景
   ObjectSetInteger(0, name, OBJPROP_BACK, false);
   ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
   ObjectSetInteger(0, name, OBJPROP_SELECTED, false);
   ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
   
   // 设置标签位置
   datetime label_time = time;
   int shift = iBarShift(Symbol(), PERIOD_CURRENT, time);
   if(shift >= LabelShift)
      label_time = iTime(Symbol(), PERIOD_CURRENT, shift - LabelShift);
   
   ObjectSetInteger(0, name, OBJPROP_TIME, label_time);
   ObjectSetDouble(0, name, OBJPROP_PRICE, price);
}

//+------------------------------------------------------------------+
//| 删除所有指标创建的对象                                             |
//+------------------------------------------------------------------+
void DeleteAllObjects()
{
   for(int i = ObjectsTotal(0, 0, -1) - 1; i >= 0; i--)
   {
      string name = ObjectName(0, i);
      if(StringFind(name, prefix) == 0)
         ObjectDelete(0, name);
   }
}

//+------------------------------------------------------------------+
//| 指标清理函数                                                      |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   // 删除所有创建的对象
   DeleteAllObjects();
}
//+------------------------------------------------------------------+