---
description: 
globs: 
alwaysApply: false
---
# Context7验证要求

## 强制验证场景
**以下情况无条件需要Context7验证**:
- 引入新的外部依赖（库、框架、重要模块）
- 核心架构决策或影响多模块的重大重构
- 使用不常用或不确定的API/SDK功能
- 选择关键技术方案、核心算法或重要设计模式
- AI对自身知识准确性或时效性有任何疑虑
- 用户明确要求进行验证

## 验证流程
```typescript
// 按此顺序使用Context7工具:
// 1. resolve_library_id="库名" 
// 2. get_library_docs="具体查询内容"
```

## 文档标准
使用外部知识时，始终包含适当的归属信息：

### Context7验证信息
```typescript
// Context7验证: 库名@版本 (查询日期) - 关键特性或说明
```

### AI知识库信息
```typescript  
// AI知识库: 来源描述 (数据截止日期) - 置信度指标
```

### 历史项目验证
```typescript
// 历史项目验证: 项目标识 (验证日期) - 验证范围
```

## 自动升级场景
- **错误触发升级**: 如果AI提供的代码在用户环境中失败（非配置问题），自动将相关Context7验证升级到最严格级别
- **风险评估**: 即使在简单任务中，对于有深远架构影响的决策，主动建议验证升级
- **用户覆盖**: 用户可随时要求对任何信息进行更高验证标准

## 知识质量保证
- 优先考虑精确匹配和最新文档
- 在可用时交叉引用多个来源
- 标记过时或冲突的信息
- 在适用时提供版本特定的指导


