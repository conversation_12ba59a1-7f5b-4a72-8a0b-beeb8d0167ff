-- =============================================
-- 账户表：存储不同经纪商的MT5账户信息
-- =============================================
CREATE TABLE accounts (
    account_id INT AUTO_INCREMENT PRIMARY KEY,  -- 账户ID，自增主键
    broker_name VARCHAR(100) NOT NULL,          -- 经纪商名称
    account_number VARCHAR(50) NOT NULL,        -- MT5账户号码
    account_type ENUM('DEMO', 'REAL') NOT NULL, -- 账户类型：模拟或真实
    currency VARCHAR(10) NOT NULL,              -- 账户货币类型
    leverage VARCHAR(20),                       -- 账户杠杆比例
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,           -- 记录创建时间
    last_updated DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, -- 记录最后更新时间
    UNIQUE KEY (broker_name, account_number)    -- 确保经纪商和账号组合的唯一性
);

-- =============================================
-- 交易记录表：存储所有开仓和平仓信息
-- =============================================
CREATE TABLE trades (
    trade_id INT AUTO_INCREMENT PRIMARY KEY,    -- 交易ID，自增主键
    account_id INT NOT NULL,                    -- 关联的账户ID
    mt5_ticket BIGINT NOT NULL,                 -- MT5系统中的订单号
    symbol VARCHAR(20) NOT NULL,                -- 交易品种
    trade_type ENUM('BUY', 'SELL') NOT NULL,    -- 交易类型：买入或卖出
    volume DECIMAL(10,2) NOT NULL,              -- 交易手数
    open_price DECIMAL(10,5) NOT NULL,          -- 开仓价格
    close_price DECIMAL(10,5),                  -- 平仓价格，未平仓时为NULL
    open_time DATETIME NOT NULL,                -- 开仓时间
    close_time DATETIME,                        -- 平仓时间，未平仓时为NULL
    sl_price DECIMAL(10,5),                     -- 止损价格
    tp_price DECIMAL(10,5),                     -- 止盈价格
    profit DECIMAL(10,2),                       -- 交易盈亏
    commission DECIMAL(10,2) DEFAULT 0,         -- 佣金
    swap DECIMAL(10,2) DEFAULT 0,               -- 隔夜利息
    comment VARCHAR(255),                       -- 交易备注
    timeframe VARCHAR(10) NOT NULL,             -- 交易周期
    signal_type VARCHAR(50),                    -- 信号类型
    signal_strength VARCHAR(20),                -- 信号强度
    atr_value DECIMAL(10,5),                    -- 开仓时的ATR值
    max_profit DECIMAL(10,2) DEFAULT 0,         -- 最大浮盈
    max_loss DECIMAL(10,2) DEFAULT 0,           -- 最大浮亏
    risk_reward_ratio DECIMAL(5,2),             -- 风险回报比
    status ENUM('OPEN', 'CLOSED', 'PARTIALLY_CLOSED') NOT NULL DEFAULT 'OPEN', -- 交易状态
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,           -- 记录创建时间
    last_updated DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, -- 记录最后更新时间
    FOREIGN KEY (account_id) REFERENCES accounts(account_id), -- 外键关联账户表
    INDEX (symbol),                             -- 品种索引，加速查询
    INDEX (open_time),                          -- 开仓时间索引
    INDEX (close_time),                         -- 平仓时间索引
    INDEX (status)                              -- 状态索引
);

-- =============================================
-- 持仓管理表：记录止损修改等持仓管理操作
-- =============================================
CREATE TABLE position_management (
    management_id INT AUTO_INCREMENT PRIMARY KEY, -- 管理操作ID，自增主键
    trade_id INT NOT NULL,                      -- 关联的交易ID
    action_type ENUM('SL_MODIFY', 'TP_MODIFY', 'PARTIAL_CLOSE') NOT NULL, -- 操作类型
    action_time DATETIME NOT NULL,              -- 操作时间
    old_value DECIMAL(10,5),                    -- 修改前的值
    new_value DECIMAL(10,5),                    -- 修改后的值
    price_at_action DECIMAL(10,5) NOT NULL,     -- 操作时的市场价格
    profit_at_action DECIMAL(10,2),             -- 操作时的浮动盈亏
    reason VARCHAR(100),                        -- 操作原因
    details TEXT,                               -- 详细说明
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP, -- 记录创建时间
    FOREIGN KEY (trade_id) REFERENCES trades(trade_id), -- 外键关联交易表
    INDEX (action_time),                        -- 操作时间索引
    INDEX (action_type)                         -- 操作类型索引
);

-- =============================================
-- 信号记录表：记录系统生成的交易信号
-- =============================================
CREATE TABLE signals (
    signal_id INT AUTO_INCREMENT PRIMARY KEY,   -- 信号ID，自增主键
    symbol VARCHAR(20) NOT NULL,                -- 品种
    timeframe VARCHAR(10) NOT NULL,             -- 时间周期
    direction ENUM('LONG', 'SHORT') NOT NULL,   -- 信号方向
    signal_type VARCHAR(50) NOT NULL,           -- 信号类型
    signal_strength VARCHAR(20) NOT NULL,       -- 信号强度
    generated_time DATETIME NOT NULL,           -- 信号生成时间
    price_at_signal DECIMAL(10,5) NOT NULL,     -- 信号生成时的价格
    atr_value DECIMAL(10,5),                    -- 信号生成时的ATR值
    satisfied_patterns TEXT,                    -- 满足的模式
    executed BOOLEAN DEFAULT FALSE,             -- 是否已执行
    execution_result VARCHAR(50),               -- 执行结果
    execution_reason TEXT,                      -- 执行或未执行的原因
    related_trade_id INT,                       -- 关联的交易ID
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP, -- 记录创建时间
    FOREIGN KEY (related_trade_id) REFERENCES trades(trade_id), -- 外键关联交易表
    INDEX (symbol),                             -- 品种索引
    INDEX (timeframe),                          -- 时间周期索引
    INDEX (generated_time),                     -- 生成时间索引
    INDEX (executed)                            -- 执行状态索引
);

-- =============================================
-- 账户余额表：记录账户余额变化
-- =============================================
CREATE TABLE account_balance (
    balance_id INT AUTO_INCREMENT PRIMARY KEY,  -- 余额记录ID，自增主键
    account_id INT NOT NULL,                    -- 关联的账户ID
    snapshot_time DATETIME NOT NULL,            -- 快照时间
    balance DECIMAL(15,2) NOT NULL,             -- 账户余额
    equity DECIMAL(15,2) NOT NULL,              -- 账户净值
    margin DECIMAL(15,2) NOT NULL,              -- 已用保证金
    free_margin DECIMAL(15,2) NOT NULL,         -- 可用保证金
    margin_level DECIMAL(10,2),                 -- 保证金水平
    open_positions INT NOT NULL DEFAULT 0,      -- 开仓数量
    floating_profit DECIMAL(15,2) DEFAULT 0,    -- 浮动盈亏
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP, -- 记录创建时间
    FOREIGN KEY (account_id) REFERENCES accounts(account_id), -- 外键关联账户表
    INDEX (snapshot_time),                      -- 快照时间索引
    INDEX (account_id, snapshot_time)           -- 账户ID和快照时间组合索引
);

-- =============================================
-- 系统运行记录表：记录系统每次运行的情况
-- =============================================
CREATE TABLE system_runs (
    run_id INT AUTO_INCREMENT PRIMARY KEY,      -- 运行ID，自增主键
    account_id INT NOT NULL,                    -- 关联的账户ID
    start_time DATETIME NOT NULL,               -- 开始时间
    end_time DATETIME,                          -- 结束时间
    duration_seconds DECIMAL(10,2),             -- 运行时长(秒)
    signals_generated INT DEFAULT 0,            -- 生成的信号数量
    positions_managed INT DEFAULT 0,            -- 管理的持仓数量
    new_trades_opened INT DEFAULT 0,            -- 新开仓数量
    trades_closed INT DEFAULT 0,                -- 平仓数量
    run_status ENUM('SUCCESS', 'PARTIAL_SUCCESS', 'FAILURE') NOT NULL, -- 运行状态
    error_message TEXT,                         -- 错误信息
    details TEXT,                               -- 详细信息
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP, -- 记录创建时间
    FOREIGN KEY (account_id) REFERENCES accounts(account_id), -- 外键关联账户表
    INDEX (start_time),                         -- 开始时间索引
    INDEX (run_status)                          -- 运行状态索引
);

-- =============================================
-- 错误日志表：记录系统错误和异常
-- =============================================
CREATE TABLE error_logs (
    log_id INT AUTO_INCREMENT PRIMARY KEY,      -- 日志ID，自增主键
    account_id INT,                             -- 关联的账户ID
    run_id INT,                                 -- 关联的运行ID
    error_time DATETIME NOT NULL,               -- 错误发生时间
    error_level ENUM('INFO', 'WARNING', 'ERROR', 'CRITICAL') NOT NULL, -- 错误级别
    error_source VARCHAR(100) NOT NULL,         -- 错误来源
    error_message TEXT NOT NULL,                -- 错误信息
    stack_trace TEXT,                           -- 堆栈跟踪
    additional_info TEXT,                       -- 附加信息
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP, -- 记录创建时间
    FOREIGN KEY (account_id) REFERENCES accounts(account_id), -- 外键关联账户表
    FOREIGN KEY (run_id) REFERENCES system_runs(run_id), -- 外键关联系统运行表
    INDEX (error_time),                         -- 错误时间索引
    INDEX (error_level)                         -- 错误级别索引
);

-- =============================================
-- 交易统计表：按周期统计交易绩效
-- =============================================
CREATE TABLE trade_statistics (
    stat_id INT AUTO_INCREMENT PRIMARY KEY,     -- 统计ID，自增主键
    account_id INT NOT NULL,                    -- 关联的账户ID
    period_type ENUM('DAILY', 'WEEKLY', 'MONTHLY', 'YEARLY') NOT NULL, -- 统计周期类型
    period_start DATETIME NOT NULL,             -- 周期开始时间
    period_end DATETIME NOT NULL,               -- 周期结束时间
    total_trades INT NOT NULL DEFAULT 0,        -- 总交易次数
    winning_trades INT NOT NULL DEFAULT 0,      -- 盈利交易次数
    losing_trades INT NOT NULL DEFAULT 0,       -- 亏损交易次数
    win_rate DECIMAL(5,2),                      -- 胜率
    profit_factor DECIMAL(5,2),                 -- 盈亏比
    total_profit DECIMAL(15,2) NOT NULL DEFAULT 0, -- 总盈利
    total_loss DECIMAL(15,2) NOT NULL DEFAULT 0,-- 总亏损
    net_profit DECIMAL(15,2) NOT NULL DEFAULT 0,-- 净盈亏
    average_win DECIMAL(10,2),                  -- 平均盈利
    average_loss DECIMAL(10,2),                 -- 平均亏损
    largest_win DECIMAL(10,2),                  -- 最大盈利
    largest_loss DECIMAL(10,2),                 -- 最大亏损
    average_holding_time_minutes INT,           -- 平均持仓时间(分钟)
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP, -- 记录创建时间
    FOREIGN KEY (account_id) REFERENCES accounts(account_id), -- 外键关联账户表
    UNIQUE KEY (account_id, period_type, period_start), -- 确保每个账户每个周期只有一条记录
    INDEX (period_start, period_end)            -- 周期时间索引
);

-- =============================================
-- 品种表现统计表：按品种统计交易绩效
-- =============================================
CREATE TABLE symbol_performance (
    performance_id INT AUTO_INCREMENT PRIMARY KEY, -- 表现ID，自增主键
    account_id INT NOT NULL,                    -- 关联的账户ID
    symbol VARCHAR(20) NOT NULL,                -- 品种
    period_type ENUM('DAILY', 'WEEKLY', 'MONTHLY', 'YEARLY') NOT NULL, -- 统计周期类型
    period_start DATETIME NOT NULL,             -- 周期开始时间
    period_end DATETIME NOT NULL,               -- 周期结束时间
    total_trades INT NOT NULL DEFAULT 0,        -- 总交易次数
    winning_trades INT NOT NULL DEFAULT 0,      -- 盈利交易次数
    losing_trades INT NOT NULL DEFAULT 0,       -- 亏损交易次数
    win_rate DECIMAL(5,2),                      -- 胜率
    net_profit DECIMAL(15,2) NOT NULL DEFAULT 0,-- 净盈亏
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP, -- 记录创建时间
    FOREIGN KEY (account_id) REFERENCES accounts(account_id), -- 外键关联账户表
    UNIQUE KEY (account_id, symbol, period_type, period_start), -- 确保每个账户每个品种每个周期只有一条记录
    INDEX (symbol),                             -- 品种索引
    INDEX (period_start, period_end)            -- 周期时间索引
);

-- =============================================
-- 当前持仓视图：查看所有未平仓的交易
-- =============================================
CREATE VIEW current_positions_view AS
SELECT 
    t.trade_id,                                 -- 交易ID
    t.account_id,                               -- 账户ID
    a.broker_name,                              -- 经纪商名称
    t.mt5_ticket,                               -- MT5订单号
    t.symbol,                                   -- 交易品种
    t.trade_type,                               -- 交易类型
    t.volume,                                   -- 交易手数
    t.open_price,                               -- 开仓价格
    t.open_time,                                -- 开仓时间
    t.sl_price,                                 -- 止损价格
    t.tp_price,                                 -- 止盈价格
    t.timeframe,                                -- 交易周期
    t.signal_type,                              -- 信号类型
    t.signal_strength,                          -- 信号强度
    t.atr_value,                                -- ATR值
    TIMESTAMPDIFF(MINUTE, t.open_time, NOW()) AS holding_time_minutes -- 持仓时间(分钟)
FROM 
    trades t                                    -- 交易表
JOIN 
    accounts a ON t.account_id = a.account_id   -- 关联账户表
WHERE 
    t.status = 'OPEN';                          -- 只查询未平仓的交易

-- =============================================
-- 交易绩效视图：按品种、周期、信号类型统计交易绩效
-- =============================================
CREATE VIEW trade_performance_view AS
SELECT 
    t.account_id,                               -- 账户ID
    a.broker_name,                              -- 经纪商名称
    t.symbol,                                   -- 交易品种
    t.timeframe,                                -- 交易周期
    t.signal_type,                              -- 信号类型
    COUNT(*) AS total_trades,                   -- 总交易次数
    SUM(CASE WHEN t.profit > 0 THEN 1 ELSE 0 END) AS winning_trades, -- 盈利交易次数
    SUM(CASE WHEN t.profit <= 0 THEN 1 ELSE 0 END) AS losing_trades, -- 亏损交易次数
    SUM(CASE WHEN t.profit > 0 THEN 1 ELSE 0 END) / COUNT(*) * 100 AS win_rate, -- 胜率
    SUM(t.profit) AS net_profit,                -- 净盈亏
    AVG(CASE WHEN t.profit > 0 THEN t.profit ELSE NULL END) AS avg_win, -- 平均盈利
    AVG(CASE WHEN t.profit <= 0 THEN t.profit ELSE NULL END) AS avg_loss, -- 平均亏损
    MAX(t.profit) AS max_win,                   -- 最大盈利
    MIN(t.profit) AS max_loss,                  -- 最大亏损
    AVG(TIMESTAMPDIFF(MINUTE, t.open_time, t.close_time)) AS avg_holding_time_minutes -- 平均持仓时间(分钟)
FROM 
    trades t                                    -- 交易表
JOIN 
    accounts a ON t.account_id = a.account_id   -- 关联账户表
WHERE 
    t.status = 'CLOSED'                         -- 只统计已平仓的交易
GROUP BY 
    t.account_id, a.broker_name, t.symbol, t.timeframe, t.signal_type; -- 按账户、经纪商、品种、周期、信号类型分组

-- =============================================
-- 存储过程：更新交易统计数据
-- =============================================
DELIMITER //
CREATE PROCEDURE update_trade_statistics(IN p_account_id INT, IN p_period_type VARCHAR(10))
BEGIN
    DECLARE v_period_start DATETIME;            -- 统计周期开始时间
    DECLARE v_period_end DATETIME;              -- 统计周期结束时间
    
    -- 设置统计周期
    CASE p_period_type
        WHEN 'DAILY' THEN                       -- 日统计
            SET v_period_start = DATE_FORMAT(NOW(), '%Y-%m-%d 00:00:00'); -- 当天开始时间
            SET v_period_end = DATE_FORMAT(NOW(), '%Y-%m-%d 23:59:59');   -- 当天结束时间
        WHEN 'WEEKLY' THEN                      -- 周统计
            SET v_period_start = DATE_SUB(DATE_FORMAT(NOW(), '%Y-%m-%d 00:00:00'), INTERVAL WEEKDAY(NOW()) DAY); -- 本周一
            SET v_period_end = DATE_ADD(v_period_start, INTERVAL 6 DAY);  -- 本周日
            SET v_period_end = DATE_FORMAT(v_period_end, '%Y-%m-%d 23:59:59'); -- 本周日结束时间
        WHEN 'MONTHLY' THEN                     -- 月统计
            SET v_period_start = DATE_FORMAT(NOW(), '%Y-%m-01 00:00:00'); -- 本月1日
            SET v_period_end = LAST_DAY(NOW());                           -- 本月最后一天
            SET v_period_end = DATE_FORMAT(v_period_end, '%Y-%m-%d 23:59:59'); -- 本月最后一天结束时间
        WHEN 'YEARLY' THEN                      -- 年统计
            SET v_period_start = DATE_FORMAT(NOW(), '%Y-01-01 00:00:00'); -- 本年1月1日
            SET v_period_end = DATE_FORMAT(NOW(), '%Y-12-31 23:59:59');   -- 本年12月31日结束时间
    END CASE;
    
    -- 删除已有的统计数据
    DELETE FROM trade_statistics               -- 删除交易统计表中的记录
    WHERE account_id = p_account_id            -- 匹配账户ID
    AND period_type = p_period_type            -- 匹配周期类型
    AND period_start = v_period_start;         -- 匹配周期开始时间
    
    -- 插入新的统计数据
    INSERT INTO trade_statistics (
        account_id, period_type, period_start, period_end,
        total_trades, winning_trades, losing_trades, win_rate,
        profit_factor, total_profit, total_loss, net_profit,
        average_win, average_loss, largest_win, largest_loss,
        average_holding_time_minutes
    )
    SELECT 
        p_account_id,                           -- 账户ID
        p_period_type,                          -- 周期类型
        v_period_start,                         -- 周期开始时间
        v_period_end,                           -- 周期结束时间
        COUNT(*),                               -- 总交易次数
        SUM(CASE WHEN profit > 0 THEN 1 ELSE 0 END), -- 盈利交易次数
        SUM(CASE WHEN profit <= 0 THEN 1 ELSE 0 END), -- 亏损交易次数
        (SUM(CASE WHEN profit > 0 THEN 1 ELSE 0 END) / COUNT(*)) * 100, -- 胜率
        CASE 
            WHEN ABS(SUM(CASE WHEN profit <= 0 THEN profit ELSE 0 END)) = 0 THEN NULL -- 如果没有亏损交易
            ELSE ABS(SUM(CASE WHEN profit > 0 THEN profit ELSE 0 END) / SUM(CASE WHEN profit <= 0 THEN profit ELSE 0 END)) -- 盈亏比
        END,
        SUM(CASE WHEN profit > 0 THEN profit ELSE 0 END), -- 总盈利
        ABS(SUM(CASE WHEN profit <= 0 THEN profit ELSE 0 END)), -- 总亏损(绝对值)
        SUM(profit),                            -- 净盈亏
        AVG(CASE WHEN profit > 0 THEN profit ELSE NULL END), -- 平均盈利
        AVG(CASE WHEN profit <= 0 THEN profit ELSE NULL END), -- 平均亏损
        MAX(profit) AS largest_win,             -- 最大盈利
        MIN(profit) AS largest_loss,            -- 最大亏损
        AVG(TIMESTAMPDIFF(MINUTE, open_time, close_time)) AS average_holding_time_minutes -- 平均持仓时间(分钟)
    FROM 
        trades                                  -- 交易表
    WHERE 
        account_id = p_account_id               -- 匹配账户ID
        AND status = 'CLOSED'                   -- 只统计已平仓的交易
        AND close_time BETWEEN v_period_start AND v_period_end; -- 在指定周期内平仓的交易
END //

-- =============================================
-- 存储过程：更新品种表现统计数据
-- =============================================
CREATE PROCEDURE update_symbol_performance(IN p_account_id INT, IN p_period_type VARCHAR(10))
BEGIN
    DECLARE v_period_start DATETIME;            -- 统计周期开始时间
    DECLARE v_period_end DATETIME;              -- 统计周期结束时间
    
    -- 设置统计周期
    CASE p_period_type
        WHEN 'DAILY' THEN                       -- 日统计
            SET v_period_start = DATE_FORMAT(NOW(), '%Y-%m-%d 00:00:00'); -- 当天开始时间
            SET v_period_end = DATE_FORMAT(NOW(), '%Y-%m-%d 23:59:59');   -- 当天结束时间
        WHEN 'WEEKLY' THEN                      -- 周统计
            SET v_period_start = DATE_SUB(DATE_FORMAT(NOW(), '%Y-%m-%d 00:00:00'), INTERVAL WEEKDAY(NOW()) DAY); -- 本周一
            SET v_period_end = DATE_ADD(v_period_start, INTERVAL 6 DAY);  -- 本周日
            SET v_period_end = DATE_FORMAT(v_period_end, '%Y-%m-%d 23:59:59'); -- 本周日结束时间
        WHEN 'MONTHLY' THEN                     -- 月统计
            SET v_period_start = DATE_FORMAT(NOW(), '%Y-%m-01 00:00:00'); -- 本月1日
            SET v_period_end = LAST_DAY(NOW());                           -- 本月最后一天
            SET v_period_end = DATE_FORMAT(v_period_end, '%Y-%m-%d 23:59:59'); -- 本月最后一天结束时间
        WHEN 'YEARLY' THEN                      -- 年统计
            SET v_period_start = DATE_FORMAT(NOW(), '%Y-01-01 00:00:00'); -- 本年1月1日
            SET v_period_end = DATE_FORMAT(NOW(), '%Y-12-31 23:59:59');   -- 本年12月31日结束时间
    END CASE;
    
    -- 删除已有的统计数据
    DELETE FROM symbol_performance              -- 删除品种表现统计表中的记录
    WHERE account_id = p_account_id             -- 匹配账户ID
    AND period_type = p_period_type             -- 匹配周期类型
    AND period_start = v_period_start;          -- 匹配周期开始时间
    
    -- 插入新的统计数据
    INSERT INTO symbol_performance (
        account_id, symbol, period_type, period_start, period_end,
        total_trades, winning_trades, losing_trades, win_rate, net_profit
    )
    SELECT 
        account_id,                             -- 账户ID
        symbol,                                 -- 交易品种
        p_period_type,                          -- 周期类型
        v_period_start,                         -- 周期开始时间
        v_period_end,                           -- 周期结束时间
        COUNT(*),                               -- 总交易次数
        SUM(CASE WHEN profit > 0 THEN 1 ELSE 0 END), -- 盈利交易次数
        SUM(CASE WHEN profit <= 0 THEN 1 ELSE 0 END), -- 亏损交易次数
        (SUM(CASE WHEN profit > 0 THEN 1 ELSE 0 END) / COUNT(*)) * 100, -- 胜率
        SUM(profit)                             -- 净盈亏
    FROM 
        trades                                  -- 交易表
    WHERE 
        account_id = p_account_id               -- 匹配账户ID
        AND status = 'CLOSED'                   -- 只统计已平仓的交易
        AND close_time BETWEEN v_period_start AND v_period_end -- 在指定周期内平仓的交易
    GROUP BY 
        account_id, symbol;                     -- 按账户ID和品种分组
END //

-- =============================================
-- 存储过程：记录账户余额
-- =============================================
CREATE PROCEDURE record_account_balance(IN p_account_id INT)
BEGIN
    -- 从MT5获取的数据将通过应用程序插入
    -- 此存储过程仅作为占位符
    SELECT 'Account balance recording procedure called' AS message; -- 返回调用信息
END //

-- =============================================
-- 存储过程：记录系统运行
-- =============================================
CREATE PROCEDURE log_system_run(
    IN p_account_id INT,                        -- 账户ID
    IN p_start_time DATETIME,                   -- 开始时间
    IN p_end_time DATETIME,                     -- 结束时间
    IN p_duration_seconds DECIMAL(10,2),        -- 运行时长(秒)
    IN p_signals_generated INT,                 -- 生成的信号数量
    IN p_positions_managed INT,                 -- 管理的持仓数量
    IN p_new_trades_opened INT,                 -- 新开仓数量
    IN p_trades_closed INT,                     -- 平仓数量
    IN p_run_status VARCHAR(20),                -- 运行状态
    IN p_error_message TEXT,                    -- 错误信息
    IN p_details TEXT,                          -- 详细信息
    OUT p_run_id INT                            -- 输出参数：运行ID
)
BEGIN
    INSERT INTO system_runs (                   -- 插入系统运行记录
        account_id, start_time, end_time, duration_seconds,
        signals_generated, positions_managed, new_trades_opened, trades_closed,
        run_status, error_message, details
    ) VALUES (
        p_account_id, p_start_time, p_end_time, p_duration_seconds,
        p_signals_generated, p_positions_managed, p_new_trades_opened, p_trades_closed,
        p_run_status, p_error_message, p_details
    );
    
    SET p_run_id = LAST_INSERT_ID();            -- 获取插入记录的ID
END //

-- =============================================
-- 存储过程：记录错误日志
-- =============================================
CREATE PROCEDURE log_error(
    IN p_account_id INT,                        -- 账户ID
    IN p_run_id INT,                            -- 运行ID
    IN p_error_time DATETIME,                   -- 错误时间
    IN p_error_level VARCHAR(10),               -- 错误级别
    IN p_error_source VARCHAR(100),             -- 错误来源
    IN p_error_message TEXT,                    -- 错误信息
    IN p_stack_trace TEXT,                      -- 堆栈跟踪
    IN p_additional_info TEXT                   -- 附加信息
)
BEGIN
    INSERT INTO error_logs (                    -- 插入错误日志记录
        account_id, run_id, error_time, error_level, error_source,
        error_message, stack_trace, additional_info
    ) VALUES (
        p_account_id, p_run_id, p_error_time, p_error_level, p_error_source,
        p_error_message, p_stack_trace, p_additional_info
    );
END //

-- =============================================
-- 存储过程：记录交易
-- =============================================
CREATE PROCEDURE record_trade(
    IN p_account_id INT,                        -- 账户ID
    IN p_mt5_ticket BIGINT,                     -- MT5订单号
    IN p_symbol VARCHAR(20),                    -- 交易品种
    IN p_trade_type VARCHAR(10),                -- 交易类型
    IN p_volume DECIMAL(10,2),                  -- 交易手数
    IN p_open_price DECIMAL(10,5),              -- 开仓价格
    IN p_open_time DATETIME,                    -- 开仓时间
    IN p_sl_price DECIMAL(10,5),                -- 止损价格
    IN p_tp_price DECIMAL(10,5),                -- 止盈价格
    IN p_comment VARCHAR(255),                  -- 交易备注
    IN p_timeframe VARCHAR(10),                 -- 交易周期
    IN p_signal_type VARCHAR(50),               -- 信号类型
    IN p_signal_strength VARCHAR(20),           -- 信号强度
    IN p_atr_value DECIMAL(10,5),               -- ATR值
    OUT p_trade_id INT                          -- 输出参数：交易ID
)
BEGIN
    INSERT INTO trades (                        -- 插入交易记录
        account_id, mt5_ticket, symbol, trade_type, volume,
        open_price, open_time, sl_price, tp_price, comment,
        timeframe, signal_type, signal_strength, atr_value, status
    ) VALUES (
        p_account_id, p_mt5_ticket, p_symbol, p_trade_type, p_volume,
        p_open_price, p_open_time, p_sl_price, p_tp_price, p_comment,
        p_timeframe, p_signal_type, p_signal_strength, p_atr_value, 'OPEN'
    );
    
    SET p_trade_id = LAST_INSERT_ID();          -- 获取插入记录的ID
END //

-- =============================================
-- 存储过程：平仓交易
-- =============================================
CREATE PROCEDURE close_trade(
    IN p_trade_id INT,                          -- 交易ID
    IN p_close_price DECIMAL(10,5),             -- 平仓价格
    IN p_close_time DATETIME,                   -- 平仓时间
    IN p_profit DECIMAL(10,2),                  -- 交易盈亏
    IN p_commission DECIMAL(10,2),              -- 佣金
    IN p_swap DECIMAL(10,2)                     -- 隔夜利息
)
BEGIN
    UPDATE trades                               -- 更新交易记录
    SET 
        close_price = p_close_price,            -- 设置平仓价格
        close_time = p_close_time,              -- 设置平仓时间
        profit = p_profit,                      -- 设置交易盈亏
        commission = p_commission,              -- 设置佣金
        swap = p_swap,                          -- 设置隔夜利息
        status = 'CLOSED'                       -- 设置交易状态为已平仓
    WHERE 
        trade_id = p_trade_id;                  -- 匹配交易ID
        
    -- 计算并更新风险回报比
    UPDATE trades
    SET risk_reward_ratio = CASE 
            WHEN profit > 0 AND (open_price - sl_price) != 0 THEN  -- 如果是盈利交易且止损有效
                ABS(profit / ((open_price - sl_price) * volume))   -- 计算风险回报比
            ELSE NULL                                              -- 否则设为NULL
        END
    WHERE trade_id = p_trade_id;                -- 匹配交易ID
END //

-- =============================================
-- 存储过程：记录持仓管理操作
-- =============================================
CREATE PROCEDURE record_position_management(
    IN p_trade_id INT,                          -- 交易ID
    IN p_action_type VARCHAR(20),               -- 操作类型
    IN p_action_time DATETIME,                  -- 操作时间
    IN p_old_value DECIMAL(10,5),               -- 修改前的值
    IN p_new_value DECIMAL(10,5),               -- 修改后的值
    IN p_price_at_action DECIMAL(10,5),         -- 操作时的市场价格
    IN p_profit_at_action DECIMAL(10,2),        -- 操作时的浮动盈亏
    IN p_reason VARCHAR(100),                   -- 操作原因
    IN p_details TEXT                           -- 详细说明
)
BEGIN
    INSERT INTO position_management (           -- 插入持仓管理记录
        trade_id, action_type, action_time, old_value, new_value,
        price_at_action, profit_at_action, reason, details
    ) VALUES (
        p_trade_id, p_action_type, p_action_time, p_old_value, p_new_value,
        p_price_at_action, p_profit_at_action, p_reason, p_details
    );
END //

-- =============================================
-- 存储过程：记录信号
-- =============================================
CREATE PROCEDURE record_signal(
    IN p_symbol VARCHAR(20),                    -- 品种
    IN p_timeframe VARCHAR(10),                 -- 时间周期
    IN p_direction VARCHAR(10),                 -- 信号方向
    IN p_signal_type VARCHAR(50),               -- 信号类型
    IN p_signal_strength VARCHAR(20),           -- 信号强度
    IN p_generated_time DATETIME,               -- 信号生成时间
    IN p_price_at_signal DECIMAL(10,5),         -- 信号生成时的价格
    IN p_atr_value DECIMAL(10,5),               -- 信号生成时的ATR值
    IN p_satisfied_patterns TEXT,               -- 满足的模式
    OUT p_signal_id INT                         -- 输出参数：信号ID
)
BEGIN
    INSERT INTO signals (                       -- 插入信号记录
        symbol, timeframe, direction, signal_type, signal_strength,
        generated_time, price_at_signal, atr_value, satisfied_patterns
    ) VALUES (
        p_symbol, p_timeframe, p_direction, p_signal_type, p_signal_strength,
        p_generated_time, p_price_at_signal, p_atr_value, p_satisfied_patterns
    );
    
    SET p_signal_id = LAST_INSERT_ID();         -- 获取插入记录的ID
END //

-- =============================================
-- 存储过程：更新信号执行情况
-- =============================================
CREATE PROCEDURE update_signal_execution(
    IN p_signal_id INT,                         -- 信号ID
    IN p_executed BOOLEAN,                      -- 是否已执行
    IN p_execution_result VARCHAR(50),          -- 执行结果
    IN p_execution_reason TEXT,                 -- 执行或未执行的原因
    IN p_related_trade_id INT                   -- 关联的交易ID
)
BEGIN
    UPDATE signals                              -- 更新信号记录
    SET 
        executed = p_executed,                  -- 设置是否已执行
        execution_result = p_execution_result,  -- 设置执行结果
        execution_reason = p_execution_reason,  -- 设置执行或未执行的原因
        related_trade_id = p_related_trade_id   -- 设置关联的交易ID
    WHERE 
        signal_id = p_signal_id;                -- 匹配信号ID
END //

DELIMITER ;                                     -- 恢复默认分隔符


-- 已经执行以下
-- 在trades表中添加新字段
ALTER TABLE trades
ADD COLUMN details TEXT AFTER atr_value,
ADD COLUMN direction VARCHAR(10) AFTER details;

-- 修改record_trade存储过程
DROP PROCEDURE IF EXISTS record_trade;

DELIMITER //
CREATE PROCEDURE record_trade(
    IN p_account_id INT,                        -- 账户ID
    IN p_mt5_ticket BIGINT,                     -- MT5订单号
    IN p_symbol VARCHAR(20),                    -- 交易品种
    IN p_trade_type VARCHAR(10),                -- 交易类型
    IN p_volume DECIMAL(10,2),                  -- 交易手数
    IN p_open_price DECIMAL(10,5),              -- 开仓价格
    IN p_open_time DATETIME,                    -- 开仓时间
    IN p_sl_price DECIMAL(10,5),                -- 止损价格
    IN p_tp_price DECIMAL(10,5),                -- 止盈价格
    IN p_comment VARCHAR(255),                  -- 交易备注
    IN p_timeframe VARCHAR(10),                 -- 交易周期
    IN p_signal_type VARCHAR(50),               -- 信号类型
    IN p_signal_strength VARCHAR(20),           -- 信号强度
    IN p_atr_value DECIMAL(10,5),               -- ATR值
    IN p_details TEXT,                          -- 信号详情
    IN p_direction VARCHAR(10),                 -- 交易方向
    OUT p_trade_id INT                          -- 输出参数：交易ID
)
BEGIN
    INSERT INTO trades (                        -- 插入交易记录
        account_id, mt5_ticket, symbol, trade_type, volume,
        open_price, open_time, sl_price, tp_price, comment,
        timeframe, signal_type, signal_strength, atr_value, 
        details, direction, status
    ) VALUES (
        p_account_id, p_mt5_ticket, p_symbol, p_trade_type, p_volume,
        p_open_price, p_open_time, p_sl_price, p_tp_price, p_comment,
        p_timeframe, p_signal_type, p_signal_strength, p_atr_value,
        p_details, p_direction, 'OPEN'
    );
    
    SET p_trade_id = LAST_INSERT_ID();          -- 获取插入记录的ID
END //
DELIMITER ;