# 市场解读家仪表盘 v1.0

## 项目概述

市场解读家仪表盘是一款专业的日内交易指标系统，专为MetaTrader 5平台设计。该系统采用模块化架构，整合多种技术分析工具，为交易者提供全面的市场分析和交易信号识别功能。

## 核心功能

### 🎯 趋势状态分析
- 基于Vegas Tunnel均线系统判断市场趋势方向
- 支持上升趋势、下降趋势、震荡区间三种状态识别
- 实时显示趋势强度和方向变化

### 📊 价格行为信号识别
- **L1/L2做多信号**：在上升趋势中识别回调失败的做多机会
- **H1/H2做空信号**：在下降趋势中识别反弹失败的做空机会
- 智能信号计数和重置机制
- 图形化信号标记和历史记录

### ⏰ 交易时段分析
- 自动识别亚洲、伦敦、纽约及重叠时段
- 智能夏令时处理和时区转换
- 时段活跃度评分系统

### 📈 ATR波动率监控
- 实时ATR计算和历史对比
- 波动率百分位排名
- 动态风险管理建议
- 仓位大小计算辅助

### 🎚️ 关键价位线显示
- 日线开盘价、前日高低点
- 周线、月线开盘价（可选）
- 实时距离计算和接近警报

### 🚨 智能警报系统
- 价格行为信号警报
- 趋势变化提醒
- 关键价位接近警报
- 高波动率环境警报
- 交易时段变化通知
- 支持声音警报和推送通知

### 🖥️ 专业仪表盘界面
- 深色主题专业设计
- 模块化面板布局
- 实时数据更新
- 可自定义位置和显示

## 技术架构

### 文件结构
```
Project_MarketInterpreter/
├── MarketInterpreter_Dashboard.mq5     # 主指标文件
└── Include/
    ├── Defines.mqh                     # 全局定义和常量
    ├── Context_Session.mqh             # 交易时段分析模块
    ├── Analysis_KeyLevels.mqh          # 关键价位分析模块
    ├── Analysis_Volatility.mqh         # ATR波动率分析模块
    ├── Analysis_PriceAction.mqh        # 价格行为信号识别模块
    ├── UI_Dashboard.mqh                # 仪表盘UI显示模块
    └── System_Alerts.mqh               # 警报系统模块
```

### 模块化设计
- **命名空间隔离**：每个模块使用独立命名空间，避免命名冲突
- **功能封装**：所有业务逻辑封装在.mqh模块中
- **统一接口**：标准化的初始化、更新、清理接口
- **调试支持**：内置调试模式和日志输出

## 安装和使用

### 安装步骤
1. 将整个`Project_MarketInterpreter`文件夹复制到MT5的`MQL5/Indicators/`目录下
2. 重启MetaTrader 5或刷新导航器
3. 在导航器中找到"MarketInterpreter_Dashboard"指标
4. 拖拽到图表上即可使用

### 参数配置

#### 仪表盘显示设置
- `InpShowDashboard`: 是否显示仪表盘
- `InpDashboardX/Y`: 仪表盘位置坐标
- `InpFontSize`: 字体大小

#### Vegas Tunnel均线设置
- `InpVegasFast`: 快速EMA周期（默认12）
- `InpVegasMedium`: 中速EMA周期（默认21）
- `InpVegasSlow`: 慢速EMA周期（默认55）

#### 关键价位线设置
- 支持显示/隐藏各种价位线
- 可自定义颜色和线宽

#### ATR波动率设置
- `InpATRPeriod`: ATR计算周期
- `InpATRHistoryDays`: 历史数据天数

#### 警报设置
- 各类警报的开关控制
- 声音和推送通知设置
- 警报阈值自定义

### 快捷键操作
- **D键**: 切换仪表盘显示/隐藏
- **C键**: 清除警报历史
- **S键**: 显示统计信息

## 交易逻辑说明

### L1/L2做多信号
**适用场景**: 明确的上升趋势中

**信号条件**:
1. **回调发生**: 当前K线最低点 < 前一根K线最低点
2. **回调失败**: 当前K线收盘看涨（收盘价 > 开盘价 或 收盘价 > 中位价）
3. **K线质量**: 实体占比 ≥ 30%，避免十字星等无效信号

**交易含义**: 小级别回调结束，大级别上升趋势继续

### H1/H2做空信号
**适用场景**: 明确的下降趋势中

**信号条件**:
1. **反弹发生**: 当前K线最高点 > 前一根K线最高点
2. **反弹失败**: 当前K线收盘看跌（收盘价 < 开盘价 或 收盘价 < 中位价）
3. **K线质量**: 实体占比 ≥ 30%

**交易含义**: 小级别反弹结束，大级别下降趋势继续

### 趋势判断标准
- **上升趋势**: 快速EMA > 中速EMA > 慢速EMA，且均线向上倾斜
- **下降趋势**: 快速EMA < 中速EMA < 慢速EMA，且均线向下倾斜
- **震荡区间**: 不满足明确趋势条件

## 风险管理功能

### 动态止损建议
- 基于ATR的风险调整止损距离
- 默认使用2倍ATR作为止损参考

### 仓位大小计算
- 根据账户风险百分比和止损距离计算建议仓位
- 考虑最小交易量和交易量步长

### 波动率环境识别
- 高波动率环境警报（ATR百分位 ≥ 80%）
- 低波动率环境识别（ATR百分位 ≤ 30%）

## 扩展功能

### 导出函数
系统提供多个导出函数，供EA或其他指标调用：
- `GetCurrentTrend()`: 获取当前趋势状态
- `GetCurrentSession()`: 获取当前交易时段
- `GetLastPriceActionSignal()`: 获取最后价格行为信号
- `GetCurrentATR()`: 获取当前ATR值
- `IsHighVolatilityEnvironment()`: 检查高波动率环境
- `GetRiskAdjustedStopDistance()`: 获取风险调整止损距离
- `GetDynamicPositionSize()`: 获取动态仓位建议

### 数据结构
提供`MarketInterpreterData`结构体，包含完整的市场分析数据，方便程序化交易系统集成。

## 注意事项

1. **时区设置**: 请根据您的经纪商服务器时区正确设置GMT偏移量
2. **历史数据**: 首次使用时需要足够的历史数据来计算ATR和趋势
3. **性能优化**: 系统已优化更新频率，避免过度计算
4. **警报管理**: 内置冷却机制，避免重复警报干扰

## 版本信息

- **版本**: v1.0
- **作者**: htazq
- **版权**: Copyright © 2025
- **项目地址**: https://github.com/htazq
- **许可证**: 请遵守相关使用条款

## 技术支持

如有问题或建议，请通过以下方式联系：
- GitHub Issues: https://github.com/htazq/issues
- 邮箱: [请在GitHub查看联系方式]

## 更新日志

### v1.0 (2025-01-30)
- 初始版本发布
- 实现所有核心功能模块
- 完成模块化架构设计
- 添加完整的警报系统
- 实现专业仪表盘界面

---

**免责声明**: 本指标仅供教育和研究目的，不构成投资建议。交易有风险，请谨慎操作。