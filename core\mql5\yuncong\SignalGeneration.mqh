//+------------------------------------------------------------------+
//|                                       SignalGeneration.mqh       |
//|                                                                  |
//|                                                                  |
//+------------------------------------------------------------------+
#ifndef SIGNAL_GENERATION_MQH
#define SIGNAL_GENERATION_MQH

#include "Parameters.mqh"
#include "Visualization.mqh"  // 添加可视化模块，包含DrawSRLevels函数
#include "SupportResistance.mqh" // 包含支撑阻力水平函数
#include "Utilities.mqh" // 包含IsHighVolume函数

// 定义信号信息结构体
struct SignalInfo {
    int trade_signal; // 0=无, 1=做多, -1=做空
    MqlRates breakout_candle; // 触发信号的突破K线
    double atr_at_breakout; // 突破时的ATR
    double breakout_level; // 被突破的支撑/阻力水平价格
    
    // 构造函数初始化
    SignalInfo() : trade_signal(0), atr_at_breakout(0), breakout_level(0) { ZeroMemory(breakout_candle); }
};

// 声明全局状态变量
extern bool waiting_for_confirmation_long;
extern bool waiting_for_confirmation_short;
extern int confirmation_bar_counter;

// 声明需要访问的指标句柄
extern int handle_atr;
extern int handle_volume;

// IsHighVolume函数已在Utilities.mqh中定义，此处移除重复定义


//+------------------------------------------------------------------+
//| 判断是否为强劲的确认K线                                         |
//+------------------------------------------------------------------+
bool IsStrongConfirmationCandle(const MqlRates &rates[], const double &volume_values[], int index, bool is_long)
{
    if(index < 0 || index >= ArraySize(rates))
        return false;
        
    // 获取当前K线
    MqlRates candle = rates[index];
        
    // 计算K线实体占比
    double body_size = MathAbs(candle.close - candle.open);
    double candle_range = candle.high - candle.low;
    double body_ratio = candle_range > 0 ? body_size / candle_range : 0;
    
    // 做多确认K线：阳线，实体占比大，下影线短
    if(is_long)
    {
        bool is_bullish = candle.close > candle.open;
        double lower_shadow = candle.open - candle.low; // 下影线长度
        double lower_shadow_ratio = candle_range > 0 ? lower_shadow / candle_range : 0;
        
        return is_bullish && body_ratio >= 0.6 && lower_shadow_ratio <= 0.3;
    }
    // 做空确认K线：阴线，实体占比大，上影线短
    else
    {
        bool is_bearish = candle.close < candle.open;
        double upper_shadow = candle.high - candle.open; // 上影线长度
        double upper_shadow_ratio = candle_range > 0 ? upper_shadow / candle_range : 0;
        
        return is_bearish && body_ratio >= 0.6 && upper_shadow_ratio <= 0.3;
    }
}

//+------------------------------------------------------------------+
//| 检查入场信号                                                     |
//+------------------------------------------------------------------+
SignalInfo CheckEntrySignals(
   const MqlRates &rates[], const double &atr_values[], const double &volume_values[], int copied_count, // 输入数据
   const SwingPointInfo &latest_high, const SwingPointInfo &latest_low // 最近的高低点
)
{
    // 创建结果结构体
    SignalInfo result;
    
    // 检查输入数据有效性
    if(copied_count < inp_LookbackPeriod)
    {
        Print("输入数据不足, 复制了 ", copied_count, " 根K线");
        return result;
    }
    
    // 低波动率过滤
    if(inp_MinAtrThreshold > 0 && atr_values[1] < inp_MinAtrThreshold)
    {
        // Print("ATR值过低, 当前: ", atr_values[1], ", 阈值: ", inp_MinAtrThreshold);
        return result;
    }
    
    // 检查最近的高低点是否有效
    if(latest_high.index <= 0 && latest_low.index <= 0)
    {
        Print("未能识别到有效的高低点");
        return result;
    }
    
    // 使用最近的高低点作为阻力位和支撑位
    double R = latest_high.index > 0 ? latest_high.price : 0;
    double S = latest_low.index > 0 ? latest_low.price : 0;

    // --- 检查阻力位向上突破 (做多机会) ---
    if ((inp_TradeDirection == DIRECTION_BOTH || inp_TradeDirection == DIRECTION_LONG) && R > 0)
    {
        // 检查突破幅度
        bool is_breakout_up = (rates[1].close > R && (rates[1].close - R) > inp_BreakoutAtrMargin * atr_values[1]);
        if (is_breakout_up && !waiting_for_confirmation_short) // 不能在等待做空时触发做多
        {
            // 检查成交量
            bool is_volume_ok = !inp_UseVolumeConfirm || 
                                IsHighVolume(volume_values, 1, inp_VolumeAvgPeriod, inp_MinVolumeIncreaseFactor);
            if (is_volume_ok)
            {
                waiting_for_confirmation_long = true;
                result.breakout_candle = rates[1];
                result.atr_at_breakout = atr_values[1];
                result.trade_signal = 1; // 做多信号
                result.breakout_level = R; // 记录被突破的阻力位价格
                confirmation_bar_counter = 0;
                waiting_for_confirmation_short = false;
                Print("检测到关键阻力位 ", R, " 向上突破, 等待做多确认...");
            }
        }
    }
    
    // --- 检查支撑位向下突破 (做空机会) ---
    if ((inp_TradeDirection == DIRECTION_BOTH || inp_TradeDirection == DIRECTION_SHORT) && S > 0)
    {
        // 检查突破幅度
        bool is_breakout_down = (rates[1].close < S && (S - rates[1].close) > inp_BreakoutAtrMargin * atr_values[1]);
        if (is_breakout_down && !waiting_for_confirmation_long) // 不能在等待做多时触发做空
        {
            // 检查成交量
            bool is_volume_ok = !inp_UseVolumeConfirm || 
                                IsHighVolume(volume_values, 1, inp_VolumeAvgPeriod, inp_MinVolumeIncreaseFactor);
            if (is_volume_ok)
            {
                waiting_for_confirmation_short = true;
                result.breakout_candle = rates[1];
                result.atr_at_breakout = atr_values[1];
                result.trade_signal = -1; // 做空信号
                result.breakout_level = S; // 记录被突破的支撑位价格
                confirmation_bar_counter = 0;
                waiting_for_confirmation_long = false;
                Print("检测到关键支撑位 ", S, " 向下突破, 等待做空确认...");
            }
        }
    }
    
    
    return result;
}

//+------------------------------------------------------------------+
//| 检查确认信号                                                     |
//+------------------------------------------------------------------+
SignalInfo CheckConfirmationSignals(
   const MqlRates &rates[], const double &atr_values[], const double &volume_values[], 
   SignalInfo &result
)
{
    // 检查做多确认
    if(waiting_for_confirmation_long)
    {
        confirmation_bar_counter++; // 每根新K线检查时计数器加1
        Print("等待做多确认: 第 ", confirmation_bar_counter, " 根K线 (共 ", inp_ConfirmationBars, " 根)");
        
        // 检查是否超时
        if(confirmation_bar_counter > inp_ConfirmationBars)
        {
            Print("做多确认超时 (检查了 ", confirmation_bar_counter -1 , " 根 K 线)");
            waiting_for_confirmation_long = false;  // 必须设置为 false
            confirmation_bar_counter = 0;        // 必须重置计数器
            // 移除此处的 return，让函数继续执行，以便检查做空信号（如果有）
        }
        else
        {
            // 检查确认条件
            double breakout_level = result.breakout_level; // 获取被突破的阻力位
            bool is_candle_ok = !inp_UseCandleConfirm || 
                                (IsStrongConfirmationCandle(rates, volume_values, 1, true) && rates[1].close > breakout_level);
            bool is_volume_ok = !inp_UseVolumeConfirm || 
                                IsHighVolume(volume_values, 1, inp_VolumeAvgPeriod, inp_MinVolumeIncreaseFactor);
                                
            if(is_candle_ok && is_volume_ok)
            {
                // 确认成功，设置做多信号
                result.trade_signal = 1;
                waiting_for_confirmation_long = false;
                confirmation_bar_counter = 0; // 确保重置
            }
        }
    }
    
    // 检查做空确认
    if(waiting_for_confirmation_short)
    {
        confirmation_bar_counter++; // 每根新K线检查时计数器加1
        Print("等待做空确认: 第 ", confirmation_bar_counter, " 根K线 (共 ", inp_ConfirmationBars, " 根)");
        
        // 检查是否超时
        if(confirmation_bar_counter > inp_ConfirmationBars)
        {
            Print("做空确认超时 (检查了 ", confirmation_bar_counter -1 , " 根 K 线)");
            waiting_for_confirmation_short = false; // 必须设置为 false
            confirmation_bar_counter = 0;        // 必须重置计数器
            // 移除此处的 return
        }
        else
        {
            // 检查确认条件
            double breakout_level = result.breakout_level; // 获取被突破的支撑位
            bool is_candle_ok = !inp_UseCandleConfirm || 
                                (IsStrongConfirmationCandle(rates, volume_values, 1, false) && rates[1].close < breakout_level);
            bool is_volume_ok = !inp_UseVolumeConfirm || 
                                IsHighVolume(volume_values, 1, inp_VolumeAvgPeriod, inp_MinVolumeIncreaseFactor);
                                
            if(is_candle_ok && is_volume_ok)
            {
                // 确认成功，设置做空信号
                result.trade_signal = -1;
                waiting_for_confirmation_short = false;
                confirmation_bar_counter = 0; // 确保重置
            }
        }
    }
    
    // 不再需要清除和绘制支撑阻力水平线，这些由DrawKeySRLines函数处理
    
    
    return result;
}

#endif // SIGNAL_GENERATION_MQH