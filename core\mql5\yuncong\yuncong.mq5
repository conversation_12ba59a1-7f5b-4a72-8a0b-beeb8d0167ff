//+------------------------------------------------------------------+
//|                                       TrendlineBreakoutEA.mq5    |
//|                                                                  |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright "Copyright 2023"
#property link      ""
#property version   "1.00"

// 包含自定义库文件
#include "Includes.mqh"
#include <Arrays/ArrayString.mqh> // 需要包含字符串数组库

// 全局变量定义
int handle_atr = INVALID_HANDLE;
int handle_volume = INVALID_HANDLE;

bool waiting_for_confirmation_long = false;
bool waiting_for_confirmation_short = false;
int confirmation_bar_counter = 0;

// 存储已绘制的趋势线对象名称 (已不再使用，但保留变量以避免编译错误)
CArrayString G_UpTrendLineNames;   // 存储已绘制的上升趋势线对象名称
CArrayString G_DownTrendLineNames; // 存储已绘制的下降趋势线对象名称

// 新增全局变量存储关键S/R线的对象名称
string G_KeyResistanceLineName = "KeyResistanceLine";
string G_KeySupportLineName = "KeySupportLine";

CTrade trade;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
   // 初始化指标
   if(!InitializeIndicators())
   {
      Print("初始化指标失败");
      return INIT_FAILED;
   }
   
   // 设置交易参数
   trade.SetExpertMagicNumber(inp_MagicNumber);
   trade.SetDeviationInPoints(inp_Slippage);
   
   Print("TrendlineBreakoutEA初始化成功");
   Print("回看周期: ", inp_LookbackPeriod, ", 波动点定义K线数: ", inp_SwingDefinitionBars);
   Print("ATR乘数(波动点): ", inp_SwingAmplitudeMultiplier, ", 合并S/R水平ATR因子: ", inp_MergeAtrFactor);
   Print("突破S/R水平所需的最小ATR幅度: ", inp_BreakoutAtrMargin);
   
   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   // 释放指标句柄
   DeinitializeIndicators();
   
   // 清除所有可视化对象
   ClearVisualizationObjects();
   
   // 清除关键支撑阻力线
   ClearKeySRLines();
   
   Print("TrendlineBreakoutEA已卸载");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
   // 1. 管理现有持仓
   ManageOpenPositions(); 
   
   // 2. 检查是否可以开新仓 (调用辅助函数)
   if(CountPositions() >= 1) // CountPositions 在 Utilities.mqh 中
      return;
   
   // 获取价格数据
   MqlRates rates[];
   ArraySetAsSeries(rates, true);
   int copied = CopyRates(_Symbol, PERIOD_CURRENT, 0, inp_LookbackPeriod + 10, rates);
   if(copied < inp_LookbackPeriod)
   {
      Print("获取价格数据失败, 复制了 ", copied, " 根K线");
      return;
   }
   
   // 获取ATR数据
   double atr_values[];
   ArraySetAsSeries(atr_values, true);
   if(CopyBuffer(handle_atr, 0, 0, inp_LookbackPeriod + 10, atr_values) < inp_LookbackPeriod)
   {
      Print("获取ATR数据失败");
      return;
   }
   
   // 获取成交量数据
   double volume_values[];
   ArraySetAsSeries(volume_values, true);
   if(CopyBuffer(handle_volume, 0, 0, inp_LookbackPeriod + 10, volume_values) < inp_LookbackPeriod)
   {
      Print("获取成交量数据失败");
      return;
   }
   
   // 获取最近的关键高低点
   SwingPointInfo high_points[];
   SwingPointInfo low_points[];
   DetectQualitySwingPoints(
      rates, 
      atr_values, 
      inp_LookbackPeriod, 
      inp_SwingDefinitionBars, 
      inp_SwingAmplitudeMultiplier, 
      inp_MinSwingAmplitude, 
      inp_MinBarsBetweenSwings, 
      high_points, 
      low_points
   );
   
   // 获取最近的关键高低点
   SwingPointInfo latest_high; ZeroMemory(latest_high);
   SwingPointInfo latest_low; ZeroMemory(latest_low);
   if (ArraySize(high_points) > 0) latest_high = high_points[0];
   if (ArraySize(low_points) > 0) latest_low = low_points[0];
   
   // 调用新的绘图函数
   DrawKeySRLines(latest_high, latest_low);
   
   // 3. 调用修改后的信号生成函数，接收返回的SignalInfo结构体
   SignalInfo signal_info = CheckEntrySignals(rates, atr_values, volume_values, copied, latest_high, latest_low);
   
   // 4. 不再需要更新趋势线可视化，关键支撑阻力线已单独绘制
   // 注释掉原来的UpdateVisualization调用，因为SignalInfo结构体已不再包含趋势线相关字段
   // UpdateVisualization(...);
   
   // 如果需要确认信号，调用修改后的CheckConfirmationSignals函数
   if (waiting_for_confirmation_long || waiting_for_confirmation_short) {
       signal_info = CheckConfirmationSignals(rates, atr_values, volume_values, signal_info);
   }
   
   // 5. 执行交易 (如果信号允许)
   if (signal_info.trade_signal != 0) // 如果有交易信号
   {
      bool is_long = (signal_info.trade_signal == 1);
      double entry_price, stop_loss, take_profit;
      
      // 调用 CalculateOrderParams 获取下单参数，使用signal_info中的数据，包括突破水平
      if(CalculateOrderParams(signal_info.breakout_candle, signal_info.atr_at_breakout, is_long, entry_price, stop_loss, take_profit, signal_info.breakout_level))
      {
         double lot_size = CalculateLotSize(entry_price, stop_loss);
         string comment = is_long ? "TrendlineBreakout Long" : "TrendlineBreakout Short";
         PlaceOrder(is_long, lot_size, stop_loss, take_profit, comment);
      } else {
         Print("计算订单参数失败 (主循环)");
      }
   }
}
//+------------------------------------------------------------------+