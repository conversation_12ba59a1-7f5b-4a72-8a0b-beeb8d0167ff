//+------------------------------------------------------------------+
//|                                          RiskManagement.mqh      |
//|                                                                  |
//|                                                                  |
//+------------------------------------------------------------------+
#ifndef RISK_MANAGEMENT_MQH
#define RISK_MANAGEMENT_MQH

#include "Parameters.mqh"

//+------------------------------------------------------------------+
//| 计算交易手数                                                     |
//+------------------------------------------------------------------+
double CalculateLotSize(double entry_price, double stop_loss)
{
   // 如果使用固定手数
   if(inp_RiskPercentPerTrade <= 0)
      return inp_FixedLotSize;
      
   // 计算风险金额
   double account_balance = AccountInfoDouble(ACCOUNT_BALANCE);
   double risk_amount = account_balance * inp_RiskPercentPerTrade / 100.0;
   
   // 计算点值
   double tick_size = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_SIZE);
   double tick_value = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_VALUE);
   double point_value = tick_value / tick_size;
   
   // 计算止损点数
   double stop_distance = MathAbs(entry_price - stop_loss);
   // 防止止损距离为零
   if(stop_distance <= 0) {
      Print("警告: 止损距离为零或负值，使用最小手数");
      return SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
   }
   
   double point = SymbolInfoDouble(_Symbol, SYMBOL_POINT);
   // 防止点值为零
   if(point <= 0) {
      Print("警告: 点值为零或负值，使用最小手数");
      return SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
   }
   
   double points = stop_distance / point;
   
   // 计算手数
   double lot_step = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);
   double max_lot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX);
   double min_lot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
   
   // 防止除零错误
   if(points <= 0 || point_value <= 0) {
      Print("警告: 点数或点值为零或负值，使用最小手数");
      return SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
   }
   
   double calculated_lot = risk_amount / (points * point_value);
   
   // 规范化手数
   calculated_lot = MathFloor(calculated_lot / lot_step) * lot_step;
   
   // 确保在最小和最大手数范围内
   calculated_lot = MathMax(min_lot, MathMin(max_lot, calculated_lot));
   
   return calculated_lot;
}

#endif // RISK_MANAGEMENT_MQH