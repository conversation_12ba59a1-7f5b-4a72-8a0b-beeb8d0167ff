//+------------------------------------------------------------------+
//|                                              EMA_5_Lines_Pro.mq5 |
//|                                         Copyright © 2025,  htazq |
//|                                         https://github.com/htazq |
//+------------------------------------------------------------------+
#property copyright "Copyright © 2025, htazq"
#property link      "https://github.com/htazq"
#property version   "1.00"
#property description "5条EMA线的多空排列分析指标"
#property description "把握日内趋势"

#property indicator_chart_window
#property indicator_buffers 5
#property indicator_plots   5


//--- 输入参数
input group "EMA周期"
input int InpPeriod1 = 20;    // EMA 1 (短期)
input int InpPeriod2 = 144;   // EMA 2 (中期1)
input int InpPeriod3 = 169;   // EMA 3 (中期2)
input int InpPeriod4 = 288;   // EMA 4 (长期1)
input int InpPeriod5 = 338;   // EMA 5 (长期2)

input group "基础设置"
input ENUM_APPLIED_PRICE InpAppliedPrice = PRICE_CLOSE; // 数据源

input group "显示设置"
input bool InpShow1 = true;   // 显示 EMA20
input bool InpShow2 = true;   // 显示 EMA144
input bool InpShow3 = true;   // 显示 EMA169
input bool InpShow4 = true;   // 显示 EMA288
input bool InpShow5 = true;   // 显示 EMA338
input int InpLineWidth = 1;   // 线宽
input bool InpShowCountdown = true; // 显示倒计时
input color InpCountdownColor = clrWhite; // 倒计时颜色

input group "关键价位线设置"
input bool InpShowDailyOpen = true;  // 显示日线开盘价
input color InpDailyOpenColor = clrYellow;  // 日线开盘价颜色
input bool InpShowPrevHigh = true;   // 显示前一日最高点
input color InpPrevHighColor = clrRed;  // 前一日最高点颜色
input bool InpShowPrevLow = true;    // 显示前一日最低点
input color InpPrevLowColor = clrLime;  // 前一日最低点颜色
input int InpKeyLevelLineWidth = 1;  // 关键价位线宽度

//--- 指标缓冲区
double EmaBuffer1[];
double EmaBuffer2[];
double EmaBuffer3[];
double EmaBuffer4[];
double EmaBuffer5[];

//--- EMA句柄
int ema_handle1;
int ema_handle2;
int ema_handle3;
int ema_handle4;
int ema_handle5;

//--- 倒计时对象名称和变量
string countdown_obj_name = "EMA_Countdown";
datetime last_bar_time = 0;
int last_seconds_left = -1;

//--- 关键价位线相关变量
string daily_open_line_name = "Daily_Open_Line";
string prev_high_line_name = "Prev_High_Line";
string prev_low_line_name = "Prev_Low_Line";
datetime last_day_check = 0;

//+------------------------------------------------------------------+
//| 自定义指标初始化函数                                                |
//+------------------------------------------------------------------+
int OnInit()
{
    //--- 设置指标缓冲区
    SetIndexBuffer(0, EmaBuffer1, INDICATOR_DATA);
    SetIndexBuffer(1, EmaBuffer2, INDICATOR_DATA);
    SetIndexBuffer(2, EmaBuffer3, INDICATOR_DATA);
    SetIndexBuffer(3, EmaBuffer4, INDICATOR_DATA);
    SetIndexBuffer(4, EmaBuffer5, INDICATOR_DATA);
    
    //--- 设置绘图属性
    PlotIndexSetInteger(0, PLOT_DRAW_TYPE, InpShow1 ? DRAW_LINE : DRAW_NONE);
    PlotIndexSetInteger(1, PLOT_DRAW_TYPE, InpShow2 ? DRAW_LINE : DRAW_NONE);
    PlotIndexSetInteger(2, PLOT_DRAW_TYPE, InpShow3 ? DRAW_LINE : DRAW_NONE);
    PlotIndexSetInteger(3, PLOT_DRAW_TYPE, InpShow4 ? DRAW_LINE : DRAW_NONE);
    PlotIndexSetInteger(4, PLOT_DRAW_TYPE, InpShow5 ? DRAW_LINE : DRAW_NONE);
    
    //--- 设置线宽
    PlotIndexSetInteger(0, PLOT_LINE_WIDTH, InpLineWidth);
    PlotIndexSetInteger(1, PLOT_LINE_WIDTH, InpLineWidth);
    PlotIndexSetInteger(2, PLOT_LINE_WIDTH, InpLineWidth);
    PlotIndexSetInteger(3, PLOT_LINE_WIDTH, InpLineWidth);
    PlotIndexSetInteger(4, PLOT_LINE_WIDTH, InpLineWidth);
    
    //--- 动态设置颜色
    PlotIndexSetInteger(0, PLOT_LINE_COLOR, clrYellow);
    PlotIndexSetInteger(1, PLOT_LINE_COLOR, clrLimeGreen);
    PlotIndexSetInteger(2, PLOT_LINE_COLOR, clrGreen);
    PlotIndexSetInteger(3, PLOT_LINE_COLOR, clrDodgerBlue);
    PlotIndexSetInteger(4, PLOT_LINE_COLOR, clrBlue);
    
    //--- 动态设置标签
    PlotIndexSetString(0, PLOT_LABEL, "EMA " + (string)InpPeriod1);
    PlotIndexSetString(1, PLOT_LABEL, "EMA " + (string)InpPeriod2);
    PlotIndexSetString(2, PLOT_LABEL, "EMA " + (string)InpPeriod3);
    PlotIndexSetString(3, PLOT_LABEL, "EMA " + (string)InpPeriod4);
    PlotIndexSetString(4, PLOT_LABEL, "EMA " + (string)InpPeriod5);
    
    //--- 创建EMA句柄
    ema_handle1 = iMA(_Symbol, _Period, InpPeriod1, 0, MODE_EMA, InpAppliedPrice);
    ema_handle2 = iMA(_Symbol, _Period, InpPeriod2, 0, MODE_EMA, InpAppliedPrice);
    ema_handle3 = iMA(_Symbol, _Period, InpPeriod3, 0, MODE_EMA, InpAppliedPrice);
    ema_handle4 = iMA(_Symbol, _Period, InpPeriod4, 0, MODE_EMA, InpAppliedPrice);
    ema_handle5 = iMA(_Symbol, _Period, InpPeriod5, 0, MODE_EMA, InpAppliedPrice);
    
    //--- 检查句柄创建是否成功
    if(ema_handle1 == INVALID_HANDLE || ema_handle2 == INVALID_HANDLE || 
       ema_handle3 == INVALID_HANDLE || ema_handle4 == INVALID_HANDLE || 
       ema_handle5 == INVALID_HANDLE)
    {
        Print("创建EMA句柄失败");
        return(INIT_FAILED);
    }
    
    //--- 设置指标名称
    IndicatorSetString(INDICATOR_SHORTNAME, "EMA 5线指标");
    
    //--- 设置精度
    IndicatorSetInteger(INDICATOR_DIGITS, _Digits);
    
    //--- 创建倒计时文本对象
    if(InpShowCountdown)
    {
        ObjectCreate(0, countdown_obj_name, OBJ_TEXT, 0, 0, 0);
        ObjectSetInteger(0, countdown_obj_name, OBJPROP_COLOR, InpCountdownColor);
        ObjectSetInteger(0, countdown_obj_name, OBJPROP_FONTSIZE, 14);
        ObjectSetInteger(0, countdown_obj_name, OBJPROP_BACK, false);
        ObjectSetInteger(0, countdown_obj_name, OBJPROP_SELECTABLE, false);
        ObjectSetInteger(0, countdown_obj_name, OBJPROP_ANCHOR, ANCHOR_LEFT_UPPER);
        
        //--- 启动定时器，每秒更新一次倒计时
        EventSetTimer(1);
    }
    
    return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| 自定义指标去初始化函数                                              |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    //--- 停止定时器
    EventKillTimer();
    
    //--- 释放EMA句柄
    ReleaseEMAHandles();
    
    //--- 删除倒计时对象
    ObjectDelete(0, countdown_obj_name);
    
    //--- 删除关键价位线对象
    ObjectDelete(0, daily_open_line_name);
    ObjectDelete(0, prev_high_line_name);
    ObjectDelete(0, prev_low_line_name);
}

//+------------------------------------------------------------------+
//| 定时器事件处理函数                                                  |
//+------------------------------------------------------------------+
void OnTimer()
{
    if(InpShowCountdown)
    {
        UpdateCountdown();
    }
}

//+------------------------------------------------------------------+
//| 自定义指标计算函数                                                  |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[])
{
    //--- 检查数据是否足够
    if(rates_total < MathMax(InpPeriod5, 1))
        return(0);
    
    //--- 确定计算起始位置
    int start = prev_calculated;
    if(start == 0)
        start = MathMax(InpPeriod5, 1);
    
    //--- 复制EMA数据
    if(!CopyEMAData(rates_total))
        return(0);
    
    //--- 处理关键价位线（只在新的一天第一根K线时执行）
    if(InpShowDailyOpen || InpShowPrevHigh || InpShowPrevLow)
    {
        datetime current_day_start = iTime(_Symbol, PERIOD_D1, 0);
        if(current_day_start != last_day_check)
        {
            UpdateKeyLevelLines();
            last_day_check = current_day_start;
        }
    }
    
    return(rates_total);
}

//+------------------------------------------------------------------+
//| 更新倒计时显示                                                     |
//+------------------------------------------------------------------+
void UpdateCountdown()
{
    // 获取当前K线的开始时间
    datetime bar_open_time = iTime(_Symbol, _Period, 0);
    
    // 检查是否是新的K线
    if(bar_open_time != last_bar_time)
    {
        last_bar_time = bar_open_time;
        last_seconds_left = -1; // 重置计数器
    }
    
    // 使用MQL5内置函数计算周期秒数
    int period_seconds = PeriodSeconds(_Period);
    
    // 获取当前服务器时间
    datetime current_time = TimeCurrent();
    
    // 计算下一根K线的开始时间
    datetime next_bar_time = bar_open_time + period_seconds;
    
    // 计算剩余时间（秒），并提前1秒显示
    int seconds_left = (int)(next_bar_time - current_time) - 1;
    
    // 确保剩余时间不为负数且不超过周期
    if(seconds_left < 0) 
        seconds_left = 0;
    if(seconds_left >= period_seconds)
        seconds_left = period_seconds - 1;
    
    // 防止跳秒：如果时间差异太大，使用递减方式
    if(last_seconds_left > 0 && seconds_left < last_seconds_left - 2)
    {
        seconds_left = last_seconds_left - 1;
    }
    
    // 更新记录的秒数
    last_seconds_left = seconds_left;
    
    // 计算剩余的小时、分钟和秒
    int hours = seconds_left / 3600;
    int minutes = (seconds_left % 3600) / 60;
    int seconds = seconds_left % 60;
    
    // 格式化倒计时文本
    string countdown_text;
    if(hours > 0)
        countdown_text = StringFormat("   倒计时: %d时%02d分%02d秒", hours, minutes, seconds);
    else if(minutes > 0)
        countdown_text = StringFormat("   倒计时: %02d分%02d秒", minutes, seconds);
    else
        countdown_text = StringFormat("   倒计时: %02d秒", seconds);
    
    // 使用静态变量记住上次的文本，只在变化时更新
    static string prev_countdown_text = "";
    if(countdown_text != prev_countdown_text)
    {
        // 固定显示位置：在当前K线右边，使用当前收盘价作为垂直位置
        double current_close = iClose(_Symbol, _Period, 0);
        
        // 计算显示时间：当前K线时间 + 一个周期的时间（显示在右边）
        datetime display_time = bar_open_time + period_seconds;
        
        // 更新倒计时对象的位置和文本
        ObjectSetInteger(0, countdown_obj_name, OBJPROP_TIME, display_time);
        ObjectSetDouble(0, countdown_obj_name, OBJPROP_PRICE, current_close);
        ObjectSetString(0, countdown_obj_name, OBJPROP_TEXT, countdown_text);
        
        prev_countdown_text = countdown_text;
        
        // 只在需要时重绘
        ChartRedraw();
    }
}

//+------------------------------------------------------------------+
//| 创建或更新水平线辅助函数                                              |
//+------------------------------------------------------------------+
void CreateOrUpdateHLine(string name, double price, color clr, int width, ENUM_LINE_STYLE style)
{
    // 检查对象是否存在
    if(ObjectFind(0, name) >= 0)
    {
        // 对象存在，检查价格是否需要更新
        if(MathAbs(ObjectGetDouble(0, name, OBJPROP_PRICE, 0) - price) > _Point * 0.1)
        {
            // 价格变了，移动它
            ObjectSetDouble(0, name, OBJPROP_PRICE, 0, price);
        }
        // 如果价格没变，就什么也不做
    }
    else // 对象不存在，则创建
    {
        if(ObjectCreate(0, name, OBJ_HLINE, 0, 0, price))
        {
            ObjectSetInteger(0, name, OBJPROP_COLOR, clr);
            ObjectSetInteger(0, name, OBJPROP_WIDTH, width);
            ObjectSetInteger(0, name, OBJPROP_STYLE, style);
            ObjectSetInteger(0, name, OBJPROP_BACK, false);
            ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
        }
    }
}

//+------------------------------------------------------------------+
//| 初始化EMA句柄辅助函数                                                |
//+------------------------------------------------------------------+
bool InitializeEMAHandles()
{
    //--- 创建EMA句柄
    ema_handle1 = iMA(_Symbol, _Period, InpPeriod1, 0, MODE_EMA, InpAppliedPrice);
    ema_handle2 = iMA(_Symbol, _Period, InpPeriod2, 0, MODE_EMA, InpAppliedPrice);
    ema_handle3 = iMA(_Symbol, _Period, InpPeriod3, 0, MODE_EMA, InpAppliedPrice);
    ema_handle4 = iMA(_Symbol, _Period, InpPeriod4, 0, MODE_EMA, InpAppliedPrice);
    ema_handle5 = iMA(_Symbol, _Period, InpPeriod5, 0, MODE_EMA, InpAppliedPrice);
    
    //--- 检查句柄创建是否成功
    if(ema_handle1 == INVALID_HANDLE || ema_handle2 == INVALID_HANDLE || 
       ema_handle3 == INVALID_HANDLE || ema_handle4 == INVALID_HANDLE || 
       ema_handle5 == INVALID_HANDLE)
    {
        Print("创建EMA句柄失败");
        return false;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| 释放EMA句柄辅助函数                                                  |
//+------------------------------------------------------------------+
void ReleaseEMAHandles()
{
    if(ema_handle1 != INVALID_HANDLE) IndicatorRelease(ema_handle1);
    if(ema_handle2 != INVALID_HANDLE) IndicatorRelease(ema_handle2);
    if(ema_handle3 != INVALID_HANDLE) IndicatorRelease(ema_handle3);
    if(ema_handle4 != INVALID_HANDLE) IndicatorRelease(ema_handle4);
    if(ema_handle5 != INVALID_HANDLE) IndicatorRelease(ema_handle5);
}

//+------------------------------------------------------------------+
//| 复制EMA数据辅助函数                                                  |
//+------------------------------------------------------------------+
bool CopyEMAData(int rates_total)
{
    //--- 获取EMA数据
    int copied1 = CopyBuffer(ema_handle1, 0, 0, rates_total, EmaBuffer1);
    int copied2 = CopyBuffer(ema_handle2, 0, 0, rates_total, EmaBuffer2);
    int copied3 = CopyBuffer(ema_handle3, 0, 0, rates_total, EmaBuffer3);
    int copied4 = CopyBuffer(ema_handle4, 0, 0, rates_total, EmaBuffer4);
    int copied5 = CopyBuffer(ema_handle5, 0, 0, rates_total, EmaBuffer5);
    
    //--- 检查数据复制是否成功
    return (copied1 > 0 && copied2 > 0 && copied3 > 0 && copied4 > 0 && copied5 > 0);
}

//+------------------------------------------------------------------+
//| 更新关键价位线                                                      |
//+------------------------------------------------------------------+
void UpdateKeyLevelLines()
{
    // 更新今日开盘价线
    if(InpShowDailyOpen)
    {
        double today_open = iOpen(_Symbol, PERIOD_D1, 0);
        if(today_open > 0) 
        {
            CreateOrUpdateHLine(daily_open_line_name, today_open, InpDailyOpenColor, InpKeyLevelLineWidth, STYLE_SOLID);
        }
    }

    // 更新前一日最高价线
    if(InpShowPrevHigh)
    {
        double yesterday_high = iHigh(_Symbol, PERIOD_D1, 1);
        if(yesterday_high > 0) 
        {
            CreateOrUpdateHLine(prev_high_line_name, yesterday_high, InpPrevHighColor, InpKeyLevelLineWidth, STYLE_SOLID);
        }
    }

    // 更新前一日最低价线
    if(InpShowPrevLow)
    {
        double yesterday_low = iLow(_Symbol, PERIOD_D1, 1);
        if(yesterday_low > 0) 
        {
            CreateOrUpdateHLine(prev_low_line_name, yesterday_low, InpPrevLowColor, InpKeyLevelLineWidth, STYLE_SOLID);
        }
    }
}
