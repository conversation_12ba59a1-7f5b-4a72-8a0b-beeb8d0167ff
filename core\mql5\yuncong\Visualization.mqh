//+------------------------------------------------------------------+
//|                                        Visualization.mqh         |
//|                                                                  |
//|                                                                  |
//+------------------------------------------------------------------+
#ifndef VISUALIZATION_MQH
#define VISUALIZATION_MQH

#include "Parameters.mqh"
#include <Arrays/ArrayString.mqh>
// #include "RobustRegression.mqh"  // 已不再使用
#include "SupportResistance.mqh"  // 包含支撑阻力水平函数
#include "QualitySwingPointsTrendline.mqh" // 包含SwingPointInfo结构体

// 声明全局对象名称数组
extern CArrayString G_UpTrendLineNames;
extern CArrayString G_DownTrendLineNames;

// 新增全局变量存储关键S/R线的对象名称 (在主 .mq5 文件定义)
extern string G_KeyResistanceLineName; 
extern string G_KeySupportLineName;

// 注意：DrawTrendline函数已在Utilities.mqh中定义，此处移除以避免重复定义
// 如需使用DrawTrendline函数，请引用Utilities.mqh中的定义

// 注意：ManageTrendlineObjects函数已在Utilities.mqh中定义，此处移除以避免重复定义
// 如需使用ManageTrendlineObjects函数，请引用Utilities.mqh中的定义

//+------------------------------------------------------------------+
//| 清除所有可视化对象                                               |
//+------------------------------------------------------------------+
void ClearVisualizationObjects()
{
   // 删除所有绘制的上升趋势线
   for(int i = G_UpTrendLineNames.Total() - 1; i >= 0; i--) {
      ObjectDelete(0, G_UpTrendLineNames.At(i));
   }
   G_UpTrendLineNames.Clear(); // 清空数组

   // 删除所有绘制的下降趋势线
   for(int i = G_DownTrendLineNames.Total() - 1; i >= 0; i--) {
      ObjectDelete(0, G_DownTrendLineNames.At(i));
   }
   G_DownTrendLineNames.Clear(); // 清空数组
}

// 注释掉不再需要的函数
/*
//+------------------------------------------------------------------+
//| 更新图表可视化                                                   |
//+------------------------------------------------------------------+
void UpdateVisualization(
   const MqlRates &rates[],          // 当前价格数据 (倒序)
   const double &atr_values[],       // 当前 ATR 数据 (倒序)
   int copied_count,                 // rates 数组中的 K 线数
   const int max_lines = 10)         // 每种趋势线最大保留数量
{
   // 检查数据有效性
   if(copied_count <= 0) return;
   
   // 注意：此函数已被简化，移除了回归相关的代码
   // 支撑阻力水平的绘制已在其他地方处理
   
   // 管理趋势线对象（如果有）
   if(G_UpTrendLineNames.Total() > 0) {
      ManageTrendlineObjects(G_UpTrendLineNames, inp_MaxTrendlinesToKeep);
   }
   
   if(G_DownTrendLineNames.Total() > 0) {
      ManageTrendlineObjects(G_DownTrendLineNames, inp_MaxTrendlinesToKeep);
   }
   
   // 未来可以添加绘制其他对象（如 S/R, 信号箭头）的逻辑
}
*/

//+------------------------------------------------------------------+
//| 新增：绘制关键支撑阻力线函数                                      |
//+------------------------------------------------------------------+
void DrawKeySRLines(
    const SwingPointInfo &latest_high, // 输入: 最近的高质量 Swing High
    const SwingPointInfo &latest_low   // 输入: 最近的高质量 Swing Low
)
{
    // --- 绘制关键阻力线 ---
    if(latest_high.index > 0) // 确保 high point 有效
    {
         // 先删除旧线 (如果存在)
         ObjectDelete(0, G_KeyResistanceLineName);
         // 绘制新线
         if(ObjectCreate(0, G_KeyResistanceLineName, OBJ_HLINE, 0, 0, latest_high.price))
         {
             ObjectSetInteger(0, G_KeyResistanceLineName, OBJPROP_COLOR, clrRed);
             ObjectSetInteger(0, G_KeyResistanceLineName, OBJPROP_STYLE, STYLE_SOLID); // 用实线表示关键位
             ObjectSetInteger(0, G_KeyResistanceLineName, OBJPROP_WIDTH, 2); // 线宽设为 2
             ObjectSetInteger(0, G_KeyResistanceLineName, OBJPROP_BACK, true);
             ObjectSetInteger(0, G_KeyResistanceLineName, OBJPROP_SELECTABLE, false);
         } else {
              Print("创建关键阻力线失败: ", GetLastError());
         }
    }

    // --- 绘制关键支撑线 ---
     if(latest_low.index > 0) // 确保 low point 有效
     {
         ObjectDelete(0, G_KeySupportLineName);
         if(ObjectCreate(0, G_KeySupportLineName, OBJ_HLINE, 0, 0, latest_low.price))
         {
             ObjectSetInteger(0, G_KeySupportLineName, OBJPROP_COLOR, clrGreen);
             ObjectSetInteger(0, G_KeySupportLineName, OBJPROP_STYLE, STYLE_SOLID);
             ObjectSetInteger(0, G_KeySupportLineName, OBJPROP_WIDTH, 2);
             ObjectSetInteger(0, G_KeySupportLineName, OBJPROP_BACK, true);
             ObjectSetInteger(0, G_KeySupportLineName, OBJPROP_SELECTABLE, false);
         } else {
              Print("创建关键支撑线失败: ", GetLastError());
         }
     }
}

//+------------------------------------------------------------------+
//| 新增：清除关键支撑阻力线函数                                      |
//+------------------------------------------------------------------+
void ClearKeySRLines()
{
    ObjectDelete(0, G_KeyResistanceLineName);
    ObjectDelete(0, G_KeySupportLineName);
}

//+------------------------------------------------------------------+
//| 绘制价格标签                                                     |
//+------------------------------------------------------------------+
bool DrawPriceLabel(const long chart_ID, const string object_name,
                   const datetime time, const double price,
                   const string text, const color text_color = clrWhite,
                   const int font_size = 8)
{
   // 创建文本标签对象
   if(!ObjectCreate(chart_ID, object_name, OBJ_TEXT, 0, time, price))
   {
      Print("创建价格标签对象失败: ", GetLastError());
      return false;
   }
   
   // 设置标签属性
   ObjectSetString(chart_ID, object_name, OBJPROP_TEXT, text);
   ObjectSetInteger(chart_ID, object_name, OBJPROP_COLOR, text_color);
   ObjectSetInteger(chart_ID, object_name, OBJPROP_FONTSIZE, font_size);
   ObjectSetInteger(chart_ID, object_name, OBJPROP_BACK, false);
   ObjectSetInteger(chart_ID, object_name, OBJPROP_SELECTABLE, false);
   ObjectSetInteger(chart_ID, object_name, OBJPROP_SELECTED, false);
   ObjectSetInteger(chart_ID, object_name, OBJPROP_HIDDEN, true);
   
   return true;
}

#endif // VISUALIZATION_MQH