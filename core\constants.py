from enum import Enum
from typing import Dict, List

class SignalType(Enum):
    PERFECT_PATTERN = "完美形态信号"
    TREND_BREAKOUT = "趋势突破信号"
    PATTERN_CONFIRMATION = "形态确认信号"
    PULLBACK_ENTRY = "回调买点信号"
    DUCK_HEAD = "老鸭头信号"
    CUP_HANDLE = "茶杯带柄信号"

class SignalStrength(Enum):
    HIGH = "高强度"
    MEDIUM = "中等强度"
    LOW = "低强度"

# 通知配置常量
class NotificationConfig:
    # WxPusher配置
    WXPUSHER_TOKEN = "AT_We4vq2NYeqHbDSl8Ru88wyQSDH6sRPhP"
    WXPUSHER_UIDS = [
        # 'UID_RorFljOBGQzcxnRe36vtoWek4F13',
        # 'UID_L8TKx7YEg4XkAbQsyDAfPjIJRYN2',
        # 'UID_kXDRZ6hcvoGJvF0zktkiSGbiRcFI'
    ]
    # 添加 topics 配置
    WXPUSHER_TOPICS = [
        # 在这里添加您的 topic IDs
        '35197',  # 示例 topic ID，请替换为您实际的 topic ID
    ]
    
    # 消息模板
    TEMPLATES = {
        'signal': (
            "=== 交易信号通知 ===\n"
            "时间: {time}\n"
            "交易对: {symbol}\n"
            "时间周期: {timeframe}\n"
            "信号类型: {signal_type}\n"
            "信号强度: {signal_strength}\n"
            "满足条件:\n{conditions}"
            "{market_cap}\n"
            "{extra_info}"
        ),
        'error': (
            "=== 错误通知 ===\n"
            "时间: {time}\n"
            "错误信息: {error_message}"
        ),
        'system': (
            "=== 系统通知 ===\n"
            "时间: {time}\n"
            "{message}"
        )
    }
    
    # 时间格式
    DATETIME_FORMAT = "%Y-%m-%d %H:%M:%S"
    
    # 消息类型
    class MessageType(Enum):
        SIGNAL = "交易信号"
        ERROR = "错误信息"
        SYSTEM = "系统消息" 