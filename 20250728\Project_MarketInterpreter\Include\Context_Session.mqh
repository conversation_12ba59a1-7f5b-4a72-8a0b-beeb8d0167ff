//+------------------------------------------------------------------+
//|                                               Context_Session.mqh |
//|                                         Copyright © 2025, htazq |
//|                                         https://github.com/htazq |
//+------------------------------------------------------------------+
#property copyright "Copyright © 2025, htazq"
#property link      "https://github.com/htazq"

#include "Defines.mqh"

//+------------------------------------------------------------------+
//| 交易时段分析命名空间                                               |
//+------------------------------------------------------------------+
namespace Session
{
    //+------------------------------------------------------------------+
    //| 私有变量                                                          |
    //+------------------------------------------------------------------+
    static ENUM_TRADING_SESSION current_session = SESSION_NONE;
    static datetime session_start_time = 0;
    static datetime session_end_time = 0;
    static bool is_dst_active = false;  // 夏令时标志
    
    //+------------------------------------------------------------------+
    //| 判断是否为夏令时                                                   |
    //+------------------------------------------------------------------+
    bool IsDaylightSavingTime(datetime check_time)
    {
        MqlDateTime dt;
        TimeToStruct(check_time, dt);
        
        // 美国夏令时：3月第二个周日到11月第一个周日
        if(dt.mon < 3 || dt.mon > 11) return false;
        if(dt.mon > 3 && dt.mon < 11) return true;
        
        // 3月份：第二个周日开始
        if(dt.mon == 3)
        {
            int second_sunday = 8 + (7 - (dt.day_of_week + 6) % 7);
            return dt.day >= second_sunday;
        }
        
        // 11月份：第一个周日结束
        if(dt.mon == 11)
        {
            int first_sunday = 1 + (7 - (dt.day_of_week + 6) % 7);
            return dt.day < first_sunday;
        }
        
        return false;
    }
    
    //+------------------------------------------------------------------+
    //| 将服务器时间转换为GMT时间                                          |
    //+------------------------------------------------------------------+
    datetime ServerTimeToGMT(datetime server_time, int gmt_offset_winter, int dst_offset = 1)
    {
        int total_offset = gmt_offset_winter;
        if(IsDaylightSavingTime(server_time))
            total_offset += dst_offset;
            
        return server_time - total_offset * 3600;
    }
    
    //+------------------------------------------------------------------+
    //| 获取当前交易时段（重载版本1）                                       |
    //+------------------------------------------------------------------+
    ENUM_TRADING_SESSION GetCurrent(datetime server_time, int gmt_offset_winter = 0, int dst_offset = 1)
    {
        // 转换为GMT时间
        datetime gmt_time = ServerTimeToGMT(server_time, gmt_offset_winter, dst_offset);
        
        MqlDateTime dt;
        TimeToStruct(gmt_time, dt);
        
        // 计算当前GMT小时
        int gmt_hour = dt.hour;
        
        // 判断交易时段
        // 亚洲时段: 00:00-09:00 GMT
        if(gmt_hour >= 0 && gmt_hour < 9)
        {
            current_session = SESSION_ASIA;
            return SESSION_ASIA;
        }
        
        // 伦敦时段: 08:00-17:00 GMT
        if(gmt_hour >= 8 && gmt_hour < 17)
        {
            // 检查是否为重叠时段 (13:00-17:00 GMT)
            if(gmt_hour >= 13)
            {
                current_session = SESSION_OVERLAP;
                return SESSION_OVERLAP;
            }
            current_session = SESSION_LONDON;
            return SESSION_LONDON;
        }
        
        // 纽约时段: 13:00-22:00 GMT
        if(gmt_hour >= 13 && gmt_hour < 22)
        {
            // 13:00-17:00已经在上面处理为重叠时段
            if(gmt_hour >= 17)
            {
                current_session = SESSION_NEWYORK;
                return SESSION_NEWYORK;
            }
        }
        
        // 其他时段
        current_session = SESSION_NONE;
        return SESSION_NONE;
    }
    
    //+------------------------------------------------------------------+
    //| 获取时段名称                                                       |
    //+------------------------------------------------------------------+
    string GetSessionName(ENUM_TRADING_SESSION session)
    {
        switch(session)
        {
            case SESSION_ASIA:    return "亚洲时段";
            case SESSION_LONDON:  return "伦敦时段";
            case SESSION_NEWYORK: return "纽约时段";
            case SESSION_OVERLAP: return "重叠时段";
            default:              return "其他时段";
        }
    }
    
    //+------------------------------------------------------------------+
    //| 获取时段颜色                                                       |
    //+------------------------------------------------------------------+
    color GetSessionColor(ENUM_TRADING_SESSION session)
    {
        switch(session)
        {
            case SESSION_ASIA:    return COLOR_SESSION_ASIA;
            case SESSION_LONDON:  return COLOR_SESSION_LONDON;
            case SESSION_NEWYORK: return COLOR_SESSION_NEWYORK;
            case SESSION_OVERLAP: return COLOR_SESSION_OVERLAP;
            default:              return COLOR_INFO;
        }
    }
    
    //+------------------------------------------------------------------+
    //| 获取时段开始时间                                                   |
    //+------------------------------------------------------------------+
    datetime GetSessionStartTime(ENUM_TRADING_SESSION session, datetime current_time)
    {
        MqlDateTime dt;
        TimeToStruct(current_time, dt);
        dt.min = 0;
        dt.sec = 0;
        
        switch(session)
        {
            case SESSION_ASIA:
                dt.hour = 0;
                break;
            case SESSION_LONDON:
                dt.hour = 8;
                break;
            case SESSION_NEWYORK:
                dt.hour = 13;
                break;
            case SESSION_OVERLAP:
                dt.hour = 13;
                break;
            default:
                return 0;
        }
        
        return StructToTime(dt);
    }
    
    //+------------------------------------------------------------------+
    //| 获取时段结束时间                                                   |
    //+------------------------------------------------------------------+
    datetime GetSessionEndTime(ENUM_TRADING_SESSION session, datetime current_time)
    {
        MqlDateTime dt;
        TimeToStruct(current_time, dt);
        dt.min = 0;
        dt.sec = 0;
        
        switch(session)
        {
            case SESSION_ASIA:
                dt.hour = 9;
                break;
            case SESSION_LONDON:
                dt.hour = 17;
                break;
            case SESSION_NEWYORK:
                dt.hour = 22;
                break;
            case SESSION_OVERLAP:
                dt.hour = 17;
                break;
            default:
                return 0;
        }
        
        return StructToTime(dt);
    }
    
    //+------------------------------------------------------------------+
    //| 检查指定时间是否在活跃交易时段                                      |
    //+------------------------------------------------------------------+
    bool IsActiveSession(datetime check_time, int gmt_offset_winter = 0, int dst_offset = 1)
    {
        ENUM_TRADING_SESSION session = GetCurrent(check_time, gmt_offset_winter, dst_offset);
        return (session != SESSION_NONE);
    }
    
    //+------------------------------------------------------------------+
    //| 获取时段活跃度评分 (0-100)                                         |
    //+------------------------------------------------------------------+
    int GetSessionActivityScore(ENUM_TRADING_SESSION session)
    {
        switch(session)
        {
            case SESSION_OVERLAP: return 100;  // 最高活跃度
            case SESSION_LONDON:  return 85;   // 高活跃度
            case SESSION_NEWYORK: return 80;   // 高活跃度
            case SESSION_ASIA:    return 60;   // 中等活跃度
            default:              return 20;   // 低活跃度
        }
    }
    
    //+------------------------------------------------------------------+
    //| 初始化时段分析模块                                                 |
    //+------------------------------------------------------------------+
    void Initialize()
    {
        DEBUG_PRINT("交易时段分析模块初始化完成");
    }
    
    //+------------------------------------------------------------------+
    //+------------------------------------------------------------------+
    //| 获取当前交易时段（重载版本2 - 仅时间参数）                          |
    //+------------------------------------------------------------------+
    ENUM_TRADING_SESSION GetCurrent(datetime server_time)
    {
        return GetCurrent(server_time, 0, 1);
    }
    
    //+------------------------------------------------------------------+
    //| 清理时段分析模块                                                   |
    //+------------------------------------------------------------------+
    void Deinitialize()
    {
        DEBUG_PRINT("交易时段分析模块已清理");
    }
}
