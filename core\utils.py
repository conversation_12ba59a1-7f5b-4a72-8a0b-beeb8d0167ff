import time
import logging
from functools import wraps

logger = logging.getLogger(__name__)

def timeit(func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        start = time.time()
        logger.info(f"开始执行 {func.__name__}")
        result = func(*args, **kwargs)
        end = time.time()
        logger.info(f"完成执行 {func.__name__}, 耗时: {end - start:.2f}秒")
        return result
    return wrapper 