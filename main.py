import threading
import logging
import sys
import signal
from datetime import datetime, timedelta
import time
from platforms.mt5.mt5_trader import MT5Trader
from platforms.binance.binance_analyzer import BinanceMarketAnalyzer
from utils.log_utils import setup_logger
import os

# 添加一个全局的停止标志
running = True

def signal_handler(signum, frame):
    """处理 Ctrl+C 信号"""
    global running
    print("\n收到停止信号，正在关闭程序...")
    running = False

# 注册信号处理器
signal.signal(signal.SIGINT, signal_handler)

# 设置日志
logger = setup_logger(
    'main', 
    os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), 'logs', 'main', 'main.log'),
    level=logging.INFO
)

def run_mt5():
    """运行MT5交易"""
    global running
    try:
        trader = MT5Trader()
        while running:
            try:
                # ... MT5 的运行逻辑 ...
                if not running:
                    break
            except Exception as e:
                logger.error(f"MT5交易系统出错: {str(e)}")
                if not running:
                    break
                time.sleep(60)
    except Exception as e:
        logger.error(f"MT5线程出错: {str(e)}")
    finally:
        logger.info("MT5线程已停止")

def run_binance():
    """运行Binance分析"""
    global running
    logger.info("开始运行Binance分析系统")
    
    while running:
        try:
            # ... Binance 的运行逻辑 ...
            if not running:
                break
        except Exception as e:
            logger.error(f"Binance分析系统出错: {str(e)}")
            if not running:
                break
            time.sleep(60)
    
    logger.info("Binance线程已停止")

def main():
    """主函数"""
    try:
        # 创建线程
        mt5_thread = threading.Thread(target=run_mt5, name="MT5_Thread")
        binance_thread = threading.Thread(target=run_binance, name="Binance_Thread")
        
        # 启动线程
        logger.info("启动MT5交易线程")
        mt5_thread.start()
        
        logger.info("启动Binance分析线程")
        binance_thread.start()
        
        # 主循环
        while running:
            time.sleep(1)
            
        # 等待线程结束
        logger.info("等待线程结束...")
        mt5_thread.join(timeout=5)
        binance_thread.join(timeout=5)
        
    except Exception as e:
        logger.error(f"主程序出错: {str(e)}")
    finally:
        logger.info("程序已完全停止")

if __name__ == "__main__":
    main() 