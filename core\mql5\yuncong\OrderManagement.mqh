//+------------------------------------------------------------------+
//|                                        OrderManagement.mqh       |
//|                                                                  |
//|                                                                  |
//+------------------------------------------------------------------+
#ifndef ORDER_MANAGEMENT_MQH
#define ORDER_MANAGEMENT_MQH

#include <Trade\Trade.mqh>
#include "Parameters.mqh"

// 声明全局交易对象
extern CTrade trade;
extern int handle_atr;  // 需要访问ATR指标句柄

//+------------------------------------------------------------------+
//| 计算订单参数(入场价、止损价、止盈价)                              |
//+------------------------------------------------------------------+
bool CalculateOrderParams(const MqlRates &breakout_candle,   // 输入: 突破发生的K线
                         const double atr_at_breakout,     // 输入: 突破发生时的ATR值
                         bool is_long,                     // 输入: 是否为做多交易
                         double &entry_price,             // 输出: 入场价
                         double &stop_loss,               // 输出: 止损价
                         double &take_profit,             // 输出: 止盈价
                         double breakout_level = 0)       // 输入: 被突破的支撑/阻力水平价格
{
   if(atr_at_breakout <= 0)
      return false;
   
   if(is_long)
   {
      entry_price = SymbolInfoDouble(_Symbol, SYMBOL_ASK); // 使用当前Ask价
      
      // 基于被突破的阻力位和ATR计算止损
      if(breakout_level > 0)
      {
         // 使用被突破的阻力位作为参考点
         stop_loss = NormalizeDouble(breakout_level - atr_at_breakout * inp_StopLossAtrMult, _Digits);
         // 如果计算出的止损高于突破K线的低点，则使用突破K线的低点
         if(stop_loss > breakout_candle.low)
            stop_loss = NormalizeDouble(breakout_candle.low - atr_at_breakout * 0.5, _Digits);
      }
      else
      {
         // 如果没有提供突破水平，则使用原来的方法
         stop_loss = NormalizeDouble(breakout_candle.low - atr_at_breakout * inp_StopLossAtrMult, _Digits);
      }
      
      // 检查止损距离是否符合最小要求
      double min_stop_distance = SymbolInfoInteger(_Symbol, SYMBOL_TRADE_STOPS_LEVEL) * SymbolInfoDouble(_Symbol, SYMBOL_POINT);
      if(entry_price - stop_loss < min_stop_distance)
      {
         stop_loss = entry_price - min_stop_distance;
         stop_loss = NormalizeDouble(stop_loss, _Digits);
      }
      
      // 如果设置了止盈比例
      if(inp_TakeProfitRRR > 0)
      {
         double risk = entry_price - stop_loss;
         take_profit = entry_price + risk * inp_TakeProfitRRR;
         take_profit = NormalizeDouble(take_profit, _Digits);
      }
      else
      {
         take_profit = 0; // 不设置止盈
      }
   }
   else // 做空
   {
      entry_price = SymbolInfoDouble(_Symbol, SYMBOL_BID); // 使用当前Bid价
      
      // 基于被突破的支撑位和ATR计算止损
      if(breakout_level > 0)
      {
         // 使用被突破的支撑位作为参考点
         stop_loss = NormalizeDouble(breakout_level + atr_at_breakout * inp_StopLossAtrMult, _Digits);
         // 如果计算出的止损低于突破K线的高点，则使用突破K线的高点
         if(stop_loss < breakout_candle.high)
            stop_loss = NormalizeDouble(breakout_candle.high + atr_at_breakout * 0.5, _Digits);
      }
      else
      {
         // 如果没有提供突破水平，则使用原来的方法
         stop_loss = NormalizeDouble(breakout_candle.high + atr_at_breakout * inp_StopLossAtrMult, _Digits);
      }
      
      // 检查止损距离是否符合最小要求
      double min_stop_distance = SymbolInfoInteger(_Symbol, SYMBOL_TRADE_STOPS_LEVEL) * SymbolInfoDouble(_Symbol, SYMBOL_POINT);
      if(stop_loss - entry_price < min_stop_distance)
      {
         stop_loss = entry_price + min_stop_distance;
         stop_loss = NormalizeDouble(stop_loss, _Digits);
      }
      
      // 如果设置了止盈比例
      if(inp_TakeProfitRRR > 0)
      {
         double risk = stop_loss - entry_price;
         take_profit = entry_price - risk * inp_TakeProfitRRR;
         take_profit = NormalizeDouble(take_profit, _Digits);
      }
      else
      {
         take_profit = 0; // 不设置止盈
      }
   }
   
   return true;
}

//+------------------------------------------------------------------+
//| 下单函数                                                         |
//+------------------------------------------------------------------+
bool PlaceOrder(bool is_long, double lot_size, double stop_loss, double take_profit, string comment)
{
   bool result = false;
   
   if(is_long)
   {
      result = trade.Buy(lot_size, _Symbol, 0, stop_loss, take_profit, comment);
      if(result)
         Print("做多入场成功: 止损=", stop_loss, ", 止盈=", take_profit, ", 手数=", lot_size);
      else
         Print("做多入场失败: ", GetLastError());
   }
   else
   {
      result = trade.Sell(lot_size, _Symbol, 0, stop_loss, take_profit, comment);
      if(result)
         Print("做空入场成功: 止损=", stop_loss, ", 止盈=", take_profit, ", 手数=", lot_size);
      else
         Print("做空入场失败: ", GetLastError());
   }
   
   return result;
}

//+------------------------------------------------------------------+
//| 管理现有持仓                                                     |
//+------------------------------------------------------------------+
void ManageOpenPositions()
{
   // 获取当前ATR值，用于追踪止损计算
   double atr_values[];
   ArraySetAsSeries(atr_values, true);
   if(CopyBuffer(handle_atr, 0, 0, 3, atr_values) < 3)
   {
      Print("获取ATR数据失败，无法管理持仓");
      return;
   }
   
   double current_atr = atr_values[0];
   
   // 遍历所有持仓
   for(int i = PositionsTotal() - 1; i >= 0; i--)
   {
      ulong ticket = PositionGetTicket(i);
      if(ticket <= 0)
         continue;
         
      if(!PositionSelectByTicket(ticket))
         continue;
         
      // 检查魔术数字
      if(PositionGetInteger(POSITION_MAGIC) != inp_MagicNumber)
         continue;
         
      // 获取持仓信息
      double open_price = PositionGetDouble(POSITION_PRICE_OPEN);
      double current_price = PositionGetDouble(POSITION_PRICE_CURRENT);
      double sl = PositionGetDouble(POSITION_SL);
      double tp = PositionGetDouble(POSITION_TP);
      double volume = PositionGetDouble(POSITION_VOLUME);
      ENUM_POSITION_TYPE pos_type = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);
      
      // 检查是否需要部分止盈
      if(inp_UsePartialTP && volume > SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN) * 1.5)
      {
         double risk = 0;
         double profit_target = 0;
         
         if(pos_type == POSITION_TYPE_BUY)
         {
            risk = open_price - sl;
            profit_target = open_price + risk * inp_PartialTP_RRR;
            
            if(current_price >= profit_target)
            {
               // 计算要平仓的手数
               double close_volume = volume * inp_PartialClosePercent;
               close_volume = NormalizeDouble(close_volume, 2);
               
               // 确保不小于最小手数
               if(close_volume >= SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN))
               {
                  if(trade.PositionClosePartial(ticket, close_volume))
                     Print("部分止盈成功: 平仓手数=", close_volume);
                  else
                     Print("部分止盈失败: ", GetLastError());
               }
            }
         }
         else if(pos_type == POSITION_TYPE_SELL)
         {
            risk = sl - open_price;
            profit_target = open_price - risk * inp_PartialTP_RRR;
            
            if(current_price <= profit_target)
            {
               // 计算要平仓的手数
               double close_volume = volume * inp_PartialClosePercent;
               close_volume = NormalizeDouble(close_volume, 2);
               
               // 确保不小于最小手数
               if(close_volume >= SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN))
               {
                  if(trade.PositionClosePartial(ticket, close_volume))
                     Print("部分止盈成功: 平仓手数=", close_volume);
                  else
                     Print("部分止盈失败: ", GetLastError());
               }
            }
         }
      }
      
      // 检查是否需要移动到保本
      if(inp_UseBreakEven)
      {
         double risk = 0;
         double breakeven_target = 0;
         double new_sl = 0;
         
         if(pos_type == POSITION_TYPE_BUY)
         {
            risk = open_price - sl;
            breakeven_target = open_price + risk;
            
            if(current_price >= breakeven_target && sl < open_price)
            {
               // 移动止损到保本位置，加上缓冲
               new_sl = open_price + inp_BreakEvenBufferPoints * SymbolInfoDouble(_Symbol, SYMBOL_POINT);
               new_sl = NormalizeDouble(new_sl, _Digits);
               
               if(trade.PositionModify(ticket, new_sl, tp))
                  Print("移动止损到保本位置成功: 新止损=", new_sl);
               else
                  Print("移动止损到保本位置失败: ", GetLastError());
            }
         }
         else if(pos_type == POSITION_TYPE_SELL)
         {
            risk = sl - open_price;
            breakeven_target = open_price - risk;
            
            if(current_price <= breakeven_target && sl > open_price)
            {
               // 移动止损到保本位置，加上缓冲
               new_sl = open_price - inp_BreakEvenBufferPoints * SymbolInfoDouble(_Symbol, SYMBOL_POINT);
               new_sl = NormalizeDouble(new_sl, _Digits);
               
               if(trade.PositionModify(ticket, new_sl, tp))
                  Print("移动止损到保本位置成功: 新止损=", new_sl);
               else
                  Print("移动止损到保本位置失败: ", GetLastError());
            }
         }
      }
      
      // 检查是否需要追踪止损
      if(inp_UseTrailingStop && current_atr > 0)
      {
         double new_sl = 0;
         
         if(pos_type == POSITION_TYPE_BUY)
         {
            // 计算新的追踪止损位置
            new_sl = current_price - current_atr * inp_TrailingStopAtrMult;
            new_sl = NormalizeDouble(new_sl, _Digits);
            
            // 只有当新止损高于当前止损时才修改
            if(new_sl > sl)
            {
               if(trade.PositionModify(ticket, new_sl, tp))
                  Print("追踪止损更新成功: 新止损=", new_sl);
               else
                  Print("追踪止损更新失败: ", GetLastError());
            }
         }
         else if(pos_type == POSITION_TYPE_SELL)
         {
            // 计算新的追踪止损位置
            new_sl = current_price + current_atr * inp_TrailingStopAtrMult;
            new_sl = NormalizeDouble(new_sl, _Digits);
            
            // 只有当新止损低于当前止损时才修改
            if(new_sl < sl || sl == 0)
            {
               if(trade.PositionModify(ticket, new_sl, tp))
                  Print("追踪止损更新成功: 新止损=", new_sl);
               else
                  Print("追踪止损更新失败: ", GetLastError());
            }
         }
      }
   }
}

#endif // ORDER_MANAGEMENT_MQH