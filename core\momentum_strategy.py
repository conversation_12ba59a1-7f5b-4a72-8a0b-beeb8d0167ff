from typing import Dict, List, Tuple
import pandas as pd
import numpy as np
import logging
from utils.time_utils import timeit

# 设置日志
logger = logging.getLogger(__name__)

class MomentumStrategy:
    """
    动量突破策略 - 利用价格动量和短期EMA捕捉短期爆发性走势
    
    策略原理：
    - 利用价格动量和短期EMA确认快速趋势，适合捕捉短期爆发性走势
    - 使用ROC指标衡量价格动量，使用EMA确认趋势方向
    - 在价格突破EMA且动量方向一致时产生信号
    """
    
    def __init__(self) -> None:
        self.roc_period: int = 10      # ROC指标周期
        self.ema_period: int = 8       # EMA指标周期
        self.atr_period: int = 14      # ATR指标周期
        self.atr_multiplier: float = 1.5  # 止盈目标倍数
        self.min_bars: int = 20        # 最少需要的K线数量
        
    def analyze(self, data: pd.DataFrame) -> Tuple[bool, str, Dict]:
        """
        执行动量突破策略分析
        
        参数:
            data (pd.DataFrame): 包含OHLCV数据的DataFrame
                必须包含的列：'open', 'high', 'low', 'close'
        
        返回:
            Tuple[bool, str, Dict]: (是否形成信号, 详细信息, 信号详情字典)
        """
        try:
            if len(data) < self.min_bars:
                return False, "数据量不足", {}
                
            # 使用最近的数据进行分析
            recent_data = data.tail(self.min_bars).copy()
            
            # 计算技术指标
            self._calculate_indicators(recent_data)
            
            # 去除NaN值
            valid_data = recent_data.dropna()
            if len(valid_data) < 2:
                return False, "有效数据不足", {}
            
            # 获取最新和前一个K线数据
            current_bar = valid_data.iloc[-1]
            prev_bar = valid_data.iloc[-2]
            
            # 判断是否形成做多信号
            bullish_signal = (
                current_bar['close'] > current_bar['ema8'] and  # 价格在EMA上方
                prev_bar['close'] <= prev_bar['ema8'] and  # 前一根K线价格在EMA下方或等于EMA(突破条件)
                current_bar['roc10'] > 0  # 动量向上
            )
            
            # 判断是否形成做空信号
            bearish_signal = (
                current_bar['close'] < current_bar['ema8'] and  # 价格在EMA下方
                prev_bar['close'] >= prev_bar['ema8'] and  # 前一根K线价格在EMA上方或等于EMA(突破条件)
                current_bar['roc10'] < 0  # 动量向下
            )
            
            # 计算止盈目标和止损位
            signal_details = {}
            
            if bullish_signal:
                entry_price = current_bar['close']
                stop_loss = current_bar['ema8']  # 止损设在EMA下方
                take_profit = entry_price + (self.atr_multiplier * current_bar['atr'])  # 目标为1.5倍ATR
                
                # 计算风险回报比
                risk = entry_price - stop_loss
                reward = take_profit - entry_price
                risk_reward_ratio = reward / risk if risk > 0 else 0
                
                signal_details = {
                    'signal_type': 'LONG',
                    'entry_price': entry_price,
                    'stop_loss': stop_loss,
                    'take_profit': take_profit,
                    'risk_reward_ratio': risk_reward_ratio,
                    'ema8': current_bar['ema8'],
                    'roc10': current_bar['roc10'],
                    'atr': current_bar['atr']
                }
                
                info = (
                    f"做多信号 - "
                    f"价格: {entry_price:.6f}, "
                    f"EMA8: {current_bar['ema8']:.6f}, "
                    f"ROC10: {current_bar['roc10']:.2f}%, "
                    f"止损: {stop_loss:.6f}, "
                    f"止盈: {take_profit:.6f}, "
                    f"风险回报比: {risk_reward_ratio:.2f}"
                )
                return True, info, signal_details
                
            elif bearish_signal:
                entry_price = current_bar['close']
                stop_loss = current_bar['ema8']  # 止损设在EMA上方
                take_profit = entry_price - (self.atr_multiplier * current_bar['atr'])  # 目标为1.5倍ATR
                
                # 计算风险回报比
                risk = stop_loss - entry_price
                reward = entry_price - take_profit
                risk_reward_ratio = reward / risk if risk > 0 else 0
                
                signal_details = {
                    'signal_type': 'SHORT',
                    'entry_price': entry_price,
                    'stop_loss': stop_loss,
                    'take_profit': take_profit,
                    'risk_reward_ratio': risk_reward_ratio,
                    'ema8': current_bar['ema8'],
                    'roc10': current_bar['roc10'],
                    'atr': current_bar['atr']
                }
                
                info = (
                    f"做空信号 - "
                    f"价格: {entry_price:.6f}, "
                    f"EMA8: {current_bar['ema8']:.6f}, "
                    f"ROC10: {current_bar['roc10']:.2f}%, "
                    f"止损: {stop_loss:.6f}, "
                    f"止盈: {take_profit:.6f}, "
                    f"风险回报比: {risk_reward_ratio:.2f}"
                )
                return True, info, signal_details
                
            return False, "未形成有效的动量EMA突破信号", {}
            
        except Exception as e:
            logger.error(f"动量EMA突破分析出错: {str(e)}")
            return False, f"分析出错: {str(e)}", {}
    
    def _calculate_indicators(self, data: pd.DataFrame) -> None:
        """
        计算策略所需的技术指标
        
        参数:
            data (pd.DataFrame): 价格数据
        """
        # 计算8周期EMA
        data['ema8'] = data['close'].ewm(span=self.ema_period, adjust=False).mean()
        
        # 计算10周期ROC (Rate of Change)
        data['roc10'] = data['close'].pct_change(periods=self.roc_period) * 100
        
        # 计算ATR (用于设置止盈目标)
        data['tr'] = np.maximum(
            data['high'] - data['low'],
            np.maximum(
                abs(data['high'] - data['close'].shift(1)),
                abs(data['low'] - data['close'].shift(1))
            )
        )
        data['atr'] = data['tr'].rolling(window=self.atr_period).mean()
    
    @timeit
    def backtest(self, data: pd.DataFrame) -> Dict:
        """
        对策略进行回测
        
        参数:
            data (pd.DataFrame): 包含OHLCV数据的DataFrame
        
        返回:
            Dict: 回测结果统计
        """
        if len(data) < self.min_bars:
            return {"error": "数据量不足"}
        
        # 复制数据以避免修改原始数据
        df = data.copy()
        
        # 计算技术指标
        self._calculate_indicators(df)
        
        # 去除NaN值
        df = df.dropna().reset_index(drop=True)
        
        # 初始化结果
        results = {
            'trades': [],
            'win_count': 0,
            'loss_count': 0,
            'total_profit': 0,
            'total_loss': 0,
            'max_drawdown': 0
        }
        
        # 模拟交易
        position = None
        entry_price = 0
        stop_loss = 0
        take_profit = 0
        
        for i in range(1, len(df)):
            current = df.iloc[i]
            previous = df.iloc[i-1]
            
            # 如果没有持仓，检查入场信号
            if position is None:
                # 做多信号
                if (current['close'] > current['ema8'] and 
                    previous['close'] <= previous['ema8'] and 
                    current['roc10'] > 0):
                    
                    position = 'LONG'
                    entry_price = current['close']
                    stop_loss = current['ema8']
                    take_profit = entry_price + (self.atr_multiplier * current['atr'])
                    
                # 做空信号
                elif (current['close'] < current['ema8'] and 
                      previous['close'] >= previous['ema8'] and 
                      current['roc10'] < 0):
                    
                    position = 'SHORT'
                    entry_price = current['close']
                    stop_loss = current['ema8']
                    take_profit = entry_price - (self.atr_multiplier * current['atr'])
            
            # 如果有持仓，检查出场信号
            elif position == 'LONG':
                # 止损
                if current['low'] <= stop_loss:
                    profit = (stop_loss - entry_price) / entry_price * 100
                    results['trades'].append({
                        'type': 'LONG',
                        'entry': entry_price,
                        'exit': stop_loss,
                        'profit': profit,
                        'exit_reason': 'STOP_LOSS'
                    })
                    
                    if profit >= 0:
                        results['win_count'] += 1
                        results['total_profit'] += profit
                    else:
                        results['loss_count'] += 1
                        results['total_loss'] += abs(profit)
                    
                    position = None
                
                # 止盈
                elif current['high'] >= take_profit:
                    profit = (take_profit - entry_price) / entry_price * 100
                    results['trades'].append({
                        'type': 'LONG',
                        'entry': entry_price,
                        'exit': take_profit,
                        'profit': profit,
                        'exit_reason': 'TAKE_PROFIT'
                    })
                    
                    results['win_count'] += 1
                    results['total_profit'] += profit
                    position = None
                
                # 动量反转退出
                elif current['roc10'] < 0:
                    profit = (current['close'] - entry_price) / entry_price * 100
                    results['trades'].append({
                        'type': 'LONG',
                        'entry': entry_price,
                        'exit': current['close'],
                        'profit': profit,
                        'exit_reason': 'MOMENTUM_REVERSAL'
                    })
                    
                    if profit >= 0:
                        results['win_count'] += 1
                        results['total_profit'] += profit
                    else:
                        results['loss_count'] += 1
                        results['total_loss'] += abs(profit)
                    
                    position = None
            
            elif position == 'SHORT':
                # 止损
                if current['high'] >= stop_loss:
                    profit = (entry_price - stop_loss) / entry_price * 100
                    results['trades'].append({
                        'type': 'SHORT',
                        'entry': entry_price,
                        'exit': stop_loss,
                        'profit': profit,
                        'exit_reason': 'STOP_LOSS'
                    })
                    
                    if profit >= 0:
                        results['win_count'] += 1
                        results['total_profit'] += profit
                    else:
                        results['loss_count'] += 1
                        results['total_loss'] += abs(profit)
                    
                    position = None
                
                # 止盈
                elif current['low'] <= take_profit:
                    profit = (entry_price - take_profit) / entry_price * 100
                    results['trades'].append({
                        'type': 'SHORT',
                        'entry': entry_price,
                        'exit': take_profit,
                        'profit': profit,
                        'exit_reason': 'TAKE_PROFIT'
                    })
                    
                    results['win_count'] += 1
                    results['total_profit'] += profit
                    position = None
                
                # 动量反转退出
                elif current['roc10'] > 0:
                    profit = (entry_price - current['close']) / entry_price * 100
                    results['trades'].append({
                        'type': 'SHORT',
                        'entry': entry_price,
                        'exit': current['close'],
                        'profit': profit,
                        'exit_reason': 'MOMENTUM_REVERSAL'
                    })
                    
                    if profit >= 0:
                        results['win_count'] += 1
                        results['total_profit'] += profit
                    else:
                        results['loss_count'] += 1
                        results['total_loss'] += abs(profit)
                    
                    position = None
        
        # 计算统计数据
        total_trades = results['win_count'] + results['loss_count']
        if total_trades > 0:
            results['win_rate'] = results['win_count'] / total_trades * 100
            results['profit_factor'] = results['total_profit'] / results['total_loss'] if results['total_loss'] > 0 else float('inf')
            
            # 计算最大回撤
            equity_curve = [0]
            for trade in results['trades']:
                equity_curve.append(equity_curve[-1] + trade['profit'])
            
            max_equity = 0
            max_drawdown = 0
            for equity in equity_curve:
                max_equity = max(max_equity, equity)
                drawdown = max_equity - equity
                max_drawdown = max(max_drawdown, drawdown)
            
            results['max_drawdown'] = max_drawdown
            
        return results
    
    def optimize_parameters(self, data: pd.DataFrame, param_ranges: Dict) -> Dict:
        """
        优化策略参数
        
        参数:
            data (pd.DataFrame): 包含OHLCV数据的DataFrame
            param_ranges (Dict): 参数范围字典，例如 {'roc_period': [5, 10, 15], 'ema_period': [5, 8, 13]}
        
        返回:
            Dict: 最优参数和对应的回测结果
        """
        best_result = {
            'win_rate': 0,
            'profit_factor': 0,
            'params': {}
        }
        
        # 保存原始参数
        original_roc = self.roc_period
        original_ema = self.ema_period
        original_atr = self.atr_period
        original_multiplier = self.atr_multiplier
        
        # 获取参数范围
        roc_periods = param_ranges.get('roc_period', [self.roc_period])
        ema_periods = param_ranges.get('ema_period', [self.ema_period])
        atr_periods = param_ranges.get('atr_period', [self.atr_period])
        atr_multipliers = param_ranges.get('atr_multiplier', [self.atr_multiplier])
        
        # 遍历所有参数组合
        for roc in roc_periods:
            for ema in ema_periods:
                for atr in atr_periods:
                    for multiplier in atr_multipliers:
                        # 设置参数
                        self.roc_period = roc
                        self.ema_period = ema
                        self.atr_period = atr
                        self.atr_multiplier = multiplier
                        
                        # 执行回测
                        result = self.backtest(data)
                        
                        # 检查是否有足够的交易
                        total_trades = result.get('win_count', 0) + result.get('loss_count', 0)
                        if total_trades < 5:
                            continue
                        
                        # 计算评分 (结合胜率和盈亏比)
                        win_rate = result.get('win_rate', 0)
                        profit_factor = result.get('profit_factor', 0)
                        score = win_rate * profit_factor
                        
                        # 更新最佳结果
                        if score > best_result['win_rate'] * best_result['profit_factor']:
                            best_result = {
                                'win_rate': win_rate,
                                'profit_factor': profit_factor,
                                'total_trades': total_trades,
                                'max_drawdown': result.get('max_drawdown', 0),
                                'params': {
                                    'roc_period': roc,
                                    'ema_period': ema,
                                    'atr_period': atr,
                                    'atr_multiplier': multiplier
                                },
                                'full_result': result
                            }
        
        # 恢复原始参数
        self.roc_period = original_roc
        self.ema_period = original_ema
        self.atr_period = original_atr
        self.atr_multiplier = original_multiplier
        
        return best_result