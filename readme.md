# 多平台交易分析系统

## 项目概述

这是一个基于多平台的高级技术分析交易系统，集成了 MetaTrader 5 (MT5) 和币安 (Binance) 两大交易平台。系统采用模块化设计，结合多种技术分析方法，实现对市场的智能分析和自动交易。

## 系统架构

### 核心模块 (core/)

- **pattern_analyzer.py**: 交易形态分析器
  - 支持多种技术形态识别
  - 包含趋势分析、形态识别等功能

- **unified_analyzer.py**: 统一分析器
  - 整合多种分析策略
  - 提供统一的分析接口

- **notifier.py**: 消息通知服务
  - 支持多种通知方式
  - 实时信号推送

### 平台模块 (platforms/)

#### MT5 平台 (mt5/)
- **mt5_trader.py**: MT5交易执行器
  - 订单管理
  - 仓位控制
  - 风险管理

- **mt5_analyzer.py**: MT5市场分析器
  - 技术分析
  - 信号生成

- **mt5_data.py**: MT5数据获取器
  - 实时数据获取
  - 历史数据管理

#### 币安平台 (binance/)
- **binance_analyzer.py**: 币安市场分析器
  - 加密货币市场分析
  - 交易信号生成

- **binance_data.py**: 币安数据获取器
  - 市场数据获取
  - 实时价格监控

### 调度模块 (cmd/)
- **scheduler/**: 任务调度器
  - 多任务协调
  - 定时任务管理

## 功能特性

### 1. 市场分析
- 多维度技术分析
- 实时市场监控
- 多品种同步分析
- 多周期分析支持

### 2. 交易执行
- 智能订单管理
- 风险控制系统
- 动态仓位管理
- 多策略支持

### 3. 系统功能
- 实时数据处理
- 异常处理机制
- 日志记录系统
- 消息推送服务

## 支持品种

### MT5平台
- 贵金属：XAUUSDc (黄金)
- 加密货币：BTCUSDc (比特币)

### 币安平台
- 支持所有主流交易对
- 现货交易支持

## 时间周期支持

- M15（15分钟）
- H1（1小时）
- H4（4小时）

## 安装部署

### 环境要求
- Python 3.7+
- MetaTrader 5
- 必要的Python包（见requirements.txt）

### 安装步骤

1. 克隆项目
```bash
git clone [项目地址]
```

2. 安装依赖
```bash
pip install -r requirements.txt
```

3. 配置环境变量
- 设置MT5账户信息
- 配置币安API密钥
- 设置通知服务参数

## 使用指南

### 启动系统
```bash
python main.py
```

### 配置说明
- 在core/constants.py中设置基本参数
- 在platforms/mt5/mt5_trader.py中配置交易参数
- 在platforms/binance/binance_analyzer.py中设置分析参数

## 开发计划

- [ ] 引入凯利公式进行资金管理
- [ ] 优化多空策略
- [ ] 增强风险控制系统

## 注意事项

- 请确保MT5平台正常运行
- 定期检查API密钥有效性
- 关注系统日志信息
- 定期备份重要数据

## 许可证

[许可证类型]

## 贡献指南

欢迎提交Issue和Pull Request来帮助改进项目。
