import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from typing import Dict, List
import pandas as pd
import logging
from datetime import datetime
from core.pattern_analyzer import PatternAnalyzer
from core.notifier import NotificationService
from platforms.binance.binance_data import BinanceDataFetcher

from utils.log_utils import setup_logger

# 设置日志
logger = setup_logger(
    'binance_analuzer', 
    os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), 'logs', 'binance', 'analyzer.log'),
    level=logging.INFO
)

class BinanceMarketAnalyzer:
    """币安市场分析器"""
    
    def __init__(self, api_key: str = "", api_secret: str = ""):
        """
        初始化分析器
        如果不提供API密钥，将使用环境变量中的配置
        """
        self.api_key = api_key or os.getenv('BINANCE_API_KEY')
        self.api_secret = api_secret or os.getenv('BINANCE_API_SECRET')
        self.data_fetcher = BinanceDataFetcher(self.api_key, self.api_secret)
        self.pattern_analyzer = PatternAnalyzer()
        self.notifier = NotificationService()
        self.timeframes = ['15m', '1h', '4h']

    def analyze_market(self, **kwargs) -> List[Dict]:
        """市场分析主函数"""
        all_signals = []  # 用于收集所有信号
        try:
            notify = kwargs.pop('notify', True)
            
            symbols = self.data_fetcher.get_filtered_symbols(
                limit=kwargs.get('limit', 50),
                min_volume=kwargs.get('min_volume', 1000000),
                market_cap_tiers=kwargs.get('market_cap_tiers', ['Large', 'Medium'])
            )
            
            if not symbols:
                logger.warning("没有找到符合条件的交易对")
                return all_signals
                
            logger.info(f"分析 {len(symbols)} 个交易对")
            
            for symbol_info in symbols:
                symbol = symbol_info['symbol']
                market_cap_tier = symbol_info['market_cap_tier']
                
                for timeframe in self.timeframes:
                    try:
                        data = self.data_fetcher.get_klines(symbol, timeframe)
                        if data is None:
                            continue
                            
                        df = data.copy()
                        df['tick_volume'] = df['volume']
                        
                        signals = self.pattern_analyzer.detect_trading_signals(df)
                        
                        if signals:
                            for signal in signals:
                                # 添加额外信息
                                signal.update({
                                    'symbol': symbol,
                                    'timeframe': timeframe,
                                    'market_cap_tier': market_cap_tier
                                })
                                # 将信号添加到all_signals
                                all_signals.append(signal)
                                
                                extra_info = {
                                    'platform': 'Binance',
                                    'price': symbol_info['price'],
                                    'details': signal['details']
                                }
                                
                                # 从signal中获取extra_analysis
                                if 'extra_analysis' in signal:
                                    extra_info['extra_analysis'] = signal['extra_analysis']
                                
                                if notify:
                                    self.notifier.send_notification(
                                        symbol=symbol,
                                        timeframe=timeframe,
                                        signal_type=signal['type'],
                                        signal_strength=signal['strength'],
                                        conditions=signal['satisfied_patterns'],
                                        market_cap_tier=market_cap_tier,
                                        extra_info=extra_info
                                    )
                                    
                    except Exception as e:
                        logger.error(f"分析 {symbol} {timeframe} 时出错: {str(e)}")
                        continue
                        
            return all_signals
            
        except Exception as e:
            logger.error(f"市场分析过程出错: {str(e)}")
            return all_signals


def run_analysis(api_key: str = "", api_secret: str = "", **kwargs):
    """独立运行函数"""
    
    analyzer = BinanceMarketAnalyzer(api_key, api_secret)
    return analyzer.analyze_market(**kwargs)


if __name__ == "__main__":
    # 直接运行时的配置
    API_KEY = "Mf1zIJlnuy2DOTY7UI1Jo4ER6MAYAcj9IbpuCrOWkKLpegzbazRaTQr8F39Pps4y"
    API_SECRET = "5wB8Sk4tSYFYDtG2OpOJuPf5GwLX54jOR0TsLdZ3p5ClFjLnc93enr1tYNqkzMnj"
    
    signals = run_analysis(
        api_key=API_KEY,
        api_secret=API_SECRET,
        limit=10,
        min_volume=1000000,
        market_cap_tiers=['Large'],
        notify=True  # 是否发送通知
    )
    
    # 打印分析结果
    print(f"\n发现 {len(signals)} 个信号:")
    for signal in signals:
        print(f"\n{signal['symbol']} {signal['timeframe']} - {signal['type']}")