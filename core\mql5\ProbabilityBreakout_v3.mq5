//+------------------------------------------------------------------+
//|                                        ProbabilityBreakout_v3.mq5 |
//|                           智能学习概率突破系统 - 数据持久化版        |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright "概率交易系统 v3.0 - 智能学习版"
#property link      ""
#property version   "3.00"

//--- 输入参数
input group "=== 学习系统参数 ==="
input int      InpInitialLearningDays = 60;      // 初始学习天数
input int      InpIncrementalDays = 7;           // 增量学习天数
input bool     InpForceRebuild = false;          // 强制重建数据库
input int      InpMinSampleSize = 30;            // 最小样本数量要求

input group "=== 概率计算参数 ==="
input int      InpSuccessConfirmBars = 3;        // 成功确认需要的K线数
input double   InpProbThreshold = 0.65;          // 概率阈值
input bool     InpUseAdaptiveThreshold = true;   // 使用自适应阈值

input group "=== 支撑阻力计算 ==="
input int      InpSRLookback = 20;               // 支撑阻力位回看周期
input double   InpSRTolerancePips = 5;           // 支撑阻力位容差（点）

input group "=== 风险管理 ==="
input double   InpRiskPercent = 1.0;             // 每笔交易风险百分比
input double   InpRiskRewardRatio = 2.0;         // 风险回报比
input int      InpMaxConsecutiveLoss = 3;        // 最大连续亏损次数

//--- 数据结构
struct BreakoutEvent
{
    datetime time;           // 突破时间
    double   level;          // 突破水平
    bool     isUpBreakout;   // 是否向上突破
    bool     wasSuccessful;  // 是否成功
    int      maintainBars;   // 维持的K线数
    double   volatility;     // 当时的波动率
};

struct DatabaseHeader
{
    int      version;        // 数据库版本
    datetime lastUpdate;     // 最后更新时间
    int      totalEvents;    // 总事件数
    string   symbol;         // 交易品种
    int      timeframe;      // 时间周期
};

//--- 全局变量
BreakoutEvent historyEvents[];
DatabaseHeader dbHeader;
string databaseFile;
int consecutiveLoss = 0;
bool tradingEnabled = true;
datetime lastDataUpdate = 0;

//+------------------------------------------------------------------+
//| 专家顾问初始化函数                                                  |
//+------------------------------------------------------------------+
int OnInit()
{
    Print("概率突破系统 v3.0 初始化 - 智能学习版");
    
    // 构建数据库文件名
    databaseFile = StringFormat("%s_%s_%d_BreakoutDB.bin", 
                                _Symbol, 
                                EnumToString((ENUM_TIMEFRAMES)_Period), 
                                InpSRLookback);
    
    Print("数据库文件: ", databaseFile);
    
    // 初始化学习系统
    if(!InitializeLearningSystem())
    {
        Print("学习系统初始化失败！");
        return INIT_FAILED;
    }
    
    Print("学习系统初始化完成，历史事件: ", ArraySize(historyEvents), " 个");
    PrintCurrentStatistics();
    
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| 初始化学习系统                                                     |
//+------------------------------------------------------------------+
bool InitializeLearningSystem()
{
    bool needRebuild = InpForceRebuild;
    
    // 尝试加载现有数据库
    if(!needRebuild && LoadDatabase())
    {
        Print("成功加载现有数据库，最后更新: ", TimeToString(dbHeader.lastUpdate));
        
        // 检查是否需要增量更新
        datetime currentTime = TimeCurrent();
        int daysSinceUpdate = (int)((currentTime - dbHeader.lastUpdate) / 86400);
        
        if(daysSinceUpdate >= InpIncrementalDays)
        {
            Print("数据库需要更新，距离上次更新: ", daysSinceUpdate, " 天");
            if(!IncrementalUpdate(InpIncrementalDays + 5))  // 多更新几天确保完整
            {
                Print("增量更新失败，执行完整重建");
                needRebuild = true;
            }
        }
    }
    else
    {
        needRebuild = true;
    }
    
    // 需要重建数据库
    if(needRebuild)
    {
        Print("构建新的历史数据库...");
        if(!BuildCompleteDatabase())
        {
            Print("数据库构建失败");
            return false;
        }
        
        if(!SaveDatabase())
        {
            Print("数据库保存失败");
            return false;
        }
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| 构建完整数据库                                                     |
//+------------------------------------------------------------------+
bool BuildCompleteDatabase()
{
    // 计算需要的历史数据量
    int totalBars = InpInitialLearningDays * 24;  // 假设H1图表
    if(_Period == PERIOD_M15) totalBars = InpInitialLearningDays * 96;
    else if(_Period == PERIOD_M5) totalBars = InpInitialLearningDays * 288;
    else if(_Period == PERIOD_H4) totalBars = InpInitialLearningDays * 6;
    else if(_Period == PERIOD_D1) totalBars = InpInitialLearningDays;
    
    Print("准备分析 ", totalBars, " 根K线的历史数据");
    
    return AnalyzeHistoricalData(0, totalBars);
}

//+------------------------------------------------------------------+
//| 增量更新数据库                                                     |
//+------------------------------------------------------------------+
bool IncrementalUpdate(int days)
{
    int updateBars = days * 24;  // 假设H1图表
    if(_Period == PERIOD_M15) updateBars = days * 96;
    else if(_Period == PERIOD_M5) updateBars = days * 288;
    else if(_Period == PERIOD_H4) updateBars = days * 6;
    else if(_Period == PERIOD_D1) updateBars = days;
    
    Print("增量更新最近 ", days, " 天的数据 (", updateBars, " 根K线)");
    
    int originalSize = ArraySize(historyEvents);
    
    if(!AnalyzeHistoricalData(0, updateBars))
        return false;
        
    int newEvents = ArraySize(historyEvents) - originalSize;
    Print("增量更新完成，新增事件: ", newEvents, " 个");
    
    // 更新数据库头信息
    dbHeader.lastUpdate = TimeCurrent();
    dbHeader.totalEvents = ArraySize(historyEvents);
    
    return SaveDatabase();
}

//+------------------------------------------------------------------+
//| 分析历史数据                                                       |
//+------------------------------------------------------------------+
bool AnalyzeHistoricalData(int startBar, int totalBars)
{
    // 获取历史数据
    double high[], low[], close[];
    datetime time[];
    
    ArraySetAsSeries(high, true);
    ArraySetAsSeries(low, true);
    ArraySetAsSeries(close, true);
    ArraySetAsSeries(time, true);
    
    if(CopyHigh(_Symbol, PERIOD_CURRENT, startBar, totalBars, high) <= 0 ||
       CopyLow(_Symbol, PERIOD_CURRENT, startBar, totalBars, low) <= 0 ||
       CopyClose(_Symbol, PERIOD_CURRENT, startBar, totalBars, close) <= 0 ||
       CopyTime(_Symbol, PERIOD_CURRENT, startBar, totalBars, time) <= 0)
    {
        Print("历史数据获取失败");
        return false;
    }
    
    // 如果是增量更新，保持现有数据
    if(startBar == 0 && ArraySize(historyEvents) == 0)
    {
        ArrayResize(historyEvents, 0);
    }
    
    int eventsFound = 0;
    
    // 扫描历史数据，寻找突破事件
    for(int i = InpSRLookback + InpSuccessConfirmBars; i < totalBars - 1; i++)
    {
        // 计算当前位置的支撑阻力位
        double resistance = CalculateResistance(high, i, InpSRLookback);
        double support = CalculateSupport(low, i, InpSRLookback);
        
        double tolerancePips = InpSRTolerancePips * _Point;
        
        // 检查向上突破
        if(close[i] > resistance + tolerancePips && close[i+1] <= resistance)
        {
            // 检查是否是重复事件（时间太近）
            if(!IsDuplicateEvent(time[i], true))
            {
                BreakoutEvent event;
                event.time = time[i];
                event.level = resistance;
                event.isUpBreakout = true;
                event.wasSuccessful = CheckBreakoutSuccess(close, i, resistance, true);
                event.maintainBars = CountMaintainBars(close, i, resistance, true);
                event.volatility = CalculateVolatility(high, low, i, 14);
                
                AddEvent(event);
                eventsFound++;
            }
        }
        
        // 检查向下突破
        if(close[i] < support - tolerancePips && close[i+1] >= support)
        {
            // 检查是否是重复事件
            if(!IsDuplicateEvent(time[i], false))
            {
                BreakoutEvent event;
                event.time = time[i];
                event.level = support;
                event.isUpBreakout = false;
                event.wasSuccessful = CheckBreakoutSuccess(close, i, support, false);
                event.maintainBars = CountMaintainBars(close, i, support, false);
                event.volatility = CalculateVolatility(high, low, i, 14);
                
                AddEvent(event);
                eventsFound++;
            }
        }
        
        // 进度显示
        if(i % 100 == 0)
        {
            double progress = (double)i / totalBars * 100;
            Print("分析进度: ", (int)progress, "%, 发现事件: ", eventsFound);
        }
    }
    
    Print("历史数据分析完成，新发现事件: ", eventsFound, " 个");
    return true;
}

//+------------------------------------------------------------------+
//| 检查是否重复事件                                                   |
//+------------------------------------------------------------------+
bool IsDuplicateEvent(datetime eventTime, bool isUp)
{
    int timeWindow = 3600;  // 1小时内的事件视为重复
    
    for(int i = 0; i < ArraySize(historyEvents); i++)
    {
        if(historyEvents[i].isUpBreakout == isUp &&
           MathAbs((long)historyEvents[i].time - (long)eventTime) < timeWindow)
        {
            return true;
        }
    }
    return false;
}

//+------------------------------------------------------------------+
//| 添加事件到数组                                                     |
//+------------------------------------------------------------------+
void AddEvent(const BreakoutEvent &event)
{
    int size = ArraySize(historyEvents);
    ArrayResize(historyEvents, size + 1);
    historyEvents[size] = event;
}

//+------------------------------------------------------------------+
//| 计算波动率                                                         |
//+------------------------------------------------------------------+
double CalculateVolatility(const double &high[], const double &low[], int pos, int period)
{
    double sum = 0;
    for(int i = pos; i < pos + period && i < ArraySize(high); i++)
    {
        sum += high[i] - low[i];
    }
    return sum / period;
}

//+------------------------------------------------------------------+
//| 保存数据库                                                         |
//+------------------------------------------------------------------+
bool SaveDatabase()
{
    int handle = FileOpen(databaseFile, FILE_WRITE | FILE_BIN);
    if(handle == INVALID_HANDLE)
    {
        Print("无法创建数据库文件: ", databaseFile);
        return false;
    }
    
    // 更新数据库头信息
    dbHeader.version = 1;
    dbHeader.lastUpdate = TimeCurrent();
    dbHeader.totalEvents = ArraySize(historyEvents);
    dbHeader.symbol = _Symbol;
    dbHeader.timeframe = _Period;
    
    // 写入头信息
    FileWriteStruct(handle, dbHeader);
    
    // 写入事件数据
    for(int i = 0; i < ArraySize(historyEvents); i++)
    {
        FileWriteStruct(handle, historyEvents[i]);
    }
    
    FileClose(handle);
    Print("数据库保存成功，总事件数: ", ArraySize(historyEvents));
    return true;
}

//+------------------------------------------------------------------+
//| 加载数据库                                                         |
//+------------------------------------------------------------------+
bool LoadDatabase()
{
    if(!FileIsExist(databaseFile))
    {
        Print("数据库文件不存在: ", databaseFile);
        return false;
    }
    
    int handle = FileOpen(databaseFile, FILE_READ | FILE_BIN);
    if(handle == INVALID_HANDLE)
    {
        Print("无法打开数据库文件: ", databaseFile);
        return false;
    }
    
    // 读取头信息
    FileReadStruct(handle, dbHeader);
    
    // 验证数据库兼容性
    if(dbHeader.symbol != _Symbol || dbHeader.timeframe != _Period)
    {
        Print("数据库不兼容: 品种=", dbHeader.symbol, " 周期=", dbHeader.timeframe);
        FileClose(handle);
        return false;
    }
    
    // 读取事件数据
    ArrayResize(historyEvents, dbHeader.totalEvents);
    for(int i = 0; i < dbHeader.totalEvents; i++)
    {
        FileReadStruct(handle, historyEvents[i]);
    }
    
    FileClose(handle);
    Print("数据库加载成功，事件数: ", dbHeader.totalEvents);
    return true;
}

//+------------------------------------------------------------------+
//| 其他函数（与v2版本相同）                                            |
//+------------------------------------------------------------------+
double CalculateResistance(const double &high[], int startPos, int lookback)
{
    double maxHigh = 0;
    for(int i = startPos + 1; i <= startPos + lookback; i++)
    {
        if(high[i] > maxHigh)
            maxHigh = high[i];
    }
    return maxHigh;
}

double CalculateSupport(const double &low[], int startPos, int lookback)
{
    double minLow = 999999;
    for(int i = startPos + 1; i <= startPos + lookback; i++)
    {
        if(low[i] < minLow)
            minLow = low[i];
    }
    return minLow;
}

bool CheckBreakoutSuccess(const double &close[], int breakPos, double level, bool isUp)
{
    int successCount = 0;
    
    for(int i = breakPos - 1; i >= breakPos - InpSuccessConfirmBars && i >= 0; i--)
    {
        if((isUp && close[i] > level) || (!isUp && close[i] < level))
            successCount++;
    }
    
    return (successCount >= InpSuccessConfirmBars);
}

int CountMaintainBars(const double &close[], int breakPos, double level, bool isUp)
{
    int count = 0;
    
    for(int i = breakPos - 1; i >= 0; i--)
    {
        if((isUp && close[i] > level) || (!isUp && close[i] < level))
            count++;
        else
            break;
    }
    
    return count;
}

//+------------------------------------------------------------------+
//| 打印当前统计信息                                                   |
//+------------------------------------------------------------------+
void PrintCurrentStatistics()
{
    int totalEvents = ArraySize(historyEvents);
    int upBreakouts = 0, downBreakouts = 0;
    int upSuccess = 0, downSuccess = 0;
    
    for(int i = 0; i < totalEvents; i++)
    {
        if(historyEvents[i].isUpBreakout)
        {
            upBreakouts++;
            if(historyEvents[i].wasSuccessful)
                upSuccess++;
        }
        else
        {
            downBreakouts++;
            if(historyEvents[i].wasSuccessful)
                downSuccess++;
        }
    }
    
    Print("=== 当前数据库统计 ===");
    Print("总突破事件: ", totalEvents);
    Print("向上突破: ", upBreakouts, " 次，成功: ", upSuccess, " 次，胜率: ", 
          upBreakouts > 0 ? (double)upSuccess/upBreakouts*100 : 0, "%");
    Print("向下突破: ", downBreakouts, " 次，成功: ", downSuccess, " 次，胜率: ", 
          downBreakouts > 0 ? (double)downSuccess/downBreakouts*100 : 0, "%");
    Print("数据库最后更新: ", TimeToString(dbHeader.lastUpdate));
}

//+------------------------------------------------------------------+
//| 计算当前突破概率                                                   |
//+------------------------------------------------------------------+
double CalculateCurrentBreakoutProbability(bool isUpBreakout, double currentLevel)
{
    if(ArraySize(historyEvents) < InpMinSampleSize)
    {
        Print("历史样本不足，需要至少 ", InpMinSampleSize, " 个样本");
        return 0.0;
    }
    
    int relevantEvents = 0;
    int successfulEvents = 0;
    
    // 获取相关的历史事件
    for(int i = 0; i < ArraySize(historyEvents); i++)
    {
        if(historyEvents[i].isUpBreakout == isUpBreakout)
        {
            relevantEvents++;
            if(historyEvents[i].wasSuccessful)
                successfulEvents++;
        }
    }
    
    if(relevantEvents == 0)
        return 0.5;
        
    double probability = (double)successfulEvents / relevantEvents;
    
    Print("概率计算：相关样本=", relevantEvents, ", 成功=", successfulEvents, 
          ", 概率=", probability*100, "%");
    
    return probability;
}

//+------------------------------------------------------------------+
//| 主要交易逻辑（简化版，与之前类似）                                   |
//+------------------------------------------------------------------+
void OnTick()
{
    if(!IsNewBar() || !tradingEnabled)
        return;
        
    if(consecutiveLoss >= InpMaxConsecutiveLoss)
        return;
        
    if(PositionsTotal() > 0)
        return;
        
    // 定期更新数据库（每天检查一次）
    datetime currentTime = TimeCurrent();
    if(currentTime - lastDataUpdate > 86400)  // 24小时
    {
        lastDataUpdate = currentTime;
        if((currentTime - dbHeader.lastUpdate) > 86400 * InpIncrementalDays)
        {
            Print("执行自动数据库更新...");
            IncrementalUpdate(InpIncrementalDays);
        }
    }
    
    // 分析当前市场（简化版）
    AnalyzeCurrentMarket();
}

void AnalyzeCurrentMarket()
{
    // 与之前版本类似的市场分析逻辑
    // 这里简化处理，重点展示数据持久化功能
}

bool IsNewBar()
{
    static datetime lastBarTime = 0;
    datetime currentBarTime = iTime(_Symbol, PERIOD_CURRENT, 0);
    
    if(currentBarTime != lastBarTime)
    {
        lastBarTime = currentBarTime;
        return true;
    }
    return false;
}

void OnDeinit(const int reason)
{
    Print("概率突破系统 v3.0 停止，数据已保存");
} 