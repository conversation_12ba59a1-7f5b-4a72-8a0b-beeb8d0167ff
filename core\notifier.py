from typing import List, Optional, Dict
import logging
from wxpusher import WxPusher
from datetime import datetime
from .constants import NotificationConfig as NC
import time

logger = logging.getLogger(__name__)

class NotificationService:
    """通知服务 - 处理所有的消息推送"""
    
    def __init__(self, 
                 wx_token: str = NC.WXPUSHER_TOKEN, 
                 wx_uids: List[str] = NC.WXPUSHER_UIDS, 
                 topic_ids: Optional[List[str]] = NC.WXPUSHER_TOPICS):
        """初始化通知服务"""
        self.wx_config = {
            'token': wx_token,
            'uids': wx_uids,
            'topic_ids': topic_ids or []
        }
        
    def send_notification(self, 
                         symbol: str,
                         timeframe: str,
                         signal_type: str,
                         signal_strength: str,
                         conditions: List[str],
                         market_cap_tier: Optional[str] = None,
                         extra_info: Optional[dict] = None) -> bool:
        """发送交易信号通知"""
        max_retries = 3  # 最大重试次数
        retry_delay = 10  # 重试间隔(秒)
        
        for attempt in range(max_retries):
            try:
                # 格式化条件列表
                conditions_text = "\n".join(f"- {condition}" for condition in conditions)
                
                # 格式化市值层级
                market_cap = f"\n市值层级: {market_cap_tier}" if market_cap_tier else ""
                
                # 格式化技术分析信息
                analysis_info = ""
                if extra_info and 'extra_analysis' in extra_info:
                    analysis_info = f"\n\n技术分析:\n{extra_info['extra_analysis']}"
                    extra_info.pop('extra_analysis')
                
                # 格式化其他额外信息
                extra_info_text = ""
                if extra_info:
                    extra_info_text = "\n\n额外信息:" + "".join(
                        f"\n{key}: {value}" for key, value in extra_info.items()
                    )
                
                # 使用模板构建消息
                message = NC.TEMPLATES['signal'].format(
                    time=datetime.now().strftime(NC.DATETIME_FORMAT),
                    symbol=symbol,
                    timeframe=timeframe,
                    signal_type=signal_type,
                    signal_strength=signal_strength,
                    conditions=conditions_text,
                    market_cap=market_cap,
                    extra_info=extra_info_text + analysis_info
                )
                
                # 发送消息
                WxPusher.send_message(
                    content=message,
                    uids=self.wx_config['uids'],
                    topic_ids=self.wx_config['topic_ids'],
                    token=self.wx_config['token'],
                    url="https://wxpusher.zjiecode.com",  # 使用https
                    connect_timeout=15,  # 连接超时15秒
                    read_timeout=15      # 读取超时15秒
                )
                
                logger.info(f"成功发送 {symbol} {timeframe} 的交易信号通知")
                return True
                
            except Exception as e:
                if "Connection to wxpusher.zjiecode.com timed out" in str(e):
                    logger.error(f"发送通知超时(第{attempt + 1}次尝试): {str(e)}")
                else:
                    logger.error(f"发送通知失败(第{attempt + 1}次尝试): {str(e)}")
                    
                if attempt < max_retries - 1:
                    logger.info(f"等待 {retry_delay} 秒后重试...")
                    time.sleep(retry_delay)
                    continue
                else:
                    logger.error("达到最大重试次数,放弃发送通知")
                    return False

    def send_error_notification(self, error_message: str) -> bool:
        """发送错误通知"""
        try:
            message = NC.TEMPLATES['error'].format(
                time=datetime.now().strftime(NC.DATETIME_FORMAT),
                error_message=error_message
            )
            
            WxPusher.send_message(
                content=message,
                uids=self.wx_config['uids'],
                topic_ids=self.wx_config['topic_ids'],
                token=self.wx_config['token']
            )
            
            logger.info("成功发送错误通知")
            return True
            
        except Exception as e:
            logger.error(f"发送错误通知失败: {str(e)}")
            return False
            
    def send_system_notification(self, message: str) -> bool:
        """发送系统通知"""
        try:
            formatted_message = NC.TEMPLATES['system'].format(
                time=datetime.now().strftime(NC.DATETIME_FORMAT),
                message=message
            )
            
            WxPusher.send_message(
                content=formatted_message,
                uids=self.wx_config['uids'],
                topic_ids=self.wx_config['topic_ids'],
                token=self.wx_config['token']
            )
            
            logger.info("成功发送系统通知")
            return True
            
        except Exception as e:
            logger.error(f"发送系统通知失败: {str(e)}")
            return False 