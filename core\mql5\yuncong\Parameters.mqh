#ifndef PARAMETERS_MQH
#define PARAMETERS_MQH

// 波动点与趋势线段参数
input int      inp_LookbackPeriod = 100;    // 查找波动点的回看K线数量
input int      inp_SwingDefinitionBars = 3; // 定义波动点的左右K线数量(分形)
input double   inp_SwingAmplitudeMultiplier = 1.5; // 用于波动点幅度过滤的ATR乘数
input double   inp_MinSwingAmplitude = 0.0; // 波动点反向运动的最小绝对价格幅度 (0表示禁用)
input int      inp_MinBarsBetweenSwings = 5;  // 同类型高质量波动点之间的最小 K 线间隔
input double   inp_BreakToleranceAtrMult = 0.2; // 用于突破容差的ATR乘数

// --- 支撑阻力参数 ---
input double   inp_MergeAtrFactor = 0.3; // 合并 S/R 水平位的 ATR 因子
input double   inp_BreakoutAtrMargin = 0.3; // 突破 S/R 水平所需的最小 ATR 幅度

// --- 回归参数 (已不再使用) ---
// input int      inp_RegressionPeriod = 20;     // 用于 Theil-Sen 回归的 K 线数量
// input double   inp_MinRSquared = 0.0;         // 接受回归结果的最小 R 平方值 (0 表示禁用过滤)
// input ENUM_APPLIED_PRICE inp_RegressionPrice = PRICE_LOW; // 用于上升趋势线回归的价格 (PRICE_LOW)
// input ENUM_APPLIED_PRICE inp_RegressionPriceDown = PRICE_HIGH; // 用于下降趋势线回归的价格 (PRICE_HIGH)

// 确认信号参数
input int      inp_ConfirmationBars = 2;     // 突破后等待确认信号的最大K线数
input bool     inp_UseVolumeConfirm = true;  // 是否使用成交量放大作为确认条件?
input double   inp_MinVolumeIncreaseFactor = 1.5; // 成交量需超过近期均值的最小倍数
input int      inp_VolumeAvgPeriod = 20;     // 计算平均成交量的周期
input bool     inp_UseCandleConfirm = true;  // 是否使用强劲K线收盘作为确认条件?

// 风险与订单管理参数
input double   inp_RiskPercentPerTrade = 1.0; // 每单交易承担的风险占账户余额的百分比(0使用固定手数)
input double   inp_FixedLotSize = 0.01;     // 固定手数(当风险百分比为0时使用)
input double   inp_StopLossAtrMult = 1.0;   // 用于初始止损缓冲的ATR乘数
input double   inp_TakeProfitRRR = 3.0;     // 止盈的风险回报比(0表示不设置固定止盈)
input ulong    inp_MagicNumber = 13579;     // EA魔术数字
input int      inp_Slippage = 50;           // 允许的滑点(点数)

// 过滤条件
input double   inp_MinAtrThreshold = 0.0;    // 允许交易的最小ATR值(0表示禁用此过滤)

//--- 交易方向控制 ---
enum ENUM_TRADE_DIRECTION
{
   DIRECTION_BOTH,  // 双向交易
   DIRECTION_LONG,  // 只做多
   DIRECTION_SHORT  // 只做空
};
input ENUM_TRADE_DIRECTION inp_TradeDirection = DIRECTION_BOTH; // 交易方向选择

// --- 离场管理参数 ---
input bool     inp_UsePartialTP = true;     // 是否启用部分止盈
input double   inp_PartialTP_RRR = 1.5;     // 部分止盈的风险回报比 (TP1 = R * N)
input double   inp_PartialClosePercent = 0.5; // 部分止盈时平仓的百分比 (例如 0.5 = 50%)
input bool     inp_UseBreakEven = true;     // 是否启用移至保本
input double   inp_BreakEvenBufferPoints = 2; // 移动到保本时增加的点数缓冲 (点数)
input bool     inp_UseTrailingStop = true;  // 是否启用追踪止损
input double   inp_TrailingStopAtrMult = 2.5; // 追踪止损使用的ATR倍数

// --- 趋势线可视化参数 ---
input int      inp_MaxTrendlinesToKeep = 10; // 保留的最近趋势线数量 (每个方向)

#endif // PARAMETERS_MQH