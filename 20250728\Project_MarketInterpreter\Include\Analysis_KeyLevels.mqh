//+------------------------------------------------------------------+
//|                                            Analysis_KeyLevels.mqh |
//|                                         Copyright © 2025, htazq |
//|                                         https://github.com/htazq |
//+------------------------------------------------------------------+
#property copyright "Copyright © 2025, htazq"
#property link      "https://github.com/htazq"

#include "Defines.mqh"

//+------------------------------------------------------------------+
//| 关键价位分析命名空间                                               |
//+------------------------------------------------------------------+
namespace KeyLevels
{
    //+------------------------------------------------------------------+
    //| 私有变量                                                          |
    //+------------------------------------------------------------------+
    static datetime last_day_check = 0;
    static datetime last_week_check = 0;
    static datetime last_month_check = 0;
    
    // 关键价位数据结构
    struct KeyLevelData
    {
        double price;           // 价位
        datetime time;          // 时间
        string description;     // 描述
        color line_color;       // 线条颜色
        bool is_active;         // 是否激活
    };
    
    static KeyLevelData daily_open;
    static KeyLevelData prev_high;
    static KeyLevelData prev_low;
    static KeyLevelData weekly_open;
    static KeyLevelData monthly_open;
    
    //+------------------------------------------------------------------+
    //| 智能创建或更新水平线                                               |
    //+------------------------------------------------------------------+
    void CreateOrUpdateHLine(string name, double price, color clr, int width, ENUM_LINE_STYLE style = STYLE_SOLID, string description = "")
    {
        // 检查价格有效性
        if(price <= 0) return;
        
        // 检查对象是否存在
        if(ObjectFind(0, name) >= 0)
        {
            // 对象存在，检查价格是否需要更新
            double current_price = ObjectGetDouble(0, name, OBJPROP_PRICE, 0);
            if(MathAbs(current_price - price) > _Point * 0.1)
            {
                // 价格变了，更新它
                ObjectSetDouble(0, name, OBJPROP_PRICE, 0, price);
                DEBUG_PRINT(StringFormat("更新关键价位线 %s: %.5f -> %.5f", name, current_price, price));
            }
        }
        else
        {
            // 对象不存在，创建新的
            if(ObjectCreate(0, name, OBJ_HLINE, 0, 0, price))
            {
                ObjectSetInteger(0, name, OBJPROP_COLOR, clr);
                ObjectSetInteger(0, name, OBJPROP_WIDTH, width);
                ObjectSetInteger(0, name, OBJPROP_STYLE, style);
                ObjectSetInteger(0, name, OBJPROP_BACK, false);
                ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
                ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
                
                // 设置描述文本
                if(description != "")
                {
                    ObjectSetString(0, name, OBJPROP_TEXT, description);
                }
                
                DEBUG_PRINT(StringFormat("创建关键价位线 %s: %.5f", name, price));
            }
        }
    }
    
    //+------------------------------------------------------------------+
    //| 更新日线关键价位                                                   |
    //+------------------------------------------------------------------+
    void UpdateDailyLevels()
    {
        // 获取今日开盘价
        double today_open = iOpen(_Symbol, PERIOD_D1, 0);
        if(today_open > 0)
        {
            daily_open.price = today_open;
            daily_open.time = iTime(_Symbol, PERIOD_D1, 0);
            daily_open.description = "今日开盘: " + FORMAT_PRICE(today_open);
            daily_open.is_active = true;
        }
        
        // 获取前一日最高价
        double yesterday_high = iHigh(_Symbol, PERIOD_D1, 1);
        if(yesterday_high > 0)
        {
            prev_high.price = yesterday_high;
            prev_high.time = iTime(_Symbol, PERIOD_D1, 1);
            prev_high.description = "昨日最高: " + FORMAT_PRICE(yesterday_high);
            prev_high.is_active = true;
        }
        
        // 获取前一日最低价
        double yesterday_low = iLow(_Symbol, PERIOD_D1, 1);
        if(yesterday_low > 0)
        {
            prev_low.price = yesterday_low;
            prev_low.time = iTime(_Symbol, PERIOD_D1, 1);
            prev_low.description = "昨日最低: " + FORMAT_PRICE(yesterday_low);
            prev_low.is_active = true;
        }
    }
    
    //+------------------------------------------------------------------+
    //| 更新周线关键价位                                                   |
    //+------------------------------------------------------------------+
    void UpdateWeeklyLevels()
    {
        // 获取本周开盘价
        double week_open = iOpen(_Symbol, PERIOD_W1, 0);
        if(week_open > 0)
        {
            weekly_open.price = week_open;
            weekly_open.time = iTime(_Symbol, PERIOD_W1, 0);
            weekly_open.description = "本周开盘: " + FORMAT_PRICE(week_open);
            weekly_open.is_active = true;
        }
    }
    
    //+------------------------------------------------------------------+
    //| 更新月线关键价位                                                   |
    //+------------------------------------------------------------------+
    void UpdateMonthlyLevels()
    {
        // 获取本月开盘价
        double month_open = iOpen(_Symbol, PERIOD_MN1, 0);
        if(month_open > 0)
        {
            monthly_open.price = month_open;
            monthly_open.time = iTime(_Symbol, PERIOD_MN1, 0);
            monthly_open.description = "本月开盘: " + FORMAT_PRICE(month_open);
            monthly_open.is_active = true;
        }
    }
    
    //+------------------------------------------------------------------+
    //| 主更新函数                                                         |
    //+------------------------------------------------------------------+
    void Update(bool show_daily_open = true, color daily_open_color = clrYellow,
                bool show_prev_high = true, color prev_high_color = clrRed,
                bool show_prev_low = true, color prev_low_color = clrLime,
                bool show_weekly_open = false, color weekly_open_color = clrOrange,
                bool show_monthly_open = false, color monthly_open_color = clrMagenta,
                int line_width = 1)
    {
        // 检查是否需要更新日线数据
        datetime current_day_start = iTime(_Symbol, PERIOD_D1, 0);
        if(current_day_start != last_day_check)
        {
            UpdateDailyLevels();
            last_day_check = current_day_start;
        }
        
        // 检查是否需要更新周线数据
        datetime current_week_start = iTime(_Symbol, PERIOD_W1, 0);
        if(current_week_start != last_week_check)
        {
            UpdateWeeklyLevels();
            last_week_check = current_week_start;
        }
        
        // 检查是否需要更新月线数据
        datetime current_month_start = iTime(_Symbol, PERIOD_MN1, 0);
        if(current_month_start != last_month_check)
        {
            UpdateMonthlyLevels();
            last_month_check = current_month_start;
        }
        
        // 绘制关键价位线
        if(show_daily_open && daily_open.is_active)
        {
            CreateOrUpdateHLine(OBJ_DAILY_OPEN, daily_open.price, daily_open_color, line_width, STYLE_SOLID, daily_open.description);
        }
        
        if(show_prev_high && prev_high.is_active)
        {
            CreateOrUpdateHLine(OBJ_PREV_HIGH, prev_high.price, prev_high_color, line_width, STYLE_SOLID, prev_high.description);
        }
        
        if(show_prev_low && prev_low.is_active)
        {
            CreateOrUpdateHLine(OBJ_PREV_LOW, prev_low.price, prev_low_color, line_width, STYLE_SOLID, prev_low.description);
        }
        
        if(show_weekly_open && weekly_open.is_active)
        {
            CreateOrUpdateHLine(OBJ_WEEKLY_OPEN, weekly_open.price, weekly_open_color, line_width, STYLE_DOT, weekly_open.description);
        }
        
        if(show_monthly_open && monthly_open.is_active)
        {
            CreateOrUpdateHLine(OBJ_MONTHLY_OPEN, monthly_open.price, monthly_open_color, line_width, STYLE_DASH, monthly_open.description);
        }
    }
    
    //+------------------------------------------------------------------+
    //| 获取关键价位数据                                                   |
    //+------------------------------------------------------------------+
    double GetDailyOpen() { return daily_open.price; }
    double GetPrevHigh() { return prev_high.price; }
    double GetPrevLow() { return prev_low.price; }
    double GetWeeklyOpen() { return weekly_open.price; }
    double GetMonthlyOpen() { return monthly_open.price; }
    
    //+------------------------------------------------------------------+
    //| 获取价格距离关键价位的点数                                          |
    //+------------------------------------------------------------------+
    int GetDistanceToLevel(double current_price, double level_price)
    {
        if(level_price <= 0) return 0;
        return (int)MathRound((current_price - level_price) / _Point);
    }
    
    //+------------------------------------------------------------------+
    //| 检查价格是否接近关键价位                                           |
    //+------------------------------------------------------------------+
    bool IsNearKeyLevel(double current_price, double threshold_points = 50)
    {
        double threshold = threshold_points * _Point;
        
        // 检查是否接近任何关键价位
        if(daily_open.is_active && MathAbs(current_price - daily_open.price) <= threshold) return true;
        if(prev_high.is_active && MathAbs(current_price - prev_high.price) <= threshold) return true;
        if(prev_low.is_active && MathAbs(current_price - prev_low.price) <= threshold) return true;
        if(weekly_open.is_active && MathAbs(current_price - weekly_open.price) <= threshold) return true;
        if(monthly_open.is_active && MathAbs(current_price - monthly_open.price) <= threshold) return true;
        
        return false;
    }
    
    //+------------------------------------------------------------------+
    //| 获取最近的关键价位                                                 |
    //+------------------------------------------------------------------+
    double GetNearestKeyLevel(double current_price)
    {
        double nearest_level = 0;
        double min_distance = DBL_MAX;
        
        // 检查所有激活的关键价位
        if(daily_open.is_active)
        {
            double distance = MathAbs(current_price - daily_open.price);
            if(distance < min_distance)
            {
                min_distance = distance;
                nearest_level = daily_open.price;
            }
        }
        
        if(prev_high.is_active)
        {
            double distance = MathAbs(current_price - prev_high.price);
            if(distance < min_distance)
            {
                min_distance = distance;
                nearest_level = prev_high.price;
            }
        }
        
        if(prev_low.is_active)
        {
            double distance = MathAbs(current_price - prev_low.price);
            if(distance < min_distance)
            {
                min_distance = distance;
                nearest_level = prev_low.price;
            }
        }
        
        if(weekly_open.is_active)
        {
            double distance = MathAbs(current_price - weekly_open.price);
            if(distance < min_distance)
            {
                min_distance = distance;
                nearest_level = weekly_open.price;
            }
        }
        
        if(monthly_open.is_active)
        {
            double distance = MathAbs(current_price - monthly_open.price);
            if(distance < min_distance)
            {
                min_distance = distance;
                nearest_level = monthly_open.price;
            }
        }
        
        return nearest_level;
    }
    
    //+------------------------------------------------------------------+
    //| 初始化关键价位分析模块                                             |
    //+------------------------------------------------------------------+
    void Initialize()
    {
        // 初始化数据结构
        daily_open.price = 0;
        daily_open.is_active = false;
        prev_high.price = 0;
        prev_high.is_active = false;
        prev_low.price = 0;
        prev_low.is_active = false;
        weekly_open.price = 0;
        weekly_open.is_active = false;
        monthly_open.price = 0;
        monthly_open.is_active = false;
        
        // 重置检查时间
        last_day_check = 0;
        last_week_check = 0;
        last_month_check = 0;
        
        DEBUG_PRINT("关键价位分析模块初始化完成");
    }
    
    //+------------------------------------------------------------------+
    //| 清理关键价位分析模块                                               |
    //+------------------------------------------------------------------+
    void Deinitialize()
    {
        // 删除所有关键价位线对象
        ObjectDelete(0, OBJ_DAILY_OPEN);
        ObjectDelete(0, OBJ_PREV_HIGH);
        ObjectDelete(0, OBJ_PREV_LOW);
        ObjectDelete(0, OBJ_WEEKLY_OPEN);
        ObjectDelete(0, OBJ_MONTHLY_OPEN);
        
        DEBUG_PRINT("关键价位分析模块已清理");
    }
}