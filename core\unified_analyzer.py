from typing import Dict, List, Tuple, Optional
import pandas as pd
import logging
from core.pattern_analyzer import PatternAnalyzer as LongPatternAnalyzer
from core.short_pattern_analyzer import PatternAnalyzer as ShortPatternAnalyzer
from utils.time_utils import timeit

logger = logging.getLogger(__name__)

class UnifiedPatternAnalyzer:
    """统一的技术形态分析器，同时支持多空分析"""
    
    def __init__(self, direction: str = "both"):
        """
        初始化统一分析器
        
        参数:
            direction: 分析方向，可选值为 "long"、"short" 或 "both"
        """
        self.direction = direction.lower()
        self.long_analyzer = LongPatternAnalyzer() if direction in ["long", "both"] else None
        self.short_analyzer = ShortPatternAnalyzer() if direction in ["short", "both"] else None
    @timeit
    def detect_trading_signals(self, data: pd.DataFrame) -> List[Dict]:
        """检测交易信号"""
        signals = []
        
        # 分析做多信号
        if self.long_analyzer:
            logger.debug("开始分析做多信号...")
            long_signals = self.long_analyzer.detect_trading_signals(data)
            if long_signals:
                logger.debug(f"发现 {len(long_signals)} 个做多信号")
                for signal in long_signals:
                    signal['direction'] = 'LONG'
                    signals.append(signal)
        
        # 分析做空信号
        if self.short_analyzer:
            logger.debug("开始分析做空信号...")
            short_signals = self.short_analyzer.detect_trading_signals(data)
            if short_signals:
                logger.debug(f"发现 {len(short_signals)} 个做空信号")
                for signal in short_signals:
                    signal['direction'] = 'SHORT'
                    signals.append(signal)
        
        logger.debug(f"总共发现 {len(signals)} 个信号")
        return signals
    
    def analyze_specific_pattern(self, data: pd.DataFrame, pattern_name: str, direction: str = None) -> Tuple[bool, str]:
        """
        分析特定形态
        
        参数:
            data: OHLCV数据
            pattern_name: 形态名称
            direction: 可选的方向覆盖，如果不提供则使用初始化时的方向
            
        返回:
            (是否形成有效形态, 详细信息)
        """
        direction = direction or self.direction
        
        if direction == "long" or direction == "both":
            # 尝试从做多分析器获取方法
            try:
                method = getattr(self.long_analyzer, f"analyze_{pattern_name}")
                return method(data)
            except (AttributeError, TypeError):
                if direction == "long":
                    logger.error(f"做多分析器中找不到形态: {pattern_name}")
                    return False, f"做多分析器中找不到形态: {pattern_name}"
        
        if direction == "short" or direction == "both":
            # 尝试从做空分析器获取方法
            try:
                method = getattr(self.short_analyzer, f"analyze_{pattern_name}")
                return method(data)
            except (AttributeError, TypeError):
                logger.error(f"做空分析器中找不到形态: {pattern_name}")
                return False, f"做空分析器中找不到形态: {pattern_name}"
        
        return False, "无效的分析方向"
    
    def analyze_all_patterns(self, data: pd.DataFrame, direction: str = None) -> Dict[str, Dict[str, Tuple[bool, str]]]:
        """
        分析所有技术形态
        
        参数:
            data: OHLCV数据
            direction: 可选的方向覆盖，如果不提供则使用初始化时的方向
            
        返回:
            包含多空分析结果的字典
        """
        direction = direction or self.direction
        results = {}
        
        if direction in ["long", "both"] and self.long_analyzer:
            results["long"] = self.long_analyzer.analyze_all_patterns(data)
            
        if direction in ["short", "both"] and self.short_analyzer:
            results["short"] = self.short_analyzer.analyze_all_patterns(data)
            
        return results
    
    def get_support_resistance(self, data: pd.DataFrame) -> Dict[str, Tuple[bool, str]]:
        """
        获取支撑阻力分析结果
        
        参数:
            data: OHLCV数据
            
        返回:
            包含多空支撑阻力分析结果的字典
        """
        results = {}
        
        if self.long_analyzer:
            results["long"] = self.long_analyzer.analyze_support_resistance(data)
            
        if self.short_analyzer:
            results["short"] = self.short_analyzer.analyze_support_resistance(data)
            
        return results
    
    def get_adam_theory(self, data: pd.DataFrame) -> Dict[str, Tuple[bool, str]]:
        """
        获取亚当理论分析结果
        
        参数:
            data: OHLCV数据
            
        返回:
            包含多空亚当理论分析结果的字典
        """
        results = {}
        
        if self.long_analyzer:
            results["long"] = self.long_analyzer.analyze_adam_theory(data)
            
        if self.short_analyzer:
            results["short"] = self.short_analyzer.analyze_adam_theory(data)
            
        return results