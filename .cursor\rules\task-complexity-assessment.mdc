---
description: 
globs: 
alwaysApply: false
---
# 任务复杂度评估与工作模式

## 强制执行流程
1. **自动任务分析**: 基于需求、文件数量和技术复杂度评估复杂度
2. **向用户声明判断**: 清楚说明复杂度级别和对应工作模式
3. **获得用户确认**: 用户可以接受、调整或要求更严格的验证
4. **按确认级别执行**: 严格按照确认的验证级别进行后续工作

## 简单任务
**特征**:
- 单文件或少数文件修改
- 语法修复、样式调整、简单逻辑更改
- 使用项目中现有的成熟模式
- 不涉及新技术引入或架构变更

**工作模式**:
- Context7验证: 仅对新技术或不确定部分
- 交互频率: 任务完成后统一反馈
- 错误处理: 尝试自动修复常见错误，失败则详细报告

**AI声明示例**:
```
"我将此任务评估为'简单任务'(单文件样式修改)，将采用高效模式：
- 仅对不确定部分进行Context7验证  
- 完成后统一反馈结果
- 常见错误会尝试自动修复
是否同意此工作模式？如需更严格验证，请告知。"
```

## 标准任务  
**特征**:
- 多文件功能开发或重构
- 新功能模块实现
- 涉及多个组件的协调
- 使用相对熟悉的技术栈

**工作模式**:
- Context7验证: 核心API使用和重要技术决策
- 交互频率: 主要阶段完成后请求反馈
- 错误处理: 详细分析后提供修复方案，等待确认

## 复杂任务
**特征**:
- 架构级别的设计或重构
- 引入新的技术栈或框架
- 影响多个模块的大规模变更  
- 具有高技术风险或不确定性

**工作模式**:
- Context7验证: 所有关键决策点和新技术引入
- 交互频率: 每个重要步骤后都请求反馈
- 错误处理: 遇到问题立即停止并请求指导

## 用户控制机制
- **级别调整**: 用户可随时调整验证级别或交互频率
- **模式切换**: 用户可临时要求更严格或更高效的工作模式
- **强制验证**: 用户可随时要求对任何技术点进行Context7验证
- **方向调整**: 用户可随时调整任务方向或重新评估复杂度

