import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from typing import Dict, List
import pandas as pd
import logging
from datetime import datetime
from core.pattern_analyzer import Pat<PERSON><PERSON><PERSON>yzer
from core.notifier import NotificationService
from platforms.efinance.efinance_data import EFinanceDataFetcher
from utils.log_utils import setup_logger


# 设置日志
logger = setup_logger(
    'efinance_analyzer', 
    os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), 'logs', 'efinance', 'analyzer.log'),
    level=logging.INFO
)
class EFinanceMarketAnalyzer:
    """国内期货市场分析器"""
    
    def __init__(self, custom_symbols: List[str] = None):
        self.data_fetcher = EFinanceDataFetcher()
        self.pattern_analyzer = PatternAnalyzer()
        self.notifier = NotificationService()
        # 使用自定义品种或默认品种
        self.analysis_symbols = custom_symbols if custom_symbols else ['jdm']

    def analyze_market(self, **kwargs) -> List[Dict]:
        """市场分析主函数"""
        all_signals = []
        try:
            notify = kwargs.get('notify', True)
            limit = kwargs.get('limit', 200)  # 默认获取200根K线
            
            # 遍历分析品种
            for symbol in self.analysis_symbols:
                # 遍历所有时间周期
                for timeframe in self.data_fetcher.timeframes:
                    try:
                        # 获取K线数据
                        data = self.data_fetcher.get_klines(
                            symbol=symbol,
                            timeframe=timeframe,
                            start_date=kwargs.get('start_date'),
                            end_date=kwargs.get('end_date'),
                            limit=limit  # 添加limit参数
                        )
                        
                        if data is None:
                            continue
                            
                        # 使用核心分析器分析
                        signals = self.pattern_analyzer.detect_trading_signals(data)
                        
                        if signals:
                            for signal in signals:
                                # 添加额外信息
                                signal.update({
                                    'symbol': symbol,
                                    'timeframe': timeframe
                                })
                                # 将信号添加到all_signals
                                all_signals.append(signal)
                                
                                extra_info = {
                                    'platform': 'EFinance',
                                    'details': signal['details']
                                }
                                
                                # 从signal中获取extra_analysis
                                if 'extra_analysis' in signal:
                                    extra_info['extra_analysis'] = signal['extra_analysis']
                                
                                if notify:
                                    self.notifier.send_notification(
                                        symbol=symbol,
                                        timeframe=timeframe,
                                        signal_type=signal['type'],
                                        signal_strength=signal['strength'],
                                        conditions=signal['satisfied_patterns'],
                                        extra_info=extra_info
                                    )
                                    
                    except Exception as e:
                        logger.error(f"分析 {symbol} {timeframe} 时出错: {str(e)}")
                        continue
                        
            return all_signals
            
        except Exception as e:
            logger.error(f"市场分析过程出错: {str(e)}")
            return all_signals


def run_analysis(**kwargs):
    """独立运行函数"""
    
    analyzer = EFinanceMarketAnalyzer()
    return analyzer.analyze_market(**kwargs)


if __name__ == "__main__":
    # 使用limit参数获取最近200根K线
    signals = run_analysis(
        limit=200,  # 获取最近200根K线
        notify=True
    )
    
    # 或者使用日期范围
    # signals = run_analysis(
    #     start_date='2023-01-01',
    #     end_date='2024-03-19',
    #     notify=True
    # )
    
    # 打印分析结果
    print(f"\n发现 {len(signals)} 个信号:")
    for signal in signals:
        print(f"\n{signal['symbol']} {signal['timeframe']} - {signal['type']}") 