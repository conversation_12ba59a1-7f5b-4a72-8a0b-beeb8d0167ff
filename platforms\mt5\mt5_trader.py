import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

import MetaTrader5 as mt5
import pandas as pd
import logging
from datetime import datetime, timedelta
import time
from typing import Dict, List, Optional, Sized
from platforms.mt5.mt5_unified_analyzer import MT5UnifiedMarketAnalyzer
from platforms.mt5.mt5_data import MT5DataFetcher
from utils.time_utils import timeit
# 导入新的持仓管理模块
from platforms.mt5.mt5_position_manager import MT5PositionManager
from platforms.mt5.mt5_config import (
    TIMEFRAME_MAP, 
    TRADING_CONFIG, 
    SIGNAL_CONFIG, 
    SYMBOLS, 
    TIMEFRAME_STRINGS,
    TIMEFRAME_CHECK_MAP,
    TIMEFRAME_PRIORITY,
    TIMEFRAME_COMMENT_MAP,
    TIMEFRAMES,
    POSITION_MANAGEMENT_CONFIG,
    MT5_PATH
)
from utils.log_utils import setup_logger

# 设置日志
logger = setup_logger(
    'mt5_trader', 
    os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), 'logs', 'mt5', 'trader.log'),
    level=logging.INFO
)

class MT5Trader:
    """MT5交易执行器"""
    def __init__(self):
        """初始化交易系统"""
        try:
            # 初始化MT5连接，指定路径
            if not mt5.initialize(path=MT5_PATH):
                logger.error(f"MT5初始化失败，路径: {MT5_PATH}")
                raise Exception(f"MT5初始化失败，路径: {MT5_PATH}")
                
            # 初始化日志
            self.logger = setup_logger('mt5_trader', 'logs/mt5_trader.log')
            
            # 从配置文件加载交易品种
            self.trading_symbols = SYMBOLS.get("default", [])
            self.symbols_config = TRADING_CONFIG.get("symbols", {})
            
            # 初始化品种信息（从MT5获取pip值等）
            from platforms.mt5.mt5_config import initialize_symbol_info
            initialize_symbol_info()
            
            # 初始化市场分析器
            self.analyzer = MT5UnifiedMarketAnalyzer(custom_symbols=list(self.symbols_config.keys()))
            
            # 初始化持仓管理器
            self.position_manager = MT5PositionManager()
            logger.info("持仓管理器初始化成功")

            # 初始化交易方向
            self.trading_directions = {}
            
            # 初始化时间周期映射
            self.timeframe_map = TIMEFRAME_MAP
            
            # 初始化信号配置
            self.signal_config = SIGNAL_CONFIG
            
            # 从TRADING_CONFIG加载最大持仓数量和ATR周期
            self.max_positions = TRADING_CONFIG["max_positions"]
            self.atr_period = TRADING_CONFIG["atr_period"]

            # 验证配置
            if not self.trading_symbols:
                raise ValueError("未配置交易品种")
                
            if not self.symbols_config:
                raise ValueError("未配置品种参数")
                
            # 验证每个品种的配置
            for symbol in self.trading_symbols:
                if symbol not in self.symbols_config:
                    raise ValueError(f"品种 {symbol} 缺少配置")
                    
                # 验证每个时间周期的配置
                for timeframe in TIMEFRAMES:
                    if timeframe not in self.symbols_config[symbol].get("timeframes", {}):
                        raise ValueError(f"品种 {symbol} 缺少时间周期 {timeframe} 的配置")
                        
                    # 验证固定手数配置
                    lot_size = self.symbols_config[symbol]["timeframes"][timeframe].get("lot_size")
                    if lot_size is None:
                        raise ValueError(f"品种 {symbol} 时间周期 {timeframe} 缺少固定手数配置")
                    
                    # 验证手数是否为有效值
                    if not isinstance(lot_size, (int, float)) or lot_size <= 0:
                        raise ValueError(f"品种 {symbol} 时间周期 {timeframe} 的固定手数 {lot_size} 无效")
            
            logger.info("MT5交易系统初始化完成")
            logger.info(f"交易品种: {self.trading_symbols}")
            logger.info(f"最大持仓数量: {self.max_positions}")
            logger.info(f"ATR周期: {self.atr_period}")
            
        except Exception as e:
            logger.error(f"MT5交易系统初始化失败: {str(e)}", exc_info=True)
            raise

    def get_market_info(self, symbol: str) -> dict:
        """获取市场信息"""
        try:
            # 检查MT5连接状态
            if not mt5.terminal_info():
                logger.error(f"获取{symbol}市场信息失败: MT5连接已断开")
                return None

            # 确保品种已选择
            if not mt5.symbol_select(symbol, True):
                logger.error(f"品种选择失败: {symbol}, 错误: {mt5.last_error()}")
                return None
                
            tick = mt5.symbol_info_tick(symbol)
            symbol_info = mt5.symbol_info(symbol)
            
            if tick is None:
                logger.error(f"获取{symbol}报价失败: {mt5.last_error()}")
                return None
                
            if symbol_info is None:
                logger.error(f"获取{symbol}信息失败: {mt5.last_error()}")
                return None
                
            market_info = {
                'bid': tick.bid,
                'ask': tick.ask,
                'point': symbol_info.point,
                'digits': symbol_info.digits,
                'volume_step': symbol_info.volume_step,
                'spread': tick.ask - tick.bid,
                'time': tick.time
            }
            
            # 检查数据有效性
            if market_info['ask'] <= 0 or market_info['bid'] <= 0:
                logger.error(f"{symbol}报价异常: bid={market_info['bid']}, ask={market_info['ask']}")
                return None
                
            # 检查点差是否合理
            if market_info['spread'] > symbol_info.spread * 10:
                logger.warning(f"{symbol}点差异常: {market_info['spread']} > {symbol_info.spread * 10}")
                
            return market_info
            
        except Exception as e:
            logger.error(f"获取{symbol}市场信息出错: {str(e)}", exc_info=True)
            return None

    def get_position_count(self, symbol: str, timeframe: int, direction: str = None) -> int:
        """获取某个品种某个周期某个方向的持仓数量"""
        try:
            positions = mt5.positions_get(symbol=symbol)
            if positions is None:
                return 0
            
            timeframe_str = self._get_timeframe_str(timeframe)
            timeframe_positions = []
            position_tickets = set()  # 用于去重
            
            for pos in positions:
                # 如果已经处理过该持仓，则跳过
                if pos.ticket in position_tickets:
                    continue
                position_tickets.add(pos.ticket)
                
                # 如果指定了方向，则只统计该方向的持仓
                if direction is not None:
                    if direction == 'LONG' and pos.type != mt5.POSITION_TYPE_BUY:
                        continue
                    if direction == 'SHORT' and pos.type != mt5.POSITION_TYPE_SELL:
                        continue
                
                # 1. 处理新格式 (例如: B151A3.5)
                if len(pos.comment) >= 4 and pos.comment[0] in ['A', 'B', 'C', 'D']:
                    # 检查周期
                    check_patterns = TIMEFRAME_CHECK_MAP.get(timeframe_str, [])
                    if any(pattern in pos.comment for pattern in check_patterns):
                        timeframe_positions.append(pos)
            
            # 打印日志以便调试（简化版本，避免重复）
            if timeframe_positions:
                direction_str = f"({direction}方向)" if direction else ""
                logger.info(f"{symbol} {timeframe_str}周期{direction_str}当前持仓: {len(timeframe_positions)}笔")
                
                # 只打印一次持仓详情，避免重复
                for pos in timeframe_positions:
                    direction_text = '多' if pos.type == mt5.POSITION_TYPE_BUY else '空'
                    logger.info(f"持仓详情 - Ticket: {pos.ticket}, Comment: {pos.comment}, 方向: {direction_text}")
            
            return len(timeframe_positions)
            
        except Exception as e:
            logger.error(f"获取持仓数量出错: {str(e)}")
            return 0

    def can_add_position(self, symbol, timeframe, signal_type=None, direction=None):
        """检查是否可以添加持仓（允许多空共存）"""
        try:
            # 获取当前持仓
            positions = mt5.positions_get(symbol=symbol)
            if positions is None:
                positions = []
            
            # 检查该品种该周期该方向的持仓数量
            current_positions = 0
            for pos in positions:
                if pos.symbol == symbol:
                    # 从注释中提取周期
                    # 修正：使用正确的方法名
                    pos_timeframe = self._get_timeframe_from_comment(pos.comment)
                    if pos_timeframe == timeframe:
                        # 检查方向
                        if (direction == 'LONG' and pos.type == mt5.POSITION_TYPE_BUY) or \
                        (direction == 'SHORT' and pos.type == mt5.POSITION_TYPE_SELL):
                            current_positions += 1
            
            # 检查是否超过最大持仓数量
            if current_positions >= self.max_positions:
                logger.info(f"{symbol} {timeframe} {direction}方向已达到最大持仓数量: {current_positions}")
                return False
            
            # 允许添加持仓
            return True
                
        except Exception as e:
            logger.error(f"检查是否可以添加持仓出错: {str(e)}", exc_info=True)
            return False

    def _get_timeframe_from_comment(self, comment):
        """从注释中提取时间周期"""
        if not comment:
            return None
            
        # 遍历时间周期检查映射
        for tf_str, patterns in TIMEFRAME_CHECK_MAP.items():
            for pattern in patterns:
                if pattern in comment:
                    # 找到匹配的时间周期字符串，转换为MT5时间周期
                    tf_value = TIMEFRAME_COMMENT_MAP.get(tf_str)
                    if tf_value:
                        return TIMEFRAME_MAP.get(tf_value)
        
        return None

    def calculate_atr(self, symbol: str, timeframe: int, signal_type: str = 'default') -> float:
        """计算ATR并根据信号类型调整"""
        try:
            # 映射分析周期到MT5周期
            mt5_timeframe = self.timeframe_map.get(timeframe)
            if mt5_timeframe is None:
                logger.error(f"计算ATR失败: 未知的分析周期 {timeframe}")
                return None
                
            # 直接从MT5获取数据
            rates = mt5.copy_rates_from_pos(symbol, mt5_timeframe, 0, self.atr_period + 1)
            if rates is None:
                logger.error(f"计算ATR失败: 无法获取{symbol} {mt5_timeframe}数据")
                return None
                
            # 转换为DataFrame
            df = pd.DataFrame(rates)
            
            # 计算ATR
            df['high_low'] = df['high'] - df['low']
            df['high_close'] = abs(df['high'] - df['close'].shift(1))
            df['low_close'] = abs(df['low'] - df['close'].shift(1))
            df['tr'] = df[['high_low', 'high_close', 'low_close']].max(axis=1)
            df['atr'] = df['tr'].rolling(self.atr_period).mean()
            
            if df['atr'].isnull().all():
                logger.error(f"计算ATR失败: {symbol} {mt5_timeframe} ATR计算结果全为空")
                return None
                
            base_atr = df['atr'].iloc[-1]
            if pd.isnull(base_atr):
                logger.error(f"计算ATR失败: {symbol} {mt5_timeframe} 最新ATR值为空")
                return None
            
            # 根据信号类型调整ATR
            signal_params = self.signal_config.get(signal_type, self.signal_config['default'])
            return base_atr * signal_params['initial_stop']                
        except Exception as e:
            logger.error(f"计算ATR出错: {str(e)}", exc_info=True)
            return None

    def open_position(self, symbol, timeframe, direction, signal_type='B', signal_strength='MEDIUM'):
        """开仓"""
        try:
            # 检查参数
            if not isinstance(symbol, str) or not symbol:
                logger.error(f"无效的品种: {symbol}")
                return False
                
            if not isinstance(direction, str) or direction not in ['LONG', 'SHORT']:
                logger.error(f"无效的交易方向: {direction}")
                return False
                
            # 获取当前价格
            tick = mt5.symbol_info_tick(symbol)
            if tick is None:
                logger.error(f"无法获取{symbol}价格")
                return False
                
            # 根据方向确定价格
            price = tick.ask if direction == 'LONG' else tick.bid
            
            # 计算ATR值
            atr_value = self.calculate_atr(symbol, timeframe)
            if atr_value is None:
                logger.error(f"无法计算{symbol} {timeframe}的ATR值")
                return False
                
            # 获取手数
            lot_size = self.get_lot_size(symbol, timeframe)
            if lot_size is None:
                logger.error(f"无法获取{symbol} {timeframe}的手数")
                return False
                
            logger.info(f"使用固定手数: {symbol}, 周期: {TIMEFRAME_STRINGS.get(timeframe, str(timeframe))}, 手数: {lot_size}")
            
            # 获取风险因子 - 从品种和周期特定配置
            config_risk_factor = self.get_risk_factor(symbol, timeframe)
            
            # 获取信号类型的initial_stop值
            signal_initial_stop = self.signal_config.get(signal_type, self.signal_config['default'])['initial_stop']
            
            # 获取周期调整系数
            from platforms.mt5.mt5_config import adjust_atr_multiplier_by_timeframe
            timeframe_factor = adjust_atr_multiplier_by_timeframe(1.0, timeframe)
            
            # 综合计算风险因子
            combined_risk_factor = config_risk_factor * signal_initial_stop * timeframe_factor
            logger.info(f"止损计算 - 基础风险因子: {config_risk_factor}, 信号初始止损: {signal_initial_stop}, 周期调整: {timeframe_factor}, 综合: {combined_risk_factor}")
            
            # 计算止损价格 - 基于ATR和综合风险因子
            sl_price_atr = None
            if direction == 'LONG':
                sl_price_atr = price - (atr_value * combined_risk_factor)
            else:
                sl_price_atr = price + (atr_value * combined_risk_factor)
                
            # 使用持仓管理器计算止损价格 - 传递综合风险因子
            sl_price_risk = self.position_manager.adjust_stop_loss(
                symbol, 
                direction,
                price,
                atr_value,
                combined_risk_factor  # 使用综合风险因子
            )
            
            # 如果任一止损计算失败，使用另一个
            if sl_price_atr is None:
                sl_price = sl_price_risk
            elif sl_price_risk is None:
                sl_price = sl_price_atr
            else:
                # 使用更保守的止损（多单取较高值，空单取较低值）
                if direction == 'LONG':
                    sl_price = max(sl_price_atr, sl_price_risk)
                else:
                    sl_price = min(sl_price_atr, sl_price_risk)
            
            # 如果止损计算失败，使用默认止损 - 也应该使用综合风险因子
            if sl_price is None:
                logger.warning(f"止损计算失败，使用默认止损计算方法")
                if direction == 'LONG':
                    sl_price = price - (atr_value * combined_risk_factor)  # 使用综合风险因子而不是固定的2
                else:
                    sl_price = price + (atr_value * combined_risk_factor)  # 使用综合风险因子而不是固定的2
                logger.info(f"默认止损计算: 价格 {price} +/- (ATR {atr_value} * 风险因子 {combined_risk_factor}) = {sl_price}")
                
            # 创建交易注释
            # 格式: 信号类型 + 时间周期 + 方向 + ATR值
            # 例如: B1h1L3.5 表示B类信号，1小时周期，多单，ATR值3.5
            tf_str = self._get_timeframe_str(timeframe)
            direction_char = 'L' if direction == 'LONG' else 'S'
            comment = f"{signal_type}{tf_str}{direction_char}{atr_value:.2f}"
            
            # 确保注释不超过32个字符
            if len(comment) > 32:
                comment = comment[:31]
                
            # 创建交易请求
            order_type = mt5.ORDER_TYPE_BUY if direction == 'LONG' else mt5.ORDER_TYPE_SELL
            
            # 计算止盈价格（如果需要）
            tp_price = 0.0  # 默认不设置止盈
            if self.signal_config.get(signal_type, {}).get('take_profit'):
                tp_multiplier = self.signal_config[signal_type]['take_profit']
                if direction == 'LONG':
                    tp_price = price + (atr_value * tp_multiplier)
                else:
                    tp_price = price - (atr_value * tp_multiplier)
                
                # 四舍五入到合适的小数位
                symbol_info = mt5.symbol_info(symbol)
                if symbol_info:
                    tp_price = round(tp_price, symbol_info.digits)
            
            # 创建交易请求
            request = {
                "action": mt5.TRADE_ACTION_DEAL,
                "symbol": symbol,
                "volume": lot_size,  # 使用正确的变量名
                "type": order_type,
                "price": price,
                "sl": sl_price,
                "tp": tp_price,
                "deviation": 20,
                "magic": 234000,
                "comment": comment,
                "type_time": mt5.ORDER_TIME_GTC,
                "type_filling": mt5.ORDER_FILLING_FOK,
            }
            
            # 发送交易请求
            result = mt5.order_send(request)
            if result.retcode != mt5.TRADE_RETCODE_DONE:
                logger.error(f"开仓失败: {result.comment}, 错误代码: {result.retcode}")
                return False
                
            logger.info(f"开仓成功: {symbol} {tf_str} {direction}, 价格: {price}, 止损: {sl_price}, 止盈: {tp_price}, 手数: {lot_size}, 订单号: {result.order}")
            return True
            
        except Exception as e:
            logger.error(f"开仓出错: {str(e)}", exc_info=True)
            return False

    def calculate_position_size(self, symbol, timeframe, atr_value, risk_amount=None):
        """计算仓位大小 - 使用固定手数"""
        try:
            # 获取品种和周期配置
            if symbol not in self.symbols_config:
                logger.error(f"未找到品种配置: {symbol}")
                return self.symbols_config.get(symbol, {}).get("min_lot", 0.01)
                
            if timeframe not in self.symbols_config[symbol]["timeframes"]:
                logger.error(f"未找到周期配置: {symbol} {timeframe}")
                return self.symbols_config[symbol].get("min_lot", 0.01)
                
            # 获取固定手数
            lot_size = self.symbols_config[symbol]["timeframes"][timeframe].get("lot_size", 0.01)
            
            logger.info(f"使用固定手数: {symbol}, 周期: {self._get_timeframe_str(timeframe)}, 手数: {lot_size}")
            return lot_size
            
        except Exception as e:
            logger.error(f"计算仓位大小出错: {str(e)}", exc_info=True)
            # 出错时返回最小手数
            return self.symbols_config[symbol].get("min_lot", 0.01)
            
    def get_lot_size(self, symbol, timeframe):
        """获取固定手数"""
        try:
            # 直接从配置中获取固定手数
            if symbol in self.symbols_config and timeframe in self.symbols_config[symbol]["timeframes"]:
                lot_size = self.symbols_config[symbol]["timeframes"][timeframe].get("lot_size", 0.01)
                return lot_size
            else:
                logger.warning(f"未找到{symbol} {timeframe}的手数配置，使用默认值0.01")
                return 0.01
        except Exception as e:
            logger.error(f"获取手数出错: {str(e)}", exc_info=True)
            return 0.01  # 出错时返回默认值

    def manage_positions(self, signals=None):
        """管理持仓"""
        try:
            logger.info("开始管理持仓...")
            
            # 更新持仓管理器的信号
            if signals:
                self.position_manager.update_signals(signals)
            
            # 调用持仓管理器管理所有持仓
            logger.info("使用持仓管理器管理持仓...")
            self.position_manager.manage_all_positions()
            
            logger.info("持仓管理完成")
                    
        except Exception as e:
            logger.error(f"管理持仓出错: {str(e)}", exc_info=True)

    def get_risk_factor(self, symbol, timeframe, signal_type=None, signal_strength=None):
        """获取风险因子"""
        try:
            # 从品种和周期特定配置获取风险因子
            if symbol in self.symbols_config and timeframe in self.symbols_config[symbol]["timeframes"]:
                config_risk_factor = self.symbols_config[symbol]["timeframes"][timeframe].get("risk_factor", 1.0)
            else:
                config_risk_factor = 1.0
                logger.warning(f"未找到{symbol} {timeframe}的风险因子配置，使用默认值1.0")
                
            return config_risk_factor
            
        except Exception as e:
            logger.error(f"获取风险因子出错: {str(e)}", exc_info=True)
            return 1.0  # 出错时返回默认值


    def run(self):
        """运行交易系统"""
        start_time = time.time()
        try:
            logger.info("=== 开始新一轮交易系统运行 ===")
            
            # 检查MT5连接
            if not mt5.terminal_info():
                logger.error("MT5连接已断开,尝试重新初始化...")
                if not mt5.initialize():
                    logger.error("MT5重新初始化失败")
                    return
            
            # 分析市场
            logger.info("开始市场分析...")
            analysis_start = time.time()
            
            # 设置分析器为双向分析
            self.analyzer.direction = "both"
            signals = self.analyzer.analyze_market(notify=True)
            
            analysis_time = time.time() - analysis_start
            logger.info(f"市场分析完成，耗时: {analysis_time:.2f}秒")
            
            if signals:
                logger.info(f"收到信号: {len(signals)}个")
                # 按品种和方向分组信号
                signals_by_symbol_direction = self._group_signals_by_symbol_direction(signals)
                
                # 输出详细的信号信息
                for symbol, directions in signals_by_symbol_direction.items():
                    for direction, direction_signals in directions.items():
                        timeframes = [TIMEFRAME_STRINGS.get(self.timeframe_map.get(s['timeframe']), s['timeframe']) for s in direction_signals]
                        logger.info(f"{symbol} {direction}方向信号: {len(direction_signals)}个, 周期: {timeframes}")
                
                # 管理持仓 - 使用所有信号
                self.position_manager.manage_all_positions(signals)
                
                # 处理信号 - 为每个信号开仓
                for signal in signals:
                    self.process_signal(signal)
            else:
                logger.info("没有交易信号")
                # 没有信号时也需要管理持仓
                self.position_manager.manage_all_positions([])
            
            run_time = time.time() - start_time
            logger.info(f"交易系统运行完成，总耗时: {run_time:.2f}秒")
            
        except Exception as e:
            logger.error(f"交易系统运行出错: {str(e)}", exc_info=True)
           
    def process_signal(self, signal):
        """处理单个信号"""
        try:
            symbol = signal['symbol']
            timeframe = signal['timeframe']
            direction = signal['direction']
            signal_type = signal.get('type', 'B')  # 获取信号类型，默认为B
            signal_strength = signal.get('strength', 'MEDIUM')  # 获取信号强度，默认为MEDIUM
            
            # 检查是否可以开仓
            if not self.can_add_position(symbol, timeframe, signal_type, direction):
                logger.info(f"不满足开仓条件，跳过信号: {symbol} {timeframe} {direction}")
                return
            
            # 开仓 - 传递正确的参数
            success = self.open_position(symbol, timeframe, direction, signal_type, signal_strength)
            if success:
                logger.info(f"成功处理信号: {symbol} {timeframe} {direction}")
            else:
                logger.warning(f"处理信号失败: {symbol} {timeframe} {direction}")
                
        except Exception as e:
            logger.error(f"处理信号出错: {str(e)}", exc_info=True)
    
    def _group_signals_by_symbol_direction(self, signals):
        """按品种和方向分组信号"""
        result = {}
        for signal in signals:
            symbol = signal['symbol']
            direction = signal['direction']
            
            if symbol not in result:
                result[symbol] = {'LONG': [], 'SHORT': []}
            
            result[symbol][direction].append(signal)
        
        return result

    def _get_timeframe_str(self, timeframe):
        """获取时间周期的字符串表示"""
        return TIMEFRAME_STRINGS.get(timeframe, str(timeframe))

    def get_positions_by_symbol_and_timeframe(self, symbol, timeframe):
        """获取指定品种和时间周期的持仓"""
        positions = []
        
        # 获取所有持仓
        all_positions = mt5.positions_get(symbol=symbol)
        if all_positions is None:
            return positions
            
        # 获取时间周期字符串
        tf_string = TIMEFRAME_STRINGS.get(timeframe, "")
        if not tf_string:
            return positions
            
        # 检查每个持仓的注释
        for position in all_positions:
            # 提取原始注释（去除可能的部分平仓标记）
            comment = position.comment
            original_comment = comment.split("PC")[0] if "PC" in comment else comment
            
            # 检查注释中是否包含时间周期标识
            if any(tf_check in original_comment for tf_check in TIMEFRAME_CHECK_MAP.get(tf_string, [])):
                positions.append(position)
                
        return positions

    @timeit
    def analyze_market(self):
        """分析市场并获取交易信号"""
        logger.info("开始市场分析...")
        
        # 获取当前市场状态和交易方向
        market_directions = self.trading_directions
        if not market_directions:
            logger.warning("没有确定任何品种的交易方向，无法进行分析")
            return []
        
        # 创建市场分析器并传入交易方向
        start_time = time.time()
        
        # 只分析符合当前方向的品种
        filtered_symbols = []
        symbol_directions = {}  # 新增：用于存储每个品种的方向
        for symbol, direction in market_directions.items():
            filtered_symbols.append(symbol)
            symbol_directions[symbol] = direction  # 新增：记录每个品种的方向
            logger.info(f"{symbol} 现价方向为: {direction}，只分析{direction}方向")
        
        if not filtered_symbols:
            logger.warning("没有符合条件的品种可分析")
            return []
            
        # 修改：为每个品种创建独立的分析器，并在创建时就指定正确的方向
        all_signals = []
        for symbol in filtered_symbols:
            direction = symbol_directions[symbol]
            # 创建分析器时直接指定方向
            analyzer = MT5UnifiedMarketAnalyzer(custom_symbols=[symbol], direction=direction)
            
            # 执行分析
            signals = analyzer.analyze_market(notify=False)
            if signals:
                all_signals.extend(signals)
        
        end_time = time.time()
        logger.info(f"市场分析完成，耗时: {end_time - start_time:.2f}秒")
        
        if all_signals:
            logger.info(f"发现 {len(all_signals)} 个符合交易方向的信号")
            for signal in all_signals:
                logger.info(f"信号: {signal['symbol']} {signal['timeframe']} {signal['direction']} {signal['type']} 强度:{signal['strength']}")
        else:
            logger.info("没有符合交易方向的信号")
            
        return all_signals
    
if __name__ == "__main__":

    trader = MT5Trader()
    
    # # 选择运行模式
    # print("\n选择运行模式:")
    # print("1. 测试运行（立即执行一次）")
    # print("2. 自动运行（定时执行）")
    # choice = input("请输入选择（1 或 2）: ")
    
    # if choice == "1":
    #     trader.test_run()
    # else:
    trader.run() 