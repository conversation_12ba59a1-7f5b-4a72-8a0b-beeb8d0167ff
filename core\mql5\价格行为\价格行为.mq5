//+------------------------------------------------------------------+
//|                                                    价格行为.mq5 |
//|                                  Copyright 2025, MetaQuotes Ltd. |
//|                                             https://www.mql5.com |
//|                        价格行为策略：大阳/阴线突破 + EMA20过滤    |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, MetaQuotes Ltd."
#property link      "https://www.mql5.com"
#property version   "1.00"

// 输入参数
input double   LotSize = 0.1;           // 交易手数
input bool     AutoLotSize = false;     // 自动调整交易手数
input double   LotSizePerK = 0.01;      // 每1000结余对应的手数(自动模式)
input int      RiskRewardRatio = 2;     // 盈亏比(1:N)
input bool     UseEMA20Filter = true;   // 使用EMA20过滤
input bool     OnlyLong = true;         // 只做多单
input bool     OnlyShort = false;       // 只做空单
input double   BigCandlePercent = 0.5;  // 大阳/阴线定义(收盘与开盘差值占整体K线的百分比)
input bool     UsePendingOrder = true;  // 使用挂单(true)或市价单(false)
input int      PendingOrderExpiry = 60; // 挂单自动取消时间(秒)
input int      LookbackPeriod = 50;     // 寻找摆动点的回看K线数量
input int      SwingDefinitionBars = 2; // 定义摆动点的简单分形规则(左右各N根K线)
input int      NumRecentPoints = 5;     // 返回最近的摆动点数量
input bool     UsePartialTP = true;     // 是否启用部分止盈
input double   PartialTP_RRR = 1.5;     // 部分止盈的盈亏比 (TP1 = R * N)
input double   PartialClosePercent = 0.5; // 部分止盈时平仓的百分比 (例如 0.5 = 50%)
input bool     UseBreakEven = true;     // 是否启用移至保本
input double   BreakEvenBufferPoints = 2; // 移动到保本时增加的点数缓冲
input bool     UseTrailingStop = true;  // 是否启用追踪止损
input int      ATR_Period = 14;         // ATR 指标周期
input double   ATR_Multiplier = 2.5;    // ATR 倍数
input bool     TradeReversals = false;  // 新增: true = 交易反转信号, false = 交易原始突破信号

// 全局变量
int handle_ema20;                       // EMA20指标句柄
int handle_atr;                         // ATR指标句柄
double ema20_buffer[];                  // EMA20数据缓冲区
ulong ticket = 0;                       // 订单号

// 定义摆动点结构体
struct SwingPoint
{
   double price;  // 价格
   int index;     // K线索引
};

// 定义市场结构枚举类型
enum MarketStructure 
{
   TREND_UP,       // 上升趋势 (HH, HL)
   TREND_DOWN,     // 下降趋势 (LH, LL)
   RANGE,          // 盘整/区间
   UNDEFINED       // 无法确定 (数据不足等)
};

//+------------------------------------------------------------------+
//| 计算订单参数                                                      |
//+------------------------------------------------------------------+
bool CalculateOrderParams(const MqlRates &signal_candle, bool is_bullish_trade, bool is_reversal_mode, 
                           double &entry_price, double &stop_loss, double &take_profit)
{
   // 获取交易品种的最小距离要求（点数）
   long stops_level = SymbolInfoInteger(_Symbol, SYMBOL_TRADE_STOPS_LEVEL);
   double min_distance = stops_level * _Point;
   Print("交易品种最小止损/止盈距离: ", min_distance, " 点");
   
   // 获取ATR值(用于反转止损缓冲)
   double atr_value = 0.0;
   double atr_buffer[];
   ArraySetAsSeries(atr_buffer, true);
   
   if(CopyBuffer(handle_atr, 0, 0, 2, atr_buffer) >= 2)
   {
      atr_value = atr_buffer[1]; // 使用前一根K线收盘时的ATR值
      Print("当前ATR值: ", atr_value);
   }
   else
   {
      Print("获取ATR数据失败，使用固定点数作为缓冲");
      atr_value = 10 * _Point; // 默认值
   }
   
   // 根据交易模式计算参数
   if(is_reversal_mode)
   {
      // === 反转模式 ===
      
      // 如果设置了使用挂单但处于反转模式，打印警告
      if(UsePendingOrder)
      {
         Print("警告: 反转模式下不支持挂单，将使用市价单");
      }
      
      // 市价入场
      entry_price = is_bullish_trade ? Ask() : Bid();
      
      // 止损放在信号K线的极值点之外
      if(is_bullish_trade) // 做多反转(信号K线是阴线)
      {
         // 止损设置在信号K线低点下方
         stop_loss = NormalizeDouble(signal_candle.low - 5 * _Point, _Digits);
         
         // 确保止损点位符合最小距离要求
         if((entry_price - stop_loss) < min_distance)
         {
            stop_loss = entry_price - min_distance - 5 * _Point;
            Print("调整止损点位至: ", stop_loss, " (确保符合最小距离要求)");
         }
         
         Print("反转做多 - 入场价: ", entry_price, ", 止损: ", stop_loss, " (信号K线低点: ", signal_candle.low, ")");
      }
      else // 做空反转(信号K线是阳线)
      {
         // 止损设置在信号K线高点上方
         stop_loss = NormalizeDouble(signal_candle.high + 5 * _Point, _Digits);
         
         // 确保止损点位符合最小距离要求
         if((stop_loss - entry_price) < min_distance)
         {
            stop_loss = entry_price + min_distance + 5 * _Point;
            Print("调整止损点位至: ", stop_loss, " (确保符合最小距离要求)");
         }
         
         Print("反转做空 - 入场价: ", entry_price, ", 止损: ", stop_loss, " (信号K线高点: ", signal_candle.high, ")");
      }
      
      // 计算止盈点位
      double stop_distance = MathAbs(entry_price - stop_loss);
      
      if(is_bullish_trade)
      {
         take_profit = entry_price + (stop_distance * RiskRewardRatio);
         Print("反转做多止盈点位: ", take_profit, ", 盈亏比: 1:", RiskRewardRatio);
      }
      else
      {
         take_profit = entry_price - (stop_distance * RiskRewardRatio);
         Print("反转做空止盈点位: ", take_profit, ", 盈亏比: 1:", RiskRewardRatio);
      }
   }
   else
   {
      // === 原始突破模式 ===
      
      if(UsePendingOrder)
      {
         // 挂单模式
         if(is_bullish_trade)
         {
            // 多单 - Buy Stop
            double current_ask = Ask();
            
            // 计算挂单价格 - 确保高于当前价格且符合最小距离要求
            entry_price = MathMax(signal_candle.high + 5 * _Point, current_ask + min_distance + 5 * _Point);
            
            // 计算止损点位 - 前一根K线最低点下方
            stop_loss = signal_candle.low - 5 * _Point;
            
            // 确保止损点位符合最小距离要求
            if((entry_price - stop_loss) < min_distance)
            {
               stop_loss = entry_price - min_distance - 5 * _Point;
               Print("调整止损点位至: ", stop_loss, " (确保符合最小距离要求)");
            }
            
            Print("多单挂单 - 价格: ", entry_price, " (前高: ", signal_candle.high, 
                  "), 止损: ", stop_loss, " (前低: ", signal_candle.low, ")");
         }
         else
         {
            // 空单 - Sell Stop
            double current_bid = Bid();
            
            // 计算挂单价格 - 确保低于当前价格且符合最小距离要求
            entry_price = MathMin(signal_candle.low - 5 * _Point, current_bid - min_distance - 5 * _Point);
            
            // 计算止损点位 - 前一根K线最高点上方
            stop_loss = signal_candle.high + 5 * _Point;
            
            // 确保止损点位符合最小距离要求
            if((stop_loss - entry_price) < min_distance)
            {
               stop_loss = entry_price + min_distance + 5 * _Point;
               Print("调整止损点位至: ", stop_loss, " (确保符合最小距离要求)");
            }
            
            Print("空单挂单 - 价格: ", entry_price, " (前低: ", signal_candle.low, 
                  "), 止损: ", stop_loss, " (前高: ", signal_candle.high, ")");
         }
      }
      else
      {
         // 市价单模式
         if(is_bullish_trade)
         {
            // 多单 - 市价买入
            entry_price = Ask();
            stop_loss = signal_candle.low - 5 * _Point;
            
            // 确保止损点位符合最小距离要求
            if((entry_price - stop_loss) < min_distance)
            {
               stop_loss = entry_price - min_distance - 5 * _Point;
               Print("调整止损点位至: ", stop_loss, " (确保符合最小距离要求)");
            }
            
            Print("多单市价 - 价格: ", entry_price, ", 止损: ", stop_loss, " (前低: ", signal_candle.low, ")");
         }
         else
         {
            // 空单 - 市价卖出
            entry_price = Bid();
            stop_loss = signal_candle.high + 5 * _Point;
            
            // 确保止损点位符合最小距离要求
            if((stop_loss - entry_price) < min_distance)
            {
               stop_loss = entry_price + min_distance + 5 * _Point;
               Print("调整止损点位至: ", stop_loss, " (确保符合最小距离要求)");
            }
            
            Print("空单市价 - 价格: ", entry_price, ", 止损: ", stop_loss, " (前高: ", signal_candle.high, ")");
         }
      }
      
      // 计算止盈点位
      double stop_distance = MathAbs(entry_price - stop_loss);
      
      if(is_bullish_trade)
      {
         take_profit = entry_price + (stop_distance * RiskRewardRatio);
         Print("多单止盈点位: ", take_profit, ", 盈亏比: 1:", RiskRewardRatio);
      }
      else
      {
         take_profit = entry_price - (stop_distance * RiskRewardRatio);
         Print("空单止盈点位: ", take_profit, ", 盈亏比: 1:", RiskRewardRatio);
      }
   }
   
   return true; // 参数计算成功
}

//+------------------------------------------------------------------+
//| 执行下单                                                          |
//+------------------------------------------------------------------+
void PlaceOrder(bool is_bullish_trade, double entry_price, double stop_loss, double take_profit, 
                double volume, bool use_pending, const MqlRates &signal_candle)
{
   // 准备下单请求
   MqlTradeRequest request = {};
   MqlTradeResult result = {};
   
   // 判断是否使用挂单
   bool actual_use_pending = use_pending && !TradeReversals; // 反转模式强制使用市价单
   
   if(actual_use_pending)
   {
      request.action = TRADE_ACTION_PENDING;
      request.type = is_bullish_trade ? ORDER_TYPE_BUY_STOP : ORDER_TYPE_SELL_STOP;
      
      // 不设置过期时间，使用GTC模式（取消前有效）
      request.type_time = ORDER_TIME_GTC;
      request.expiration = 0;
      
      Print("创建", is_bullish_trade ? "Buy Stop" : "Sell Stop", "挂单, 价格: ", entry_price);
   }
   else
   {
      request.action = TRADE_ACTION_DEAL;
      request.type = is_bullish_trade ? ORDER_TYPE_BUY : ORDER_TYPE_SELL;
      
      Print("创建", is_bullish_trade ? "买入" : "卖出", "市价单, 价格: ", entry_price);
   }
   
   // 设置填充类型
   request.type_filling = ORDER_FILLING_FOK;
   
   // 设置交易参数
   request.symbol = _Symbol;
   request.volume = volume;
   request.price = entry_price;
   request.sl = stop_loss;
   request.tp = take_profit;
   request.deviation = 50;  // 允许的滑点
   request.magic = 123456;  // EA魔术数字
   request.comment = TradeReversals ? "价格行为EA-反转" : "价格行为EA-突破";
   
   // 发送订单
   bool success = OrderSend(request, result);
   if(success)
   {
      ticket = result.order;
      if(actual_use_pending)
      {
         Print("挂单创建成功! 订单号: ", ticket);
      }
      else
      {
         Print("市价单执行成功! 订单号: ", ticket);
      }
   }
   else
   {
      int error_code = GetLastError();
      Print("订单执行失败! 错误代码: ", error_code, ", 错误描述: ", GetLastErrorDescription());
      
      // 打印详细的请求信息，帮助调试
      Print("请求详情 - 交易品种: ", request.symbol, 
            ", 交易量: ", request.volume, 
            ", 价格: ", request.price, 
            ", 止损: ", request.sl, 
            ", 止盈: ", request.tp);
   }
}



//+------------------------------------------------------------------+
//| 识别最近N个摆动高点和低点作为潜在的阻力位和支撑位                   |
//+------------------------------------------------------------------+
void DetectSwingSRLevels(const MqlRates &rates[], int lookback_period, int swing_definition_bars, 
   double &resistance_levels[], double &support_levels[], int num_recent_points = 5)
{
   // 清空输出数组
   ArrayResize(resistance_levels, 0);
   ArrayResize(support_levels, 0);
   
   // 确保有足够的数据进行分析
   int total_bars = ArraySize(rates);
   if(total_bars < lookback_period || lookback_period <= 2 * swing_definition_bars)
   {
      Print("警告: 没有足够的K线数据进行摆动点分析");
      return;
   }
   
   // 限制回看周期不超过可用数据
   lookback_period = MathMin(lookback_period, total_bars);
   
   // 创建临时数组存储所有摆动点
   SwingPoint swing_highs[];
   SwingPoint swing_lows[];
   ArrayResize(swing_highs, 0);
   ArrayResize(swing_lows, 0);
   
   // 遍历K线寻找摆动点
   for(int i = swing_definition_bars; i < lookback_period - swing_definition_bars; i++)
   {
      // 检查是否为摆动高点
      bool is_swing_high = true;
      
      // 检查左侧K线
      for(int j = 1; j <= swing_definition_bars; j++)
      {
         if(rates[i].high <= rates[i-j].high)
         {
            is_swing_high = false;
            break;
         }
      }
      
      // 如果左侧检查通过，继续检查右侧K线
      if(is_swing_high)
      {
         for(int j = 1; j <= swing_definition_bars; j++)
         {
            if(rates[i].high <= rates[i+j].high)
            {
               is_swing_high = false;
               break;
            }
         }
      }
      
      // 如果是摆动高点，添加到临时数组
      if(is_swing_high)
      {
         int size = ArraySize(swing_highs);
         ArrayResize(swing_highs, size + 1);
         swing_highs[size].price = rates[i].high;
         swing_highs[size].index = i;
      }
      
      // 检查是否为摆动低点
      bool is_swing_low = true;
      
      // 检查左侧K线
      for(int j = 1; j <= swing_definition_bars; j++)
      {
         if(rates[i].low >= rates[i-j].low)
         {
            is_swing_low = false;
            break;
         }
      }
      
      // 如果左侧检查通过，继续检查右侧K线
      if(is_swing_low)
      {
         for(int j = 1; j <= swing_definition_bars; j++)
         {
            if(rates[i].low >= rates[i+j].low)
            {
               is_swing_low = false;
               break;
            }
         }
      }
      
      // 如果是摆动低点，添加到临时数组
      if(is_swing_low)
      {
         int size = ArraySize(swing_lows);
         ArrayResize(swing_lows, size + 1);
         swing_lows[size].price = rates[i].low;
         swing_lows[size].index = i;
      }
   }
   
   // 按K线索引降序排序摆动高点（最新的在前）
   for(int i = 0; i < ArraySize(swing_highs) - 1; i++)
   {
      for(int j = i + 1; j < ArraySize(swing_highs); j++)
      {
         if(swing_highs[i].index > swing_highs[j].index)
            continue;
            
         SwingPoint temp = swing_highs[i];
         swing_highs[i] = swing_highs[j];
         swing_highs[j] = temp;
      }
   }
   
   // 按K线索引降序排序摆动低点（最新的在前）
   for(int i = 0; i < ArraySize(swing_lows) - 1; i++)
   {
      for(int j = i + 1; j < ArraySize(swing_lows); j++)
      {
         if(swing_lows[i].index > swing_lows[j].index)
            continue;
            
         SwingPoint temp = swing_lows[i];
         swing_lows[i] = swing_lows[j];
         swing_lows[j] = temp;
      }
   }
   
   // 获取最近的num_recent_points个摆动高点
   int high_count = MathMin(ArraySize(swing_highs), num_recent_points);
   ArrayResize(resistance_levels, high_count);
   for(int i = 0; i < high_count; i++)
   {
      resistance_levels[i] = swing_highs[i].price;
   }
   
   // 获取最近的num_recent_points个摆动低点
   int low_count = MathMin(ArraySize(swing_lows), num_recent_points);
   ArrayResize(support_levels, low_count);
   for(int i = 0; i < low_count; i++)
   {
      support_levels[i] = swing_lows[i].price;
   }
   
   // 打印找到的最近摆动点数量
   Print("找到 ", high_count, " 个最近摆动高点和 ", low_count, " 个最近摆动低点");
   
   // 打印详细的摆动点信息（用于调试）
   for(int i = 0; i < high_count; i++)
   {
      Print("最近摆动高点 #", i+1, ": 价格 = ", resistance_levels[i], ", K线索引 = ", swing_highs[i].index);
   }
   
   for(int i = 0; i < low_count; i++)
   {
      Print("最近摆动低点 #", i+1, ": 价格 = ", support_levels[i], ", K线索引 = ", swing_lows[i].index);
   }
}


//+------------------------------------------------------------------+
//| 分析市场结构                                                      |
//+------------------------------------------------------------------+
MarketStructure GetMarketStructure(const double &recent_highs[], const double &recent_lows[], int points_to_check)
{
   // 检查数组大小是否足够
   if(ArraySize(recent_highs) < points_to_check || ArraySize(recent_lows) < points_to_check)
   {
      Print("市场结构分析: 数据点不足，需要至少 ", points_to_check, " 个高点和低点");
      return UNDEFINED;
   }
   
   // 检查是否为上升趋势 (Higher Highs & Higher Lows)
   bool has_higher_highs = true;
   bool has_higher_lows = true;
   
   for(int i = 0; i < points_to_check - 1; i++)
   {
      // 检查连续的高点是否上升
      if(recent_highs[i] <= recent_highs[i+1])
      {
         has_higher_highs = false;
         break;
      }
   }
   
   for(int i = 0; i < points_to_check - 1; i++)
   {
      // 检查连续的低点是否上升
      if(recent_lows[i] <= recent_lows[i+1])
      {
         has_higher_lows = false;
         break;
      }
   }
   
   if(has_higher_highs && has_higher_lows)
   {
      Print("市场结构分析: 上升趋势 (Higher Highs & Higher Lows)");
      return TREND_UP;
   }
   
   // 检查是否为下降趋势 (Lower Highs & Lower Lows)
   bool has_lower_highs = true;
   bool has_lower_lows = true;
   
   for(int i = 0; i < points_to_check - 1; i++)
   {
      // 检查连续的高点是否下降
      if(recent_highs[i] >= recent_highs[i+1])
      {
         has_lower_highs = false;
         break;
      }
   }
   
   for(int i = 0; i < points_to_check - 1; i++)
   {
      // 检查连续的低点是否下降
      if(recent_lows[i] >= recent_lows[i+1])
      {
         has_lower_lows = false;
         break;
      }
   }
   
   if(has_lower_highs && has_lower_lows)
   {
      Print("市场结构分析: 下降趋势 (Lower Highs & Lower Lows)");
      return TREND_DOWN;
   }
   
   // 如果既不是明确的上升趋势，也不是明确的下降趋势，则认为是盘整
   Print("市场结构分析: 盘整/区间");
   return RANGE;
}

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
   // 初始化EMA20指标
   handle_ema20 = iMA(_Symbol, PERIOD_CURRENT, 20, 0, MODE_EMA, PRICE_CLOSE);
   if(handle_ema20 == INVALID_HANDLE)
   {
      Print("创建EMA20指标失败!");
      return(INIT_FAILED);
   }
   
   // 初始化ATR指标
   handle_atr = iATR(_Symbol, PERIOD_CURRENT, ATR_Period);
   if(handle_atr == INVALID_HANDLE)
   {
      Print("创建ATR指标失败!");
      // 不要修改输入参数，只打印错误信息
      Print("ATR指标创建失败，追踪止损功能可能无法正常工作!");
   }
   
   // 分配缓冲区内存
   ArraySetAsSeries(ema20_buffer, true);
   
   // 设置1秒定时器，用于检查挂单超时
   EventSetTimer(1);
   
   Print("价格行为EA初始化成功! 策略说明:");
   Print("1. 寻找大阳/阴线(实体占比>", BigCandlePercent, ")");
   Print("2. 使用EMA20过滤: ", UseEMA20Filter ? "是" : "否");
   Print("3. 交易方向: ", OnlyLong && !OnlyShort ? "仅多单" : (OnlyShort && !OnlyLong ? "仅空单" : "双向"));
   Print("4. 订单类型: ", UsePendingOrder ? "挂单" : "市价单");
   Print("5. 挂单自动取消时间: ", PendingOrderExpiry, "秒");
   Print("6. 部分止盈: ", UsePartialTP ? "启用" : "禁用");
   Print("7. 移动止损到保本: ", UseBreakEven ? "启用" : "禁用");
   Print("8. 追踪止损: ", UseTrailingStop ? "启用" : "禁用");
   
   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| 定时器函数 - 用于自动取消超时挂单                                  |
//+------------------------------------------------------------------+
void OnTimer()
{
   // 检查是否有挂单
   if(OrdersTotal() > 0)
   {
      // 遍历所有挂单
      for(int i = OrdersTotal() - 1; i >= 0; i--)
      {
         ulong order_ticket = OrderGetTicket(i);
         if(order_ticket > 0)
         {
            // 确认是我们的EA创建的挂单
            if(OrderGetInteger(ORDER_MAGIC) == 123456)
            {
               // 获取挂单创建时间
               datetime order_time = (datetime)OrderGetInteger(ORDER_TIME_SETUP);
               datetime current_time = TimeCurrent();
               
               // 计算挂单存在的时间（秒）
               int seconds_elapsed = (int)(current_time - order_time);
               
               // 如果挂单存在时间超过设定的秒数，则取消
               if(seconds_elapsed > PendingOrderExpiry)
               {
                  MqlTradeRequest request = {};
                  MqlTradeResult result = {};
                  
                  request.action = TRADE_ACTION_REMOVE;
                  request.order = order_ticket;
                  
                  bool success = OrderSend(request, result);
                  if(success)
                  {
                     Print("已取消超时挂单，订单号: ", order_ticket, "，存在时间: ", seconds_elapsed, "秒");
                  }
                  else
                  {
                     int error_code = GetLastError();
                     Print("取消挂单失败! 订单号: ", order_ticket, "，错误代码: ", error_code, 
                           ", 错误描述: ", GetLastErrorDescription());
                  }
               }
            }
         }
      }
   }
}


//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   // 释放指标句柄
   if(handle_ema20 != INVALID_HANDLE)
      IndicatorRelease(handle_ema20);
      
   // 释放ATR指标句柄
   if(handle_atr != INVALID_HANDLE)
      IndicatorRelease(handle_atr);
      
   // 删除定时器
   EventKillTimer();
   
   // 删除所有创建的S/R水平线
   ObjectsDeleteAll(0, "SR_Level_");
   
   Print("价格行为EA已卸载，原因代码: ", reason);
}


//+------------------------------------------------------------------+
//| 管理已开仓位                                                      |
//+------------------------------------------------------------------+
void ManageOpenPositions()
{
   // 如果没有持仓，直接返回
   if(PositionsTotal() == 0)
      return;
      
   // 获取ATR数据
   double atr_buffer[];
   double atr_value = 0.0;
   
   ArraySetAsSeries(atr_buffer, true);
   if(!UseTrailingStop || CopyBuffer(handle_atr, 0, 0, 2, atr_buffer) < 2)
   {
      // 如果禁用或获取失败，设置atr_value为0
      atr_value = 0.0;
      if(UseTrailingStop) Print("获取ATR数据失败!");
   }
   else
   {
      atr_value = atr_buffer[1]; // 使用前一根K线收盘时的ATR值
      Print("当前ATR值: ", atr_value);
   }
      
   // 遍历所有持仓
   for(int i = PositionsTotal() - 1; i >= 0; i--)
   {
      // 获取持仓订单号
      ulong position_ticket = PositionGetTicket(i);
      
      // 如果无法获取持仓信息，跳过当前循环
      if(!PositionSelectByTicket(position_ticket))
         continue;
         
      // 检查魔术数字，确保只管理本EA的仓位
      long position_magic = PositionGetInteger(POSITION_MAGIC);
      if(position_magic != 123456)
         continue;
         
      // 获取持仓详细信息
      long position_type = PositionGetInteger(POSITION_TYPE); // ORDER_TYPE_BUY or ORDER_TYPE_SELL
      double position_open_price = PositionGetDouble(POSITION_PRICE_OPEN);
      double position_current_sl = PositionGetDouble(POSITION_SL);
      double position_current_tp = PositionGetDouble(POSITION_TP);
      double position_volume = PositionGetDouble(POSITION_VOLUME);
      double position_profit = PositionGetDouble(POSITION_PROFIT);
      datetime position_time = (datetime)PositionGetInteger(POSITION_TIME);
      string position_symbol = PositionGetString(POSITION_SYMBOL);
      
      // 获取当前市场价格（在函数开始时只获取一次）
      double current_bid = Bid();
      double current_ask = Ask();
      
      // 打印持仓信息（调试用）
      Print("管理持仓 #", position_ticket, 
            ", 类型: ", position_type == POSITION_TYPE_BUY ? "买入" : "卖出", 
            ", 开仓价: ", position_open_price,
            ", 当前止损: ", position_current_sl,
            ", 当前止盈: ", position_current_tp,
            ", 手数: ", position_volume,
            ", 盈亏: ", position_profit);
      
      // --- Step 1: Check and Execute Partial Take Profit (TP1) ---
      // 检查是否启用部分止盈
      if(!UsePartialTP)
         continue;
         
      // 定义保本区域缓冲
      double be_buffer = 2 * _Point;
      
      // 检查止损是否已经移动到保本区域（如果是，则认为TP1已执行过）
      if((position_type == POSITION_TYPE_BUY && position_current_sl >= position_open_price - be_buffer) || 
         (position_type == POSITION_TYPE_SELL && position_current_sl <= position_open_price + be_buffer))
      {
         Print("止损已在保本区域，跳过TP1检查");
         continue;
      }
      
      // 计算初始风险距离（基于当前止损，假设未移动过）
      double initial_risk_distance = MathAbs(position_open_price - position_current_sl);
      
      // 计算TP1目标价格
      double tp1_price = 0;
      if(position_type == POSITION_TYPE_BUY)
      {
         tp1_price = position_open_price + initial_risk_distance * PartialTP_RRR;
      }
      else
      {
         tp1_price = position_open_price - initial_risk_distance * PartialTP_RRR;
      }
      
      // 检查是否达到TP1价格
      bool tp1_reached = false;
      if(position_type == POSITION_TYPE_BUY && current_bid >= tp1_price)
      {
         tp1_reached = true;
         Print("多单达到TP1价格: 当前Bid=", current_bid, ", TP1=", tp1_price);
      }
      else if(position_type == POSITION_TYPE_SELL && current_ask <= tp1_price)
      {
         tp1_reached = true;
         Print("空单达到TP1价格: 当前Ask=", current_ask, ", TP1=", tp1_price);
      }
      
      // 如果达到TP1价格，执行部分平仓
      if(tp1_reached)
      {
         // 计算要平仓的手数
         double volume_step = SymbolInfoDouble(position_symbol, SYMBOL_VOLUME_STEP);
         double volume_to_close = NormalizeDouble(position_volume * PartialClosePercent, 2);
         
         // 确保手数符合最小步长
         volume_to_close = NormalizeDouble(MathRound(volume_to_close / volume_step) * volume_step, 2);
         
         // 确保手数不为零
         volume_to_close = MathMax(volume_to_close, volume_step);
         
         // 确保不超过持仓总量
         volume_to_close = MathMin(volume_to_close, position_volume);
         
         Print("准备部分平仓: 总手数=", position_volume, ", 平仓手数=", volume_to_close);
         
         // 准备部分平仓请求
         MqlTradeRequest request = {};
         MqlTradeResult result = {};
         
         request.action = TRADE_ACTION_DEAL;
         request.position = position_ticket;
         request.symbol = position_symbol;
         request.volume = volume_to_close;
         request.type = (position_type == POSITION_TYPE_BUY) ? ORDER_TYPE_SELL : ORDER_TYPE_BUY;
         request.price = (position_type == POSITION_TYPE_BUY) ? current_bid : current_ask;
         request.deviation = 50;
         request.magic = 123456;
         request.comment = "部分止盈TP1";
         
         // 发送部分平仓请求
         bool success = OrderSend(request, result);
         
         // 处理结果
         if(success && result.retcode == TRADE_RETCODE_DONE)
         {
            Print("部分止盈成功! 订单号: ", result.order, ", 平仓手数: ", volume_to_close);
            
            // 标记需要移动到保本
            bool should_move_to_be = true;
            
            // 如果剩余仓位很小，可能不需要移动到保本，直接让它运行到最终止盈
            double remaining_volume = position_volume - volume_to_close;
            if(remaining_volume < volume_step || remaining_volume < 0.01)
            {
               Print("剩余仓位很小 (", remaining_volume, ")，不执行移动止损到保本");
               should_move_to_be = false;
            }
            
            // 如果需要移动到保本，立即执行
            if(should_move_to_be)
            {
               // 计算新的止损价格（保本+小缓冲）
               double new_sl = 0;
               if(position_type == POSITION_TYPE_BUY)
               {
                  new_sl = position_open_price + 2 * _Point; // 保本+2点
               }
               else
               {
                  new_sl = position_open_price - 2 * _Point; // 保本-2点
               }
               
               // 准备修改止损请求
               MqlTradeRequest sl_request = {};
               MqlTradeResult sl_result = {};
               
               sl_request.action = TRADE_ACTION_SLTP;
               sl_request.position = position_ticket;
               sl_request.symbol = position_symbol;
               sl_request.sl = new_sl;
               sl_request.tp = position_current_tp; // 保持原有止盈不变
               
               // 发送修改止损请求
               bool sl_success = OrderSend(sl_request, sl_result);
               
               if(sl_success && sl_result.retcode == TRADE_RETCODE_DONE)
               {
                  Print("止损已移动到保本! 新止损: ", new_sl);
               }
               else
               {
                  int error_code = GetLastError();
                  Print("移动止损失败! 错误代码: ", error_code, ", 错误描述: ", GetLastErrorDescription());
               }
            }
         }
         else
         {
            int error_code = GetLastError();
            Print("部分平仓失败! 错误代码: ", error_code, ", 错误描述: ", GetLastErrorDescription());
         }
      }
      // --- Step 2: Check and Move Stop Loss to Break-Even (BE) ---
      // 检查是否启用移动止损到保本
      if(!UseBreakEven)
         continue;
         
      // 计算初始风险距离（基于当前止损，假设未移动过）
      double initial_risk_distance_be = MathAbs(position_open_price - position_current_sl);
      
      // 计算触发移动止损到保本的盈亏比
      double trigger_rrr_for_be = MathMax(1.0, PartialTP_RRR); // 取1和TP1 RRR中较大的那个
      
      // 计算移动止损到保本的触发价格
      double be_trigger_price = 0;
      if(position_type == POSITION_TYPE_BUY)
      {
         be_trigger_price = position_open_price + initial_risk_distance_be * trigger_rrr_for_be;
      }
      else
      {
         be_trigger_price = position_open_price - initial_risk_distance_be * trigger_rrr_for_be;
      }
      
      // 检查价格是否达到移动止损到保本的触发价格
      if((position_type == POSITION_TYPE_BUY && current_bid < be_trigger_price) || 
         (position_type == POSITION_TYPE_SELL && current_ask > be_trigger_price))
      {
         Print("价格尚未达到移动止损到保本的触发价格: ", 
               position_type == POSITION_TYPE_BUY ? current_bid : current_ask, 
               " vs ", be_trigger_price);
         continue;
      }
      
      // 计算保本缓冲价格
      double be_buffer_price = BreakEvenBufferPoints * _Point;
      
      // 检查当前止损是否已经在保本或更好的位置
      if((position_type == POSITION_TYPE_BUY && position_current_sl >= position_open_price + be_buffer_price) || 
         (position_type == POSITION_TYPE_SELL && position_current_sl <= position_open_price - be_buffer_price))
      {
         Print("止损已经在保本或更好的位置，无需再次移动");
         continue;
      }
      
      // 计算新的保本止损位
      double new_be_sl = 0;
      if(position_type == POSITION_TYPE_BUY)
      {
         new_be_sl = NormalizeDouble(position_open_price + be_buffer_price, _Digits);
      }
      else
      {
         new_be_sl = NormalizeDouble(position_open_price - be_buffer_price, _Digits);
      }
      
      // 准备修改止损请求
      MqlTradeRequest be_request = {};
      MqlTradeResult be_result = {};
      
      be_request.action = TRADE_ACTION_SLTP;
      be_request.position = position_ticket;
      be_request.symbol = position_symbol;
      be_request.sl = new_be_sl;
      be_request.tp = position_current_tp; // 保持当前止盈不变
      be_request.magic = 123456;
      be_request.comment = "移动止损到保本";
      
      // 发送修改止损请求
      bool be_success = OrderSend(be_request, be_result);
      
      // 处理结果
      if(be_success && be_result.retcode == TRADE_RETCODE_DONE)
      {
         Print("成功移动止损到保本! 新止损: ", new_be_sl);
      }
      else
      {
         int error_code = GetLastError();
         Print("移动止损到保本失败! 错误代码: ", error_code, ", 错误描述: ", GetLastErrorDescription());
      }
      
      // --- Step 3: Check and Execute Trailing Stop Loss (TrailSL) ---
      // 检查是否启用追踪止损
      if(!UseTrailingStop || atr_value <= 0.0)
         continue;
         
      // 只有当止损位已经被移动到保本或更好时，才开始追踪
      // 注意：这里不要重新定义be_buffer_price变量，直接使用上面已定义的变量
      
      if((position_type == POSITION_TYPE_BUY && position_current_sl < position_open_price + be_buffer_price) || 
         (position_type == POSITION_TYPE_SELL && position_current_sl > position_open_price - be_buffer_price))
      {
         Print("止损尚未移动到保本位置，不执行追踪止损");
         continue;
      }
      
      // 计算新的潜在追踪止损位
      double new_trail_sl = 0;
      if(position_type == POSITION_TYPE_BUY)
      {
         new_trail_sl = NormalizeDouble(current_bid - atr_value * ATR_Multiplier, _Digits);
      }
      else
      {
         new_trail_sl = NormalizeDouble(current_ask + atr_value * ATR_Multiplier, _Digits);
      }
      
      // 检查是否需要更新SL：新的追踪止损位必须比当前的止损位更"优"
      if((position_type == POSITION_TYPE_BUY && new_trail_sl <= position_current_sl) || 
         (position_type == POSITION_TYPE_SELL && new_trail_sl >= position_current_sl))
      {
         Print("新的追踪止损位不优于当前止损位，不更新");
         continue;
      }
      
      // 准备修改SL请求
      MqlTradeRequest trail_request = {};
      MqlTradeResult trail_result = {};
      
      trail_request.action = TRADE_ACTION_SLTP;
      trail_request.position = position_ticket;
      trail_request.symbol = position_symbol;
      trail_request.sl = new_trail_sl;
      trail_request.tp = position_current_tp; // 保持当前TP不变
      trail_request.magic = 123456;
      trail_request.comment = "ATR追踪止损";
      
      // 发送修改止损请求
      bool trail_success = OrderSend(trail_request, trail_result);
      
      // 处理结果
      if(trail_success && trail_result.retcode == TRADE_RETCODE_DONE)
      {
         Print("成功更新追踪止损! 新止损: ", new_trail_sl, 
               ", 旧止损: ", position_current_sl, 
               ", ATR值: ", atr_value, 
               ", ATR倍数: ", ATR_Multiplier);
      }
      else
      {
         int error_code = GetLastError();
         Print("更新追踪止损失败! 错误代码: ", error_code, ", 错误描述: ", GetLastErrorDescription());
      }
   }
}

//+------------------------------------------------------------------+
//| 每个Tick触发的主函数                                              |
//+------------------------------------------------------------------+
void OnTick()
{
   // 首先管理现有持仓
   ManageOpenPositions();
   
   // 检查是否可以开新仓 (当前逻辑：只在没有持仓时开新仓)
   if(PositionsTotal() > 0 || (UsePendingOrder && OrdersTotal() > 0))
      return;  // 已有持仓或挂单，不再开新仓
      
   // 获取当前K线数据
   MqlRates rates[];
   ArraySetAsSeries(rates, true);
   if(CopyRates(_Symbol, PERIOD_CURRENT, 0, MathMax(3, LookbackPeriod + 5), rates) < MathMax(3, LookbackPeriod + 5))
      return;
        
   // 获取EMA20数据
   if(CopyBuffer(handle_ema20, 0, 0, 3, ema20_buffer) < 3)
      return;
      
   // 检查K线是否即将收盘
   static datetime last_processed_time = 0;
   datetime current_time = TimeCurrent();
   datetime bar_time = rates[0].time;
   
   // 计算当前K线的结束时间
   datetime bar_end_time = bar_time + PeriodSeconds(_Period);
   
   // 如果当前时间距离K线结束时间还有1秒或更少，且这根K线还没处理过，则开始处理
   bool is_about_to_close = (bar_end_time - current_time <= 1) && 
                           (bar_end_time > current_time) && 
                           (bar_time != last_processed_time);
   
   // 如果K线不是即将收盘，不执行交易逻辑
   if(!is_about_to_close)
      return;
   
   // 记录已处理的K线时间
   last_processed_time = bar_time;
   
   Print("检测到即将收盘的K线，时间: ", TimeToString(bar_time), "，还剩", (bar_end_time - current_time), "秒");
   
   // 识别最近的摆动高点和低点作为潜在的阻力位和支撑位
   double resistance_levels[];
   double support_levels[];
   DetectSwingSRLevels(rates, LookbackPeriod, SwingDefinitionBars, resistance_levels, support_levels, NumRecentPoints);
   
   // 分析当前市场结构
   MarketStructure current_market_structure = GetMarketStructure(resistance_levels, support_levels, 2);
   Print("当前市场结构: ", EnumToString(current_market_structure));
   
   // 可视化支撑位和阻力位
   // 首先删除所有现有的S/R水平线
   ObjectsDeleteAll(0, "SR_Level_");
   
   // 绘制阻力位 - 红色水平线
   for(int i = 0; i < ArraySize(resistance_levels); i++)
   {
      string obj_name = "SR_Level_R_" + IntegerToString(i);
      if(ObjectCreate(0, obj_name, OBJ_HLINE, 0, 0, resistance_levels[i]))
      {
         // 设置线条属性
         ObjectSetInteger(0, obj_name, OBJPROP_COLOR, clrRed);
         ObjectSetInteger(0, obj_name, OBJPROP_STYLE, STYLE_DASH);
         ObjectSetInteger(0, obj_name, OBJPROP_WIDTH, 1);
         ObjectSetInteger(0, obj_name, OBJPROP_BACK, true);  // 在背景中显示
         ObjectSetString(0, obj_name, OBJPROP_TOOLTIP, "阻力位 #" + IntegerToString(i+1) + ": " + DoubleToString(resistance_levels[i], _Digits));
      }
      else
      {
         Print("创建阻力位水平线失败: ", GetLastError());
      }
   }
   
   // 绘制支撑位 - 蓝色水平线
   for(int i = 0; i < ArraySize(support_levels); i++)
   {
      string obj_name = "SR_Level_S_" + IntegerToString(i);
      if(ObjectCreate(0, obj_name, OBJ_HLINE, 0, 0, support_levels[i]))
      {
         // 设置线条属性
         ObjectSetInteger(0, obj_name, OBJPROP_COLOR, clrBlue);
         ObjectSetInteger(0, obj_name, OBJPROP_STYLE, STYLE_DASH);
         ObjectSetInteger(0, obj_name, OBJPROP_WIDTH, 1);
         ObjectSetInteger(0, obj_name, OBJPROP_BACK, true);  // 在背景中显示
         ObjectSetString(0, obj_name, OBJPROP_TOOLTIP, "支撑位 #" + IntegerToString(i+1) + ": " + DoubleToString(support_levels[i], _Digits));
      }
      else
      {
         Print("创建支撑位水平线失败: ", GetLastError());
      }
   }
   
   // 检查是否是大阳线或大阴线 (使用rates[1]因为rates[0]是当前正在形成的K线)
   double body_size = MathAbs(rates[1].close - rates[1].open);
   double candle_size = rates[1].high - rates[1].low;
   bool is_big_candle = false;
   
   // 防止除零错误
   if(candle_size > 0)
   {
      // 计算K线实体占整体K线的比例
      double body_percent = body_size / candle_size;
      is_big_candle = body_percent >= BigCandlePercent;
      Print("K线实体占比: ", body_percent, ", 需要大于等于: ", BigCandlePercent);
   }
   else
   {
      // 如果K线高低点相同，则不考虑为大阳/阴线
      is_big_candle = false;
      Print("警告: K线高低点相同，跳过交易信号");
   }
   
   if(!is_big_candle)
   {
      Print("不是大阳/阴线，跳过交易信号");
      return;  // 不是大阳/阴线，不开仓
   }
      
   bool is_bullish = rates[1].close > rates[1].open;  // 是否是阳线
   Print("检测到", is_bullish ? "大阳线" : "大阴线");

   // EMA20过滤
   bool ema_condition = true;
   if(UseEMA20Filter)
   {
      if(is_bullish)
      {
         // 阳线收盘价在EMA20上方
         ema_condition = rates[1].close > ema20_buffer[1];
         Print("EMA20过滤 - 阳线收盘价: ", rates[1].close, ", EMA20: ", ema20_buffer[1], 
               ", 结果: ", ema_condition ? "通过" : "未通过");
      }
      else
      {
         // 阴线收盘价在EMA20下方
         ema_condition = rates[1].close < ema20_buffer[1];
         Print("EMA20过滤 - 阴线收盘价: ", rates[1].close, ", EMA20: ", ema20_buffer[1], 
               ", 结果: ", ema_condition ? "通过" : "未通过");
      }
   }
   
   if(!ema_condition)
   {
      Print("不满足EMA条件，跳过交易信号");
      return;  // 不满足EMA条件，不开仓
   }
      
   // 交易方向过滤
   if((is_bullish && !OnlyLong) || (!is_bullish && !OnlyShort))
   {
      Print("不符合交易方向设置，跳过交易信号");
      return;  // 不符合交易方向设置，不开仓
   }
   
   // 定义接近度阈值
   double proximity_threshold = 10 * _Point;
   
   // 支撑阻力位条件判断
   bool sr_condition = false;
   
   if(is_bullish)
   {
      // 多单条件：大阳线低点接近支撑位 或 大阳线实体收盘突破了阻力位
      sr_condition = (IsCloseToSupport(rates[1].low, support_levels, proximity_threshold)) || 
                     (DidBreakResistance(rates[1].close, rates[1].open, resistance_levels, proximity_threshold));
      
      if(sr_condition)
         Print("多单S/R条件满足: ", 
               IsCloseToSupport(rates[1].low, support_levels, proximity_threshold) ? "低点接近支撑位" : "收盘突破阻力位");
   }
   else
   {
      // 空单条件：大阴线高点接近阻力位 或 大阴线实体收盘跌破了支撑位
      sr_condition = (IsCloseToResistance(rates[1].high, resistance_levels, proximity_threshold)) || 
                     (DidBreakSupport(rates[1].close, rates[1].open, support_levels, proximity_threshold));
      
      if(sr_condition)
         Print("空单S/R条件满足: ", 
               IsCloseToResistance(rates[1].high, resistance_levels, proximity_threshold) ? "高点接近阻力位" : "收盘跌破支撑位");
   }
   
   if(!sr_condition)
   {
      Print("不满足S/R条件，跳过交易信号");
      return;  // 不满足S/R条件，不开仓
   }
   
   // --- 信号整合与最终决策 ---
   
   // 基础信号条件检查 (复用之前的变量)
   bool base_signal_conditions_met = is_big_candle && sr_condition; 
   if (!base_signal_conditions_met) {
      Print("基础信号条件(大K线或S/R)未满足");
      return; // 不满足基础条件则返回
   }
   
   // (可选调整) EMA 过滤 - 对于反转可能需要不同处理，暂时保留
   bool ema_filter_passed = true;
   if (UseEMA20Filter && !ema_condition) {
      ema_filter_passed = false;
      Print("EMA 过滤未通过");
      // 根据策略决定是否因EMA过滤而阻止反转交易，暂时阻止
      // if (!TradeReversals) return; // 仅突破模式下严格执行EMA过滤
      // 为了简单，暂时统一处理：EMA不过滤则都不交易
      return;
   }
   
   // (可选调整) 市场结构过滤
   bool structure_filter_passed = false;
   if (!TradeReversals) { // 原始突破逻辑需要匹配趋势
      // 市场结构过滤
      bool market_structure_matches = false;
      if(is_bullish && current_market_structure == TREND_UP)
      {
         market_structure_matches = true;
         Print("多头信号符合上升趋势");
      }
      else if(!is_bullish && current_market_structure == TREND_DOWN)
      {
         market_structure_matches = true;
         Print("空头信号符合下降趋势");
      }
      else
      {
         Print("信号方向与当前市场结构(", EnumToString(current_market_structure), ")不匹配");
      }
      
      structure_filter_passed = market_structure_matches; 
      if (!structure_filter_passed) Print("突破模式: 市场结构不匹配趋势");
   } else { // 反转逻辑
      // 最小改动：暂时允许在任何结构下测试反转
      structure_filter_passed = true; 
      // 或者: 如果希望只在盘整时反转，取消下面行的注释
      // structure_filter_passed = (current_market_structure == RANGE); 
      // if (!structure_filter_passed) Print("反转模式: 市场结构非盘整");
   }
   
   // 检查所有过滤器是否通过
   if (!structure_filter_passed) {
       return; // 结构过滤器未通过
   }
   
   // --- 确定最终交易方向 ---
   bool final_trade_is_bullish; // 最终是做多还是做空
   
   if (TradeReversals) {
       // === 反转逻辑 ===
       final_trade_is_bullish = !is_bullish; // **核心: 交易方向与大K线信号相反**
       Print("模式: 反转 | 信号K线方向: ", is_bullish ? "看涨" : "看跌", " | 交易方向: ", final_trade_is_bullish ? "做多" : "做空");
   } else {
       // === 原始突破逻辑 ===
       final_trade_is_bullish = is_bullish; // 保持原始方向
        Print("模式: 突破 | 信号K线方向: ", is_bullish ? "看涨" : "看跌", " | 交易方向: ", final_trade_is_bullish ? "做多" : "做空");
   }
   
   // --- 计算订单参数 ---
   double entry_price = 0;
   double stop_loss = 0;
   double take_profit = 0;
   
   // 调用新的函数来计算参数，区分突破和反转模式
   if (!CalculateOrderParams(rates[1], final_trade_is_bullish, TradeReversals, entry_price, stop_loss, take_profit)) {
        Print("计算订单参数失败，跳过交易");
        return; // 参数计算失败则不交易
   }
   
   // --- 计算交易手数 ---
   double trade_volume = LotSize;
   if(AutoLotSize)
   {
      // 获取账户结余
      double balance = AccountInfoDouble(ACCOUNT_BALANCE);
      
      // 根据账户结余范围设置交易手数
      if(balance < 2000)
         trade_volume = 0.01;  // 0-1999结余使用0.01手
      else if(balance < 3000)
         trade_volume = 0.02;  // 2000-2999结余使用0.02手
      else
      {
         // 3000以上结余，按照整数千位数计算手数
         // 例如：3000-3999为0.03手，12000-12999为0.12手，130000-130999为1.30手
         trade_volume = NormalizeDouble(MathFloor(balance / 1000.0) * LotSizePerK, 2);
      }
      
      // 确保最小交易手数为0.01
      if(trade_volume < 0.01)
         trade_volume = 0.01;
      
      Print("账户结余: ", balance, ", 自动计算交易手数: ", trade_volume);
   }
   
   // --- 执行下单 ---
   PlaceOrder(final_trade_is_bullish, entry_price, stop_loss, take_profit, trade_volume, UsePendingOrder, rates[1]);
}


//+------------------------------------------------------------------+
//| 检查价格是否接近支撑位                                            |
//+------------------------------------------------------------------+
bool IsCloseToSupport(double price, const double &levels[], double threshold)
{
   for(int i = 0; i < ArraySize(levels); i++)
   {
      if(MathAbs(price - levels[i]) <= threshold)
      {
         Print("价格 ", price, " 接近支撑位 ", levels[i], "，差距: ", MathAbs(price - levels[i]));
         return true;
      }
   }
   return false;
}

//+------------------------------------------------------------------+
//| 检查价格是否接近阻力位                                            |
//+------------------------------------------------------------------+
bool IsCloseToResistance(double price, const double &levels[], double threshold)
{
   for(int i = 0; i < ArraySize(levels); i++)
   {
      if(MathAbs(price - levels[i]) <= threshold)
      {
         Print("价格 ", price, " 接近阻力位 ", levels[i], "，差距: ", MathAbs(price - levels[i]));
         return true;
      }
   }
   return false;
}

//+------------------------------------------------------------------+
//| 检查K线是否突破阻力位                                             |
//+------------------------------------------------------------------+
bool DidBreakResistance(double close_price, double open_price, const double &levels[], double threshold)
{
   for(int i = 0; i < ArraySize(levels); i++)
   {
      if(close_price > levels[i] && open_price <= levels[i] + threshold)
      {
         Print("K线突破阻力位: 收盘价 ", close_price, " > 阻力位 ", levels[i], 
               "，开盘价 ", open_price, " <= 阻力位+阈值 ", (levels[i] + threshold));
         return true;
      }
   }
   return false;
}

//+------------------------------------------------------------------+
//| 检查K线是否跌破支撑位                                             |
//+------------------------------------------------------------------+
bool DidBreakSupport(double close_price, double open_price, const double &levels[], double threshold)
{
   for(int i = 0; i < ArraySize(levels); i++)
   {
      if(close_price < levels[i] && open_price >= levels[i] - threshold)
      {
         Print("K线跌破支撑位: 收盘价 ", close_price, " < 支撑位 ", levels[i], 
               "，开盘价 ", open_price, " >= 支撑位-阈值 ", (levels[i] - threshold));
         return true;
      }
   }
   return false;
}

//+------------------------------------------------------------------+
//| 获取错误描述                                                      |
//+------------------------------------------------------------------+
string GetLastErrorDescription()
{
   int error_code = GetLastError();
   string error_string;
   
   switch(error_code)
   {
      case 4051: // ERR_INVALID_PRICE
         error_string = "无效价格";
         break;
      case 4052: // ERR_INVALID_STOPS
         error_string = "无效止损/止盈";
         break;
      case 4063: // ERR_INVALID_TRADE_VOLUME
         error_string = "无效交易量";
         break;
      case 4109: // ERR_TRADE_NOT_ALLOWED
         error_string = "交易不允许";
         break;
      case 4114: // ERR_TRADE_DISABLED
         error_string = "交易被禁用";
         break;
      case 4060: // ERR_MARKET_CLOSED
         error_string = "市场关闭";
         break;
      case 4050: // ERR_INVALID_TRADE_PARAMETERS
         error_string = "无效交易参数";
         break;
      case 4102: // ERR_INVALID_EXPIRATION_DATE
         error_string = "无效过期日期";
         break;
      case 4112: // ERR_TRADE_TOO_MANY_ORDERS
         error_string = "订单数量过多";
         break;
      default:
         error_string = "未知错误 " + IntegerToString(error_code);
   }
   
   return error_string;
}

//+------------------------------------------------------------------+
//| 获取当前买入价                                                    |
//+------------------------------------------------------------------+
double Ask()
{
   MqlTick last_tick;
   SymbolInfoTick(_Symbol, last_tick);
   return last_tick.ask;
}

//+------------------------------------------------------------------+
//| 获取当前卖出价                                                    |
//+------------------------------------------------------------------+
double Bid()
{
   MqlTick last_tick;
   SymbolInfoTick(_Symbol, last_tick);
   return last_tick.bid;
}
//+------------------------------------------------------------------+