#ifndef INCLUDES_MQH
#define INCLUDES_MQH

#include "Parameters.mqh" // 包含参数定义
#include "QualitySwingPointsTrendline.mqh" // 包含波动点检测库
// #include "RobustRegression.mqh"       // 包含回归库 (已不再使用)
#include "SupportResistance.mqh"         // 包含支撑阻力水平识别库
#include "Utilities.mqh"                 // 将包含通用辅助函数
#include "RiskManagement.mqh"            // 将包含风险管理函数
#include "OrderManagement.mqh"           // 将包含订单和持仓管理函数
#include "Indicators.mqh"                // 将包含指标管理函数
#include "SignalGeneration.mqh"          // 将包含信号生成逻辑
#include "Visualization.mqh"            // 包含可视化模块
#include <Trade\Trade.mqh>              // 包含标准交易库
#include <Arrays/ArrayDouble.mqh>         // 如果有用到 CArrayDouble
#include <Arrays/ArrayString.mqh>         // 用于趋势线名称数组
// #include <Arrays/ArrayObj.mqh>       // 如果有用到 CArrayObj (可能在 .mqh 内部包含了)

#endif // INCLUDES_MQH