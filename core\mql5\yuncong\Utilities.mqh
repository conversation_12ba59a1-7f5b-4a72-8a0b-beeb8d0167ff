//+------------------------------------------------------------------+
//|                                                Utilities.mqh     |
//|                                                                  |
//|                                                                  |
//+------------------------------------------------------------------+
#ifndef UTILITIES_MQH
#define UTILITIES_MQH

#include "Parameters.mqh"
#include <Arrays/ArrayString.mqh>

//+------------------------------------------------------------------+
//| 检查是否为高成交量                                               |
//+------------------------------------------------------------------+
bool <PERSON>(const double &volume_values[], int index, int avg_period, double min_factor)
{
   if(avg_period <= 0 || index < 0 || index >= ArraySize(volume_values))
      return false;
      
   double avg_volume = 0;
   int count = 0;
   
   // 计算平均成交量(不包括当前K线)
   for(int i = index + 1; i < index + 1 + avg_period && i < ArraySize(volume_values); i++)
   {
      avg_volume += volume_values[i];
      count++;
   }
   
   if(count == 0)
      return false;
      
   avg_volume /= count;
   
   // 检查当前成交量是否超过平均值的指定倍数
   return (volume_values[index] >= avg_volume * min_factor);
}

//+------------------------------------------------------------------+
//| 统计当前持仓数量                                                 |
//+------------------------------------------------------------------+
int CountPositions()
{
   int count = 0;
   
   for(int i = PositionsTotal() - 1; i >= 0; i--)
   {
      ulong ticket = PositionGetTicket(i);
      if(ticket <= 0)
         continue;
         
      if(!PositionSelectByTicket(ticket))
         continue;
         
      // 检查魔术数字
      if(PositionGetInteger(POSITION_MAGIC) == inp_MagicNumber)
         count++;
   }
   
   return count;
}

//+------------------------------------------------------------------+
//| 绘制趋势线                                                       |
//+------------------------------------------------------------------+
bool DrawTrendline(const long chart_ID, const string object_name, 
                  datetime time1, double price1, 
                  datetime time2, double price2, 
                  const color line_color = clrRed, 
                  const ENUM_LINE_STYLE line_style = STYLE_SOLID, 
                  const int line_width = 1, 
                  const bool ray_right = false)
{
   // 创建趋势线对象
   if(!ObjectCreate(chart_ID, object_name, OBJ_TREND, 0, time1, price1, time2, price2))
   {
      Print("创建趋势线对象失败: ", GetLastError());
      return false;
   }
   
   // 设置线条属性
   ObjectSetInteger(chart_ID, object_name, OBJPROP_COLOR, line_color);
   ObjectSetInteger(chart_ID, object_name, OBJPROP_STYLE, line_style);
   ObjectSetInteger(chart_ID, object_name, OBJPROP_WIDTH, line_width);
   ObjectSetInteger(chart_ID, object_name, OBJPROP_RAY_RIGHT, ray_right);
   ObjectSetInteger(chart_ID, object_name, OBJPROP_BACK, true); // 放在背景
   ObjectSetInteger(chart_ID, object_name, OBJPROP_SELECTABLE, false); // 不可选择
   ObjectSetInteger(chart_ID, object_name, OBJPROP_SELECTED, false); // 不选中
   ObjectSetInteger(chart_ID, object_name, OBJPROP_HIDDEN, true); // 在对象列表中隐藏
   
   return true;
}

//+------------------------------------------------------------------+
//| 管理趋势线对象列表                                               |
//+------------------------------------------------------------------+
void ManageTrendlineObjects(CArrayString &trendline_names_array, int max_to_keep)
{
   int current_count = trendline_names_array.Total();
   // 如果当前数量超过限制
   while(current_count > max_to_keep && current_count > 0)
   {
      // 获取最旧的对象名称 (假设新名称总是在数组末尾添加)
      string oldest_name = trendline_names_array.At(0); // 取数组第一个元素
      // 从图表上删除对象
      ObjectDelete(0, oldest_name);
      // 从数组中移除该名称
      trendline_names_array.Delete(0);
      // 更新数量
      current_count = trendline_names_array.Total();
      Print("删除了最旧的趋势线对象: ", oldest_name);
   }
}

//+------------------------------------------------------------------+
//| 从回归参数计算特定点的回归值                                      |
//+------------------------------------------------------------------+
// 注意：此函数已在RobustRegression.mqh中定义，此处注释掉以避免重复定义
/*
double GetRegressionValue(double slope, double intercept, int x_index)
{
   return slope * x_index + intercept;
}
*/

#endif // UTILITIES_MQH