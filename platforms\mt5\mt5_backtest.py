"""
MT5回测模块
使用现有的分析框架和配置信息进行历史数据回测
包括持仓管理、止盈止损等功能
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
from platforms.mt5.mt5_position_manager import MT5PositionManager
import os
import time
from datetime import datetime, timedelta
import logging
import MetaTrader5 as mt5
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from tqdm import tqdm
import MetaTrader5 as mt5
import pandas as pd
import numpy as np
import logging
from datetime import datetime, timedelta
import time
from typing import Dict, List, Optional, Tuple, Union
import matplotlib.pyplot as plt
from matplotlib.dates import DateFormatter
import matplotlib.ticker as ticker
import matplotlib.patches as mpatches
from tqdm import tqdm

from platforms.mt5.mt5_unified_analyzer import MT5UnifiedMarketAnalyzer
from platforms.mt5.mt5_data import MT5DataFetcher
from platforms.mt5.mt5_config import (
    TIMEFRAME_MAP, 
    TRADING_CONFIG, 
    SIGNAL_CONFIG, 
    SYMBOLS, 
    TIMEFRAME_STRINGS,
    TIMEFRAME_CHECK_MAP,
    TIMEFRAME_COMMENT_MAP,
    TIMEFRAMES,
    POSITION_MANAGEMENT_CONFIG,
    adjust_atr_multiplier_by_timeframe
)
from utils.log_utils import setup_logger

# 设置日志
logger = setup_logger(
    'mt5_backtest', 
    os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), 'logs', 'mt5', 'backtest.log'),
    level=logging.INFO
)

class MT5Backtester:
    """MT5回测器"""
    
    def __init__(self, 
                 symbols: List[str] = None, 
                 timeframes: List[int] = None, 
                 start_date: datetime = None, 
                 end_date: datetime = None,
                 initial_balance: float = 10000.0,
                 direction: str = "both",
                 custom_sl_tp_settings: Dict = None):  # 添加自定义止盈止损参数
        """
        初始化回测器
        
        参数:
            symbols: 回测品种列表，如果为None则使用默认品种
            timeframes: 回测时间周期列表，如果为None则使用默认时间周期
            start_date: 回测开始日期，如果为None则使用当前日期前30天
            end_date: 回测结束日期，如果为None则使用当前日期
            initial_balance: 初始账户余额
            direction: 交易方向，可选值为 "long"、"short" 或 "both"
            custom_sl_tp_settings: 自定义止盈止损设置
        """
        # 初始化回测参数
        self.symbols = symbols if symbols is not None else SYMBOLS["default"]
        self.timeframes = timeframes if timeframes is not None else TIMEFRAMES
        self.start_date = start_date if start_date is not None else datetime.now() - timedelta(days=30)
        self.end_date = end_date if end_date is not None else datetime.now()
        self.initial_balance = initial_balance
        self.direction = direction
        self.custom_sl_tp_settings = custom_sl_tp_settings or {}
        
        # 初始化持仓管理器
        self.position_manager = MT5PositionManager()

        # 初始化数据获取器和分析器
        self.data_fetcher = MT5DataFetcher(symbols=self.symbols)
        self.analyzer = MT5UnifiedMarketAnalyzer(custom_symbols=self.symbols, direction=self.direction)
        
        # 初始化回测状态
        self.balance = initial_balance
        self.equity = initial_balance
        self.positions = []
        self.closed_positions = []
        self.trade_history = []
        self.daily_equity = []
        
        # 加载配置
        self.signal_config = SIGNAL_CONFIG
        self.trading_config = TRADING_CONFIG
        self.position_config = POSITION_MANAGEMENT_CONFIG
        
        # 初始化统计数据
        self.stats = {
            'total_trades': 0,
            'winning_trades': 0,
            'losing_trades': 0,
            'win_rate': 0.0,
            'profit_factor': 0.0,
            'max_drawdown': 0.0,
            'max_drawdown_percent': 0.0,
            'total_profit': 0.0,
            'total_loss': 0.0,
            'net_profit': 0.0,
            'return_percent': 0.0,
            'sharpe_ratio': 0.0,
            'avg_trade': 0.0,
            'avg_win': 0.0,
            'avg_loss': 0.0,
            'largest_win': 0.0,
            'largest_loss': 0.0,
            'max_consecutive_wins': 0,
            'max_consecutive_losses': 0
        }
        
        logger.info(f"回测器初始化完成: {len(self.symbols)}个品种, {len(self.timeframes)}个周期, 从{self.start_date}到{self.end_date}")
    
    def initialize_mt5(self) -> bool:
        """初始化MT5连接"""
        try:
            if not mt5.initialize():
                logger.error("MT5初始化失败")
                return False
            return True
        except Exception as e:
            logger.error(f"MT5初始化出错: {str(e)}")
            return False
    
    def fetch_historical_data(self, symbol: str, timeframe: int, start_date: datetime, end_date: datetime) -> pd.DataFrame:
        """获取历史数据，支持分批获取"""
        try:
            # 设置分批获取的参数
            max_retries = 3   # 最大重试次数
            batch_days = {
                mt5.TIMEFRAME_M1: 5,      # 1分钟图，每5天一批
                mt5.TIMEFRAME_M5: 20,     # 5分钟图，每20天一批
                mt5.TIMEFRAME_M15: 60,    # 15分钟图，每60天一批
                mt5.TIMEFRAME_M30: 90,    # 30分钟图，每90天一批
                mt5.TIMEFRAME_H1: 180,    # 1小时图，每180天一批
                mt5.TIMEFRAME_H4: 365,    # 4小时图，每365天一批
                mt5.TIMEFRAME_D1: 730,    # 日线图，每730天一批
            }.get(timeframe, 30)          # 默认每30天一批
            
            # 检查MT5是否已初始化
            if not mt5.terminal_info():
                logger.error(f"获取{symbol} {timeframe}历史数据失败: MT5未初始化")
                return None
                
            # 检查品种是否存在
            symbol_info = mt5.symbol_info(symbol)
            if symbol_info is None:
                logger.error(f"获取{symbol} {timeframe}历史数据失败: 品种{symbol}不存在")
                # 输出所有可用品种
                symbols = mt5.symbols_get()
                available_symbols = [s.name for s in symbols]
                logger.info(f"可用品种: {available_symbols[:10]}... (共{len(available_symbols)}个)")
                return None
                
            # 输出时间周期信息
            logger.debug(f"尝试获取{symbol}的历史数据，时间周期为{timeframe}，对应的MT5常量值")
            for tf_name, tf_value in vars(mt5).items():
                if tf_name.startswith('TIMEFRAME_') and tf_value == timeframe:
                    logger.debug(f"使用的时间周期常量: {tf_name} = {tf_value}")
                    break
            
            # 初始化结果DataFrame
            all_data = []
            
            # 计算时间批次
            current_start = start_date
            while current_start < end_date:
                # 计算当前批次的结束时间
                current_end = min(current_start + timedelta(days=batch_days), end_date)
                
                # 转换为MT5时间戳
                start_timestamp = int(current_start.timestamp())
                end_timestamp = int(current_end.timestamp())
                
                # 重试机制
                for retry in range(max_retries):
                    try:
                        # 获取历史数据
                        logger.info(f"尝试获取{symbol} {timeframe}历史数据: {current_start.date()} 到 {current_end.date()}")
                        rates = mt5.copy_rates_range(symbol, timeframe, start_timestamp, end_timestamp)
                        
                        if rates is not None and len(rates) > 0:
                            # 转换为DataFrame并添加到结果中
                            df_batch = pd.DataFrame(rates)
                            df_batch['time'] = pd.to_datetime(df_batch['time'], unit='s')
                            all_data.append(df_batch)
                            logger.info(f"获取{symbol} {timeframe}历史数据: {current_start.date()} 到 {current_end.date()}, {len(df_batch)}条记录")
                            break
                        else:
                            # 获取最后错误信息
                            error_code = mt5.last_error()
                            error_details = f"错误代码: {error_code[0]}, 描述: {error_code[1]}"
                            logger.warning(f"获取{symbol} {timeframe}历史数据失败，重试 {retry+1}/{max_retries}, {error_details}")
                            time.sleep(1)  # 等待1秒后重试
                    except Exception as e:
                        logger.warning(f"获取{symbol} {timeframe}历史数据出错: {str(e)}，重试 {retry+1}/{max_retries}")
                        # 获取详细的异常信息
                        import traceback
                        logger.debug(f"异常详情: {traceback.format_exc()}")
                        time.sleep(1)  # 等待1秒后重试
                
                # 更新下一批次的开始时间
                current_start = current_end
            
            # 合并所有批次的数据
            if not all_data:
                logger.error(f"获取{symbol} {timeframe}历史数据失败")
                return None
            
            df = pd.concat(all_data)
            df = df.drop_duplicates(subset='time')  # 去除可能的重复数据
            df.set_index('time', inplace=True)
            df.sort_index(inplace=True)
            
            logger.info(f"获取{symbol} {timeframe}历史数据完成: 共{len(df)}条记录")
            return df
        except Exception as e:
            logger.error(f"获取历史数据出错: {str(e)}")
            # 获取详细的异常信息
            import traceback
            logger.error(f"异常详情: {traceback.format_exc()}")
            return None
    
    def calculate_atr(self, data: pd.DataFrame, period: int = 14) -> pd.Series:
        """计算ATR"""
        try:
            high = data['high']
            low = data['low']
            close = data['close'].shift(1)
            
            tr1 = high - low
            tr2 = abs(high - close)
            tr3 = abs(low - close)
            
            tr = pd.DataFrame({'tr1': tr1, 'tr2': tr2, 'tr3': tr3}).max(axis=1)
            atr = tr.rolling(period).mean()
            
            return atr
        except Exception as e:
            logger.error(f"计算ATR出错: {str(e)}")
            return None
    
    def analyze_bar(self, data: pd.DataFrame, current_index: int, symbol: str, timeframe: int) -> Optional[Dict]:
        """分析当前K线，返回交易信号"""
        try:
            # 获取当前时间之前的数据
            historical_data = data.iloc[:current_index+1].copy()
            
            # 使用统一分析器分析
            signals = self.analyzer.pattern_analyzer.detect_trading_signals(historical_data)
            
            if signals:
                # 只取最新的信号
                signal = signals[-1]
                signal.update({
                    'symbol': symbol,
                    'timeframe': timeframe,
                    'timestamp': historical_data.index[-1]
                })
                return signal
            
            return None
        except Exception as e:
            logger.error(f"分析K线出错: {str(e)}")
            return None

    def calculate_sl_tp(self, signal, data, current_index):
        """计算止损止盈价格"""
        try:
            symbol = signal['symbol']
            direction = signal['direction']
            current_bar = data.iloc[current_index]
            entry_price = current_bar['close']
            
            # 获取ATR值用于设置止损
            atr_value = current_bar.get('atr', 0)
            if atr_value == 0:
                # 如果没有ATR值，使用最近20根K线的高低点差的平均值的一定比例
                # 这里将df改为data
                recent_bars = data.iloc[max(0, current_index-20):current_index+1]
                atr_value = (recent_bars['high'] - recent_bars['low']).mean() * 0.5
            
            # 检查是否有自定义止盈止损设置
            custom_settings = None
            if self.custom_sl_tp_settings and symbol in self.custom_sl_tp_settings:
                custom_settings = self.custom_sl_tp_settings[symbol].get('default', None)
            
            # 根据自定义设置或默认规则计算止损止盈
            if custom_settings:
                # 使用自定义设置
                sl_type = custom_settings.get('sl_type', 'atr')
                tp_type = custom_settings.get('tp_type', 'atr')
                
                # 计算止损
                if sl_type == 'fixed':
                    # 固定点数止损
                    sl_points = custom_settings.get('sl_fixed_points', 10)
                    if direction == 'LONG':
                        sl_price = entry_price - (sl_points * 0.0001)
                    else:
                        sl_price = entry_price + (sl_points * 0.0001)
                elif sl_type == 'percent':
                    # 百分比止损
                    sl_percent = custom_settings.get('sl_percent', 1.0) / 100
                    if direction == 'LONG':
                        sl_price = entry_price * (1 - sl_percent)
                    else:
                        sl_price = entry_price * (1 + sl_percent)
                else:
                    # 默认使用ATR止损
                    sl_atr_multiple = custom_settings.get('sl_atr_multiple', 2.0)
                    if direction == 'LONG':
                        sl_price = entry_price - (atr_value * sl_atr_multiple)
                    else:
                        sl_price = entry_price + (atr_value * sl_atr_multiple)
                
                # 计算止盈
                if tp_type == 'fixed':
                    # 固定点数止盈
                    tp_points = custom_settings.get('tp_fixed_points', 20)
                    if direction == 'LONG':
                        tp_price = entry_price + (tp_points * 0.0001)
                    else:
                        tp_price = entry_price - (tp_points * 0.0001)
                elif tp_type == 'percent':
                    # 百分比止盈
                    tp_percent = custom_settings.get('tp_percent', 2.0) / 100
                    if direction == 'LONG':
                        tp_price = entry_price * (1 + tp_percent)
                    else:
                        tp_price = entry_price * (1 - tp_percent)
                else:
                    # 默认使用ATR止盈
                    tp_atr_multiple = custom_settings.get('tp_atr_multiple', 3.0)
                    if direction == 'LONG':
                        tp_price = entry_price + (atr_value * tp_atr_multiple)
                    else:
                        tp_price = entry_price - (atr_value * tp_atr_multiple)
            else:
                # 使用默认规则
                # 多单：止损设置在入场价下方2个ATR，止盈设置在入场价上方3个ATR
                # 空单：止损设置在入场价上方2个ATR，止盈设置在入场价下方3个ATR
                if direction == 'LONG':
                    sl_price = entry_price - (atr_value * 2)
                    tp_price = entry_price + (atr_value * 3)
                else:
                    sl_price = entry_price + (atr_value * 2)
                    tp_price = entry_price - (atr_value * 3)
            
            return sl_price, tp_price
            
        except Exception as e:
            logger.error(f"计算止损止盈价格出错: {str(e)}")
            # 返回默认值
            if direction == 'LONG':
                return entry_price * 0.98, entry_price * 1.03
            else:
                return entry_price * 1.02, entry_price * 0.97

    def calculate_position_size(self, symbol: str, entry_price: float) -> float:
        """计算仓位大小"""
        try:
            # 获取品种配置
            symbol_config = self.trading_config['symbols'].get(symbol, {})
            
            # 获取风险百分比
            risk_percent = symbol_config.get('risk_percent', self.trading_config.get('default_risk_percent', 1.0))
            
            # 计算风险金额
            risk_amount = self.balance * (risk_percent / 100)
            
            # 根据不同品种计算仓位大小
            if 'XAU' in symbol:  # 黄金
                # 黄金每0.01美元变动，1手盈亏1美元，1手=100盎司
                # 假设止损为20美元，则风险金额/20 = 手数
                lot_size = risk_amount / 20 / 100
            elif 'BTC' in symbol:  # 比特币
                # 比特币每1美元变动，1手盈亏1美元
                # 假设止损为500美元，则风险金额/500 = 手数
                lot_size = risk_amount / 500
            else:  # 外汇对
                # 外汇每0.0001变动，1手盈亏10美元，1手=100,000单位
                # 假设止损为50点，则风险金额/(50*10) = 手数
                lot_size = risk_amount / (50 * 10)
            
            # 限制最小和最大仓位
            min_lot = symbol_config.get('min_lot', 0.01)
            max_lot = symbol_config.get('max_lot', 10.0)
            lot_size = max(min_lot, min(lot_size, max_lot))
            
            # 四舍五入到合适的精度
            lot_precision = symbol_config.get('lot_precision', 2)
            lot_size = round(lot_size, lot_precision)
            
            return lot_size
            
        except Exception as e:
            logger.error(f"计算仓位大小出错: {str(e)}")
            return 0.01  # 返回最小仓位

    def open_position(self, signal: Dict, data: pd.DataFrame, current_index: int) -> bool:
        """开仓"""
        try:
            # 获取当前K线数据
            current_bar = data.iloc[current_index]
            symbol = signal['symbol']
            timeframe = signal['timeframe']
            direction = signal['direction']
            signal_type = signal['type']
            current_time = current_bar.name
            
            # 计算开仓价格（使用收盘价作为开仓价）
            entry_price = current_bar['close']
            
            # 获取ATR值用于设置止损
            atr_value = current_bar.get('atr', 0)
            if atr_value == 0:
                # 如果没有ATR值，使用最近20根K线的高低点差的平均值的一定比例
                recent_bars = data.iloc[max(0, current_index-20):current_index+1]
                atr_value = (recent_bars['high'] - recent_bars['low']).mean() * 0.5
            
            # 计算仓位大小
            lot_size = self.calculate_position_size(symbol, entry_price)
            
            # 计算止损止盈价格
            sl_price, tp_price = self.calculate_sl_tp(signal, data, current_index)
            
            # 如果持仓管理器没有返回有效的止盈止损价格，则使用默认计算方法
            if sl_price is None or tp_price is None:
                # 根据信号类型和时间周期调整ATR乘数
                signal_params = self.signal_config.get(signal_type, self.signal_config['default'])
                initial_stop_multiplier = signal_params['initial_stop']
                adjusted_multiplier = adjust_atr_multiplier_by_timeframe(initial_stop_multiplier, timeframe)
                
                # 计算止损距离
                stop_distance = atr_value * adjusted_multiplier
                
                # 计算止损价和止盈价
                if direction == 'LONG':
                    sl_price = entry_price - stop_distance
                    tp_price = entry_price + (stop_distance * signal_params['take_profit'] / signal_params['initial_stop'])
                else:  # SHORT
                    sl_price = entry_price + stop_distance
                    tp_price = entry_price - (stop_distance * signal_params['take_profit'] / signal_params['initial_stop'])
            # 获取仓位大小
            symbol_config = self.trading_config['symbols'].get(symbol, {})
            timeframe_config = symbol_config.get('timeframes', {}).get(timeframe, {})
            position_sequence = timeframe_config.get('sequence', [0.1, 0.1, 0.1])
            
            # 获取当前品种当前方向的持仓数量
            current_positions = [p for p in self.positions if p['symbol'] == symbol and p['direction'] == direction]
            position_index = len(current_positions)
            
            # 检查是否超过最大持仓数量
            if position_index >= self.trading_config['max_positions']:
                logger.info(f"已达到最大持仓数量: {symbol} {direction}")
                return False
            
            # 获取仓位大小
            lot_size = position_sequence[position_index] if position_index < len(position_sequence) else position_sequence[-1]
            
            # 创建持仓记录
            position = {
                'ticket': len(self.positions) + 1,
                'symbol': symbol,
                'timeframe': timeframe,
                'direction': direction,
                'signal_type': signal_type,
                'entry_price': entry_price,
                'stop_loss': sl_price,
                'take_profit': tp_price,
                'lot_size': lot_size,
                'open_time': current_time,
                'close_time': None,
                'profit': 0.0,
                'status': 'open',
                'atr_value': current_bar.get('atr', 0),
                'comment': f"{signal_type}_{timeframe}_{direction[0]}"
            }
            
            # 添加到持仓列表
            self.positions.append(position)
            
            # 记录交易历史
            trade_record = {
                'time': current_time,  # 将timestamp改为current_time
                'action': 'open',
                'symbol': symbol,
                'direction': direction,
                'price': entry_price,
                'lot_size': lot_size,
                'balance': self.balance,
                'equity': self.equity
            }
            self.trade_history.append(trade_record)
            
            logger.info(f"开仓: {symbol} {direction} 价格:{entry_price} 止损:{sl_price} 止盈:{tp_price} 手数:{lot_size}")  # 将stop_loss和take_profit改为sl_price和tp_price
            return True
        except Exception as e:
            logger.error(f"开仓出错: {str(e)}")
            return False
    
    def update_positions(self, data: pd.DataFrame, current_index: int, current_time: datetime) -> None:
        """更新持仓状态"""
        try:
            if not self.positions:
                return
            
            # 获取当前K线数据
            current_bar = data.iloc[current_index]
            
            # 更新每个持仓
            for position in self.positions[:]:  # 使用副本遍历，以便可以在循环中修改原列表
                if position['status'] != 'open':
                    continue
                
                symbol = position['symbol']
                direction = position['direction']
                entry_price = position['entry_price']
                stop_loss = position['stop_loss']
                take_profit = position['take_profit']
                lot_size = position['lot_size']
                
                # 计算当前价格
                current_price = current_bar['close']
                
                # 计算浮动盈亏
                symbol = position['symbol']
                if 'XAU' in symbol:  # 黄金
                    if direction == 'LONG':
                        profit = (current_price - entry_price) * lot_size * 100
                    else:  # SHORT
                        profit = (entry_price - current_price) * lot_size * 100
                elif 'BTC' in symbol:  # 比特币
                    if direction == 'LONG':
                        profit = (current_price - entry_price) * lot_size
                    else:  # SHORT
                        profit = (entry_price - current_price) * lot_size
                else:  # 外汇对
                    if direction == 'LONG':
                        profit_pips = (current_price - entry_price) * 10000
                    else:  # SHORT
                        profit_pips = (entry_price - current_price) * 10000
                    profit = profit_pips * lot_size * 10
                
                position['profit'] = profit
                
                # 检查是否触发止损或止盈
                if direction == 'LONG':
                    if current_bar['low'] <= stop_loss:  # 触发止损
                        self.close_position(position, stop_loss, current_time, 'stop_loss')
                    elif current_bar['high'] >= take_profit:  # 触发止盈
                        self.close_position(position, take_profit, current_time, 'take_profit')
                else:  # SHORT
                    if current_bar['high'] >= stop_loss:  # 触发止损
                        self.close_position(position, stop_loss, current_time, 'stop_loss')
                    elif current_bar['low'] <= take_profit:  # 触发止盈
                        self.close_position(position, take_profit, current_time, 'take_profit')
                
                # 应用持仓管理策略
                self.apply_position_management(position, current_bar, current_time)
            
            # 更新账户权益
            self.update_equity()
            
            # 记录每日权益
            self.daily_equity.append({
                'time': current_time,
                'equity': self.equity
            })
            
        except Exception as e:
            logger.error(f"更新持仓状态出错: {str(e)}")
    
    def close_position(self, position: Dict, close_price: float, close_time: datetime, reason: str) -> bool:
        """平仓"""
        try:
            if position['status'] != 'open':
                return False
            
            # 计算盈亏
            direction = position['direction']
            entry_price = position['entry_price']
            lot_size = position['lot_size']
            symbol = position['symbol']
            
            # 根据不同品种计算盈亏
            if 'XAU' in symbol:  # 黄金
                # 黄金每0.01美元变动，1手盈亏1美元，1手=100盎司
                if direction == 'LONG':
                    profit = (close_price - entry_price) * lot_size * 100
                else:  # SHORT
                    profit = (entry_price - close_price) * lot_size * 100
            elif 'BTC' in symbol:  # 比特币
                # 比特币每1美元变动，1手盈亏1美元
                if direction == 'LONG':
                    profit = (close_price - entry_price) * lot_size
                else:  # SHORT
                    profit = (entry_price - close_price) * lot_size
            else:  # 外汇对
                # 外汇每0.0001变动，1手盈亏10美元，1手=100,000单位
                if direction == 'LONG':
                    profit_pips = (close_price - entry_price) * 10000
                else:  # SHORT
                    profit_pips = (entry_price - close_price) * 10000
                profit = profit_pips * lot_size * 10
            
            # 更新持仓状态
            position['status'] = 'closed'
            position['close_price'] = close_price
            position['close_time'] = close_time
            position['profit'] = profit
            position['close_reason'] = reason
            
            # 更新账户余额
            self.balance += profit
            
            # 添加到已平仓列表
            self.closed_positions.append(position)
            
            # 从持仓列表中移除
            self.positions.remove(position)
            
            # 记录交易历史
            trade_record = {
                'time': close_time,
                'action': 'close',
                'symbol': position['symbol'],
                'direction': direction,
                'price': close_price,
                'lot_size': lot_size,
                'profit': profit,
                'reason': reason,
                'balance': self.balance,
                'equity': self.equity
            }
            self.trade_history.append(trade_record)
            
            logger.info(f"平仓: {position['symbol']} {direction} 价格:{close_price} 盈亏:{profit} 原因:{reason}")
            return True
        except Exception as e:
            logger.error(f"平仓出错: {str(e)}")
            return False
    
    def apply_position_management(self, position: Dict, current_bar: pd.Series, current_time: datetime) -> None:
        """应用持仓管理策略"""
        try:
            if position['status'] != 'open':
                return
            
            symbol = position['symbol']
            direction = position['direction']
            entry_price = position['entry_price']
            stop_loss = position['stop_loss']
            take_profit = position['take_profit']
            atr_value = position['atr_value']
            
            # 获取当前价格
            current_price = current_bar['close']
            
            # 获取持仓管理配置
            # 修复这里的配置获取逻辑
            if not hasattr(self, 'position_config') or not self.position_config:
                # 如果position_config不存在或为空，使用默认配置
                symbol_config = {
                    'partial_close': {'enabled': False},
                    'trailing_stop': {'enabled': False}
                }
            else:
                # 检查position_config的结构
                if 'symbols' not in self.position_config:
                    # 如果没有symbols键，使用整个配置作为symbol_config
                    symbol_config = self.position_config.get(symbol, self.position_config.get('default', {}))
                else:
                    # 正常获取symbol配置
                    symbol_config = self.position_config['symbols'].get(
                        symbol, self.position_config['symbols'].get('default', {})
                    )
            
            # 计算当前盈利比例（以ATR为单位）
            if direction == 'LONG':
                profit_distance = current_price - entry_price
                price_percent_profit = (profit_distance / entry_price) * 100
            else:  # SHORT
                profit_distance = entry_price - current_price
                price_percent_profit = (profit_distance / entry_price) * 100
            
            atr_ratio = profit_distance / atr_value if atr_value > 0 else 0
            
            # 检查是否应该部分平仓
            if self.should_partial_close(position, symbol_config, atr_value, atr_ratio, price_percent_profit):
                self.execute_partial_close(position, symbol_config, atr_value, atr_ratio, price_percent_profit, current_time)
            
            # 检查是否应该移动止损
            self.update_trailing_stop(position, atr_value, atr_ratio, current_price, current_time)
            
        except Exception as e:
            logger.error(f"应用持仓管理策略出错: {str(e)}")
    
    def should_partial_close(self, position: Dict, symbol_config: Dict, atr_value: float, atr_ratio: float, price_percent_profit: float) -> bool:
        """检查是否应该部分平仓"""
        try:
            # 检查配置是否存在
            if not symbol_config:
                return False
                
            # 检查是否启用部分平仓
            partial_close_config = symbol_config.get('partial_close', {})
            if not partial_close_config.get('enabled', False):
                return False
            
            # 获取部分平仓阶段
            stages = partial_close_config.get('stages', [])
            if not stages:
                return False
            
            # 获取持仓ID
            position_id = position['ticket']
            
            # 检查历史记录，避免重复平仓
            if not hasattr(self, 'partial_close_history'):
                self.partial_close_history = {}
            
            if position_id not in self.partial_close_history:
                self.partial_close_history[position_id] = set()
            
            # 检查每个阶段
            for i, stage in enumerate(stages):
                # 检查是否已经执行过该阶段的平仓
                if i in self.partial_close_history[position_id]:
                    continue
                
                # 检查是否达到平仓条件
                if partial_close_config.get('use_percentage', False):
                    # 使用价格百分比（适用于加密货币）
                    threshold = stage.get('profit_percent', 0)
                    if price_percent_profit >= threshold:
                        logger.info(f"达到部分平仓条件: Ticket {position_id}, 阶段 {i+1}, "
                                   f"盈利百分比 {price_percent_profit:.2f}% >= 阈值 {threshold:.2f}%")
                        return True
                else:
                    # 使用ATR倍数（适用于传统品种）
                    threshold = stage.get('profit_ratio', 0)
                    if atr_ratio >= threshold:
                        logger.info(f"达到部分平仓条件: Ticket {position_id}, 阶段 {i+1}, "
                                   f"ATR比例 {atr_ratio:.2f} >= 阈值 {threshold:.2f}")
                        return True
            
            return False
            
        except Exception as e:
            logger.error(f"检查部分平仓条件出错: {str(e)}")
            return False
    
    def execute_partial_close(self, position: Dict, symbol_config: Dict, atr_value: float, atr_ratio: float, price_percent_profit: float, current_time: datetime) -> bool:
        """执行部分平仓"""
        try:
            # 获取部分平仓配置
            partial_close_config = symbol_config.get('partial_close', {})
            stages = partial_close_config.get('stages', [])
            
            # 获取持仓ID
            position_id = position['ticket']
            
            # 确定当前阶段
            current_stage = None
            stage_index = None
            
            for i, stage in enumerate(stages):
                # 跳过已执行的阶段
                if i in self.partial_close_history[position_id]:
                    continue
                
                # 检查是否达到平仓条件
                if partial_close_config.get('use_percentage', False):
                    # 使用价格百分比
                    threshold = stage.get('profit_percent', 0)
                    if price_percent_profit >= threshold:
                        current_stage = stage
                        stage_index = i
                        break
                else:
                    # 使用ATR倍数
                    threshold = stage.get('profit_ratio', 0)
                    if atr_ratio >= threshold:
                        current_stage = stage
                        stage_index = i
                        break
            
            if current_stage is None:
                logger.warning(f"无法确定部分平仓阶段: Ticket {position_id}")
                return False
            
            # 计算平仓量
            close_percent = current_stage.get('close_percent', 0)
            volume_to_close = position['lot_size'] * (close_percent / 100)
            
            # 确保平仓量不小于最小交易量
            min_volume = 0.01  # 假设最小交易量为0.01
            if volume_to_close < min_volume:
                volume_to_close = min_volume
            
            # 获取当前价格
            current_price = position['entry_price']
            if position['direction'] == 'LONG':
                current_price += atr_value * atr_ratio
            else:
                current_price -= atr_value * atr_ratio
            
            # 根据不同品种计算盈亏
            symbol = position['symbol']
            if 'XAU' in symbol:  # 黄金
                if position['direction'] == 'LONG':
                    partial_profit = (current_price - position['entry_price']) * volume_to_close * 100
                else:  # SHORT
                    partial_profit = (position['entry_price'] - current_price) * volume_to_close * 100
            elif 'BTC' in symbol:  # 比特币
                if position['direction'] == 'LONG':
                    partial_profit = (current_price - position['entry_price']) * volume_to_close
                else:  # SHORT
                    partial_profit = (position['entry_price'] - current_price) * volume_to_close
            else:  # 外汇对
                if position['direction'] == 'LONG':
                    profit_pips = (current_price - position['entry_price']) * 10000
                else:  # SHORT
                    profit_pips = (position['entry_price'] - current_price) * 10000
                partial_profit = profit_pips * volume_to_close * 10
            # 创建部分平仓记录
            partial_position = position.copy()
            partial_position['ticket'] = len(self.closed_positions) + 1000  # 使用新的ticket
            partial_position['lot_size'] = volume_to_close
            partial_position['close_price'] = current_price
            partial_position['close_time'] = current_time
            partial_position['close_reason'] = f'partial_close_stage_{stage_index+1}'
            partial_position['profit'] = partial_profit
            partial_position['status'] = 'closed'
            
            # 更新原持仓
            position['lot_size'] -= volume_to_close
            
            # 添加到已平仓列表
            self.closed_positions.append(partial_position)
            
            # 更新账户余额
            self.balance += partial_profit
            
            # 记录交易历史
            trade_record = {
                'time': current_time,
                'action': 'partial_close',
                'symbol': position['symbol'],
                'direction': position['direction'],
                'price': current_price,
                'lot_size': volume_to_close,
                'profit': partial_profit,
                'reason': f'partial_close_stage_{stage_index+1}',
                'balance': self.balance,
                'equity': self.equity
            }
            self.trade_history.append(trade_record)
            
            # 记录已执行的阶段
            self.partial_close_history[position_id].add(stage_index)
            
            # 如果需要移动止损到保本位置
            if current_stage.get('move_sl_to_breakeven', False):
                # 计算新的止损位置（略高于保本点，给予一些缓冲）
                buffer = atr_value * 0.2 if not partial_close_config.get('use_percentage', False) else position['entry_price'] * 0.001
                
                if position['direction'] == 'LONG':
                    # 多单，止损设置在入场价上方一点
                    new_sl = position['entry_price'] + buffer
                    if new_sl > position['stop_loss']:
                        position['stop_loss'] = new_sl
                        logger.info(f"移动止损到保本位置: Ticket {position_id}, 新止损 {new_sl}")
                else:
                    # 空单，止损设置在入场价下方一点
                    new_sl = position['entry_price'] - buffer
                    if new_sl < position['stop_loss']:
                        position['stop_loss'] = new_sl
                        logger.info(f"移动止损到保本位置: Ticket {position_id}, 新止损 {new_sl}")
            
            # 如果需要为剩余仓位启用跟踪止损
            if current_stage.get('trailing_stop', False):
                position['trailing_enabled'] = True
                if partial_close_config.get('use_percentage', False):
                    position['trailing_distance'] = current_stage.get('trailing_percent', 0.5) / 100 * position['entry_price']
                else:
                    position['trailing_distance'] = atr_value * current_stage.get('trailing_distance', 1.0)
                
                logger.info(f"为剩余仓位启用跟踪止损: Ticket {position_id}, 跟踪距离 {position['trailing_distance']}")
            
            return True
            
        except Exception as e:
            logger.error(f"执行部分平仓出错: {str(e)}")
            return False
    
    def update_trailing_stop(self, position: Dict, atr_value: float, atr_ratio: float, current_price: float, current_time: datetime) -> None:
        """更新移动止损"""
        try:
            # 检查position_config是否存在
            if not hasattr(self, 'position_config') or not self.position_config:
                return
                
            # 获取symbol_config
            if 'symbols' not in self.position_config:
                symbol_config = self.position_config.get(position['symbol'], self.position_config.get('default', {}))
            else:
                symbol_config = self.position_config['symbols'].get(
                    position['symbol'], self.position_config['symbols'].get('default', {})
                )
                
            # 检查是否启用移动止损
            trailing_config = symbol_config.get('trailing_stop', {})
            if not trailing_config.get('enabled', False):
                return
                
            
            direction = position['direction']
            stop_loss = position['stop_loss']
            trailing_distance = position.get('trailing_distance', atr_value)
            
            # 计算新的止损位置
            if direction == 'LONG':
                new_stop_loss = current_price - trailing_distance
                # 只有当新止损高于当前止损时才更新
                if new_stop_loss > stop_loss:
                    position['stop_loss'] = new_stop_loss
                    logger.info(f"更新跟踪止损: Ticket {position['ticket']}, 新止损 {new_stop_loss}")
            else:  # SHORT
                new_stop_loss = current_price + trailing_distance
                # 只有当新止损低于当前止损时才更新
                if new_stop_loss < stop_loss:
                    position['stop_loss'] = new_stop_loss
                    logger.info(f"更新跟踪止损: Ticket {position['ticket']}, 新止损 {new_stop_loss}")
                    
        except Exception as e:
            logger.error(f"更新跟踪止损出错: {str(e)}")
    
    def update_equity(self) -> None:
        """更新账户权益"""
        try:
            # 计算所有持仓的浮动盈亏
            floating_profit = sum(position['profit'] for position in self.positions if position['status'] == 'open')
            
            # 更新权益
            self.equity = self.balance + floating_profit
            
            # 检查是否爆仓（权益低于初始资金的一定比例，例如10%）
            margin_call_level = self.initial_balance * 0.1  # 设置爆仓线为初始资金的10%
            if self.equity <= margin_call_level:
                logger.warning(f"账户爆仓! 当前权益: {self.equity:.2f}, 爆仓线: {margin_call_level:.2f}")
                self.margin_call = True
            
        except Exception as e:
            logger.error(f"更新账户权益出错: {str(e)}")
    
    def run_backtest(self) -> bool:
        """执行回测"""
        try:
            # 初始化MT5
            if not self.initialize_mt5():
                return False
            
            # 初始化爆仓标志
            self.margin_call = False
            
            logger.info("开始回测...")
            
            # 获取所有品种的历史数据
            all_data = {}
            for symbol in self.symbols:
                symbol_data = {}
                for timeframe in self.timeframes:
                    df = self.fetch_historical_data(symbol, timeframe, self.start_date, self.end_date)
                    if df is not None:
                        symbol_data[timeframe] = df
                all_data[symbol] = symbol_data
            
            # 创建时间轴（使用所有数据中最小时间周期的时间点）
            all_timestamps = set()
            for symbol, timeframes in all_data.items():
                for timeframe, df in timeframes.items():
                    # 为了减少内存使用，只添加索引而不是整个数据
                    all_timestamps.update(df.index.tolist())
            
            timeline = sorted(all_timestamps)
            
            # 释放不必要的内存
            all_timestamps = None
            
            # 按时间顺序遍历
            logger.info(f"开始按时间顺序回测，共{len(timeline)}个时间点")
            
            # 使用tqdm显示进度
            for i, current_time in enumerate(tqdm(timeline, desc="回测进度")):
                # 每处理1000个时间点，清理一次内存
                if i % 1000 == 0 and i > 0:
                    import gc
                    gc.collect()
                
                # 对每个品种和时间周期进行分析
                for symbol in self.symbols:
                    for timeframe in self.timeframes:
                        if timeframe not in all_data[symbol]:
                            continue
                        
                        df = all_data[symbol][timeframe]
                        
                        # 找到当前时间点在数据中的位置
                        if current_time not in df.index:
                            continue
                        
                        current_index = df.index.get_loc(current_time)
                        
                        # 确保有足够的历史数据进行分析
                        if current_index < 50:  # 至少需要50根K线
                            continue
                        
                        # 分析当前K线
                        signal = self.analyze_bar(df, current_index, symbol, timeframe)
                        
                        # 如果有信号，尝试开仓
                        if signal and not self.margin_call:  # 只有在未爆仓的情况下才开仓
                            self.open_position(signal, df, current_index)
                
                # 更新所有持仓状态
                for symbol in self.symbols:
                    for timeframe in self.timeframes:
                        if timeframe not in all_data[symbol]:
                            continue
                        
                        df = all_data[symbol][timeframe]
                        
                        # 找到当前时间点在数据中的位置
                        if current_time not in df.index:
                            continue
                        
                        current_index = df.index.get_loc(current_time)
                        
                        # 更新持仓
                        self.update_positions(df, current_index, current_time)
                
                # 如果爆仓，平掉所有持仓并退出回测
                if self.margin_call:
                    logger.warning(f"检测到爆仓，在时间点 {current_time} 平掉所有持仓并结束回测")
                    self.close_all_positions(current_time, reason='margin_call')
                    break
            
            # 如果没有爆仓，平掉所有剩余持仓
            if not self.margin_call and self.positions:
                self.close_all_positions(timeline[-1] if timeline else datetime.now())
            
            # 计算统计数据
            self.calculate_statistics()
            
            # 关闭MT5连接
            mt5.shutdown()
            
            return True
            
        except Exception as e:
            logger.error(f"执行回测出错: {str(e)}")
            # 确保关闭MT5连接
            try:
                mt5.shutdown()
            except:
                pass
            return False
    
    def close_all_positions(self, close_time: datetime, reason: str = 'end_of_test') -> None:
        """平掉所有剩余持仓"""
        try:
            if not self.positions:
                return
            
            logger.info(f"平掉所有剩余持仓: {len(self.positions)}个，原因: {reason}")
            
            for position in self.positions[:]:
                # 使用当前价格平仓
                close_price = position['entry_price']  # 简化处理，使用入场价平仓
                self.close_position(position, close_price, close_time, reason)
                
        except Exception as e:
            logger.error(f"平掉所有剩余持仓出错: {str(e)}")
    
    def calculate_statistics(self) -> None:
        """计算回测统计数据"""
        try:
            # 总交易次数
            self.stats['total_trades'] = len(self.closed_positions)
            
            if self.stats['total_trades'] == 0:
                logger.warning("没有完成任何交易，无法计算统计数据")
                return
            
            # 记录是否爆仓
            self.stats['margin_call'] = getattr(self, 'margin_call', False)
            
            # 盈利和亏损交易
            winning_trades = [p for p in self.closed_positions if p['profit'] > 0]
            losing_trades = [p for p in self.closed_positions if p['profit'] <= 0]
            
            self.stats['winning_trades'] = len(winning_trades)
            self.stats['losing_trades'] = len(losing_trades)
            
            # 胜率
            self.stats['win_rate'] = self.stats['winning_trades'] / self.stats['total_trades'] * 100
            
            # 总盈利和总亏损
            self.stats['total_profit'] = sum(p['profit'] for p in winning_trades)
            self.stats['total_loss'] = sum(p['profit'] for p in losing_trades)
            
            # 净利润
            self.stats['net_profit'] = self.stats['total_profit'] + self.stats['total_loss']
            
            # 收益率
            self.stats['return_percent'] = (self.stats['net_profit'] / self.initial_balance) * 100
            
            # 盈亏比
            if self.stats['total_loss'] != 0:
                self.stats['profit_factor'] = abs(self.stats['total_profit'] / self.stats['total_loss'])
            else:
                self.stats['profit_factor'] = float('inf')
            
            # 平均交易
            self.stats['avg_trade'] = self.stats['net_profit'] / self.stats['total_trades']
            
            # 平均盈利和平均亏损
            if self.stats['winning_trades'] > 0:
                self.stats['avg_win'] = self.stats['total_profit'] / self.stats['winning_trades']
            
            if self.stats['losing_trades'] > 0:
                self.stats['avg_loss'] = self.stats['total_loss'] / self.stats['losing_trades']
            
            # 最大盈利和最大亏损
            if winning_trades:
                self.stats['largest_win'] = max(p['profit'] for p in winning_trades)
            
            if losing_trades:
                self.stats['largest_loss'] = min(p['profit'] for p in losing_trades)
            
            # 计算最大回撤
            equity_series = pd.DataFrame(self.daily_equity).set_index('time')['equity']
            running_max = equity_series.cummax()
            drawdown = (running_max - equity_series) / running_max * 100
            self.stats['max_drawdown_percent'] = drawdown.max()
            self.stats['max_drawdown'] = (running_max - equity_series).max()
            
            # 计算最大连续盈利和亏损次数
            profit_series = [1 if p['profit'] > 0 else 0 for p in self.closed_positions]
            
            current_streak = 0
            max_win_streak = 0
            for profit in profit_series:
                if profit == 1:
                    current_streak += 1
                    max_win_streak = max(max_win_streak, current_streak)
                else:
                    current_streak = 0
            
            self.stats['max_consecutive_wins'] = max_win_streak
            
            current_streak = 0
            max_loss_streak = 0
            for profit in profit_series:
                if profit == 0:
                    current_streak += 1
                    max_loss_streak = max(max_loss_streak, current_streak)
                else:
                    current_streak = 0
            
            self.stats['max_consecutive_losses'] = max_loss_streak
            
            # 计算夏普比率（简化版，假设无风险利率为0）
            if len(equity_series) > 1:
                daily_returns = equity_series.pct_change().dropna()
                if len(daily_returns) > 0 and daily_returns.std() > 0:
                    self.stats['sharpe_ratio'] = (daily_returns.mean() / daily_returns.std()) * (252 ** 0.5)  # 假设一年252个交易日
            
            logger.info(f"统计数据计算完成: 总交易{self.stats['total_trades']}次, "
                       f"胜率{self.stats['win_rate']:.2f}%, 净利润{self.stats['net_profit']:.2f}, "
                       f"最大回撤{self.stats['max_drawdown_percent']:.2f}%")
            
        except Exception as e:
            logger.error(f"计算统计数据出错: {str(e)}")
    
    def plot_results(self, save_path: str = None) -> None:
        """绘制回测结果图表"""
        try:
            if not self.daily_equity:
                logger.warning("没有权益数据，无法绘制图表")
                return
            
            # 创建图表
            fig, axes = plt.subplots(2, 1, figsize=(12, 10), gridspec_kw={'height_ratios': [3, 1]})
            
            # 绘制权益曲线
            equity_df = pd.DataFrame(self.daily_equity).set_index('time')
            equity_df.plot(y='equity', ax=axes[0], color='blue', linewidth=2)
            axes[0].set_title('回测结果 - 权益曲线', fontsize=14)
            axes[0].set_ylabel('权益', fontsize=12)
            axes[0].grid(True)
            
            # 添加交易点
            for trade in self.closed_positions:
                if trade['profit'] > 0:
                    color = 'green'
                    marker = '^'
                else:
                    color = 'red'
                    marker = 'v'
                
                # 开仓点
                axes[0].scatter(trade['open_time'], self.initial_balance, color=color, marker='o', s=30, alpha=0.5)
                
                # 平仓点
                if trade['close_time'] in equity_df.index:
                    equity_at_close = equity_df.loc[trade['close_time'], 'equity']
                    axes[0].scatter(trade['close_time'], equity_at_close, color=color, marker=marker, s=50)
            
            # 绘制回撤曲线
            running_max = equity_df['equity'].cummax()
            drawdown = (running_max - equity_df['equity']) / running_max * 100
            drawdown.plot(ax=axes[1], color='red', linewidth=2)
            axes[1].set_title('回撤百分比', fontsize=14)
            axes[1].set_ylabel('回撤 (%)', fontsize=12)
            axes[1].set_xlabel('日期', fontsize=12)
            axes[1].grid(True)
            
            # 添加统计数据文本
            stats_text = (
                f"总交易: {self.stats['total_trades']}次\n"
                f"胜率: {self.stats['win_rate']:.2f}%\n"
                f"盈亏比: {self.stats['profit_factor']:.2f}\n"
                f"净利润: ${self.stats['net_profit']:.2f}\n"
                f"收益率: {self.stats['return_percent']:.2f}%\n"
                f"最大回撤: {self.stats['max_drawdown_percent']:.2f}%\n"
                f"夏普比率: {self.stats['sharpe_ratio']:.2f}\n"
                f"最大连续盈利: {self.stats['max_consecutive_wins']}次\n"
                f"最大连续亏损: {self.stats['max_consecutive_losses']}次"
            )
            
            # 在图表右上角添加统计数据
            axes[0].text(0.02, 0.98, stats_text, transform=axes[0].transAxes,
                        verticalalignment='top', horizontalalignment='left',
                        bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
            
            plt.tight_layout()
            
            # 保存图表
            if save_path:
                plt.savefig(save_path)
                logger.info(f"回测结果图表已保存至: {save_path}")
            
            # 显示图表
            plt.show()
            
        except Exception as e:
            logger.error(f"绘制回测结果图表出错: {str(e)}")
    
    def save_results(self, folder_path: str = None) -> None:
        """保存回测结果"""
        try:
            if folder_path is None:
                folder_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), 
                                          'results', 'backtest', datetime.now().strftime('%Y%m%d_%H%M%S'))
            
            # 创建结果目录
            os.makedirs(folder_path, exist_ok=True)
            
            # 保存交易记录
            trades_df = pd.DataFrame(self.closed_positions)
            trades_path = os.path.join(folder_path, 'trades.csv')
            trades_df.to_csv(trades_path, index=False)
            
            # 保存权益曲线
            equity_df = pd.DataFrame(self.daily_equity)
            equity_path = os.path.join(folder_path, 'equity.csv')
            equity_df.to_csv(equity_path, index=False)
            
            # 保存统计数据
            stats_df = pd.DataFrame([self.stats])
            stats_path = os.path.join(folder_path, 'statistics.csv')
            stats_df.to_csv(stats_path, index=False)
            
            # 保存图表
            plot_path = os.path.join(folder_path, 'backtest_results.png')
            self.plot_results(save_path=plot_path)
            
            logger.info(f"回测结果已保存至: {folder_path}")
            
        except Exception as e:
            logger.error(f"保存回测结果出错: {str(e)}")
    
    def print_summary(self) -> None:
        """打印回测摘要"""
        try:
            print("\n" + "="*50)
            print(f"{'回测摘要':^50}")
            print("="*50)
            print(f"回测周期: {self.start_date.strftime('%Y-%m-%d')} 至 {self.end_date.strftime('%Y-%m-%d')}")
            print(f"初始资金: ${self.initial_balance:.2f}")
            print(f"最终资金: ${self.balance:.2f}")
            print(f"净利润: ${self.stats['net_profit']:.2f} ({self.stats['return_percent']:.2f}%)")
            # 显示爆仓信息
            if self.stats.get('margin_call', False):
                print(f"警告: 回测期间发生爆仓!")
                
            print(f"总交易次数: {self.stats['total_trades']}")
            print(f"盈利交易: {self.stats['winning_trades']} ({self.stats['win_rate']:.2f}%)")
            print(f"亏损交易: {self.stats['losing_trades']}")
            print(f"盈亏比: {self.stats['profit_factor']:.2f}")
            print(f"平均交易盈亏: ${self.stats['avg_trade']:.2f}")
            print(f"平均盈利交易: ${self.stats['avg_win']:.2f}")
            print(f"平均亏损交易: ${self.stats['avg_loss']:.2f}")
            print(f"最大盈利交易: ${self.stats['largest_win']:.2f}")
            print(f"最大亏损交易: ${self.stats['largest_loss']:.2f}")
            print(f"最大回撤: ${self.stats['max_drawdown']:.2f} ({self.stats['max_drawdown_percent']:.2f}%)")
            print(f"最大连续盈利: {self.stats['max_consecutive_wins']}次")
            print(f"最大连续亏损: {self.stats['max_consecutive_losses']}次")
            print(f"夏普比率: {self.stats['sharpe_ratio']:.2f}")
            print("="*50)
            
            # 打印各品种的表现
            print("\n各品种表现:")
            print("-"*50)
            print(f"{'品种':^10}|{'交易次数':^8}|{'盈利次数':^8}|{'亏损次数':^8}|{'胜率':^8}|{'净利润':^10}|{'盈亏比':^8}")
            print("-"*50)
            
            # 按品种统计
            symbol_stats = {}
            for position in self.closed_positions:
                symbol = position['symbol']
                if symbol not in symbol_stats:
                    symbol_stats[symbol] = {
                        'total': 0,
                        'wins': 0,
                        'losses': 0,
                        'profit': 0.0
                    }
                
                symbol_stats[symbol]['total'] += 1
                if position['profit'] > 0:
                    symbol_stats[symbol]['wins'] += 1
                    symbol_stats[symbol]['profit'] += position['profit']
                else:
                    symbol_stats[symbol]['losses'] += 1
                    symbol_stats[symbol]['profit'] += position['profit']
            
            # 打印各品种统计
            for symbol, stats in symbol_stats.items():
                win_rate = (stats['wins'] / stats['total'] * 100) if stats['total'] > 0 else 0
                profit_factor = abs(sum(p['profit'] for p in self.closed_positions if p['symbol'] == symbol and p['profit'] > 0) / 
                                  sum(p['profit'] for p in self.closed_positions if p['symbol'] == symbol and p['profit'] <= 0)) if sum(p['profit'] for p in self.closed_positions if p['symbol'] == symbol and p['profit'] <= 0) != 0 else float('inf')
                
                print(f"{symbol:^10}|{stats['total']:^8}|{stats['wins']:^8}|{stats['losses']:^8}|{win_rate:^8.2f}%|{stats['profit']:^10.2f}|{profit_factor:^8.2f}")
            
            print("-"*50)
            
            # 打印各时间周期的表现
            print("\n各时间周期表现:")
            print("-"*50)
            print(f"{'周期':^10}|{'交易次数':^8}|{'盈利次数':^8}|{'亏损次数':^8}|{'胜率':^8}|{'净利润':^10}|{'盈亏比':^8}")
            print("-"*50)
            
            # 按时间周期统计
            timeframe_stats = {}
            for position in self.closed_positions:
                timeframe = position['timeframe']
                timeframe_str = TIMEFRAME_STRINGS.get(timeframe, str(timeframe))
                
                if timeframe_str not in timeframe_stats:
                    timeframe_stats[timeframe_str] = {
                        'total': 0,
                        'wins': 0,
                        'losses': 0,
                        'profit': 0.0
                    }
                
                timeframe_stats[timeframe_str]['total'] += 1
                if position['profit'] > 0:
                    timeframe_stats[timeframe_str]['wins'] += 1
                    timeframe_stats[timeframe_str]['profit'] += position['profit']
                else:
                    timeframe_stats[timeframe_str]['losses'] += 1
                    timeframe_stats[timeframe_str]['profit'] += position['profit']
            
            # 打印各时间周期统计
            for timeframe_str, stats in timeframe_stats.items():
                win_rate = (stats['wins'] / stats['total'] * 100) if stats['total'] > 0 else 0
                profit_factor = abs(sum(p['profit'] for p in self.closed_positions if TIMEFRAME_STRINGS.get(p['timeframe'], str(p['timeframe'])) == timeframe_str and p['profit'] > 0) / 
                                  sum(p['profit'] for p in self.closed_positions if TIMEFRAME_STRINGS.get(p['timeframe'], str(p['timeframe'])) == timeframe_str and p['profit'] <= 0)) if sum(p['profit'] for p in self.closed_positions if TIMEFRAME_STRINGS.get(p['timeframe'], str(p['timeframe'])) == timeframe_str and p['profit'] <= 0) != 0 else float('inf')
                
                print(f"{timeframe_str:^10}|{stats['total']:^8}|{stats['wins']:^8}|{stats['losses']:^8}|{win_rate:^8.2f}%|{stats['profit']:^10.2f}|{profit_factor:^8.2f}")
            
            print("-"*50)
            
            # 打印各信号类型的表现
            print("\n各信号类型表现:")
            print("-"*50)
            print(f"{'信号类型':^15}|{'交易次数':^8}|{'盈利次数':^8}|{'亏损次数':^8}|{'胜率':^8}|{'净利润':^10}|{'盈亏比':^8}")
            print("-"*50)
            
            # 按信号类型统计
            signal_stats = {}
            for position in self.closed_positions:
                signal_type = position['signal_type']
                
                if signal_type not in signal_stats:
                    signal_stats[signal_type] = {
                        'total': 0,
                        'wins': 0,
                        'losses': 0,
                        'profit': 0.0
                    }
                
                signal_stats[signal_type]['total'] += 1
                if position['profit'] > 0:
                    signal_stats[signal_type]['wins'] += 1
                    signal_stats[signal_type]['profit'] += position['profit']
                else:
                    signal_stats[signal_type]['losses'] += 1
                    signal_stats[signal_type]['profit'] += position['profit']
            
            # 打印各信号类型统计
            for signal_type, stats in signal_stats.items():
                win_rate = (stats['wins'] / stats['total'] * 100) if stats['total'] > 0 else 0
                profit_factor = abs(sum(p['profit'] for p in self.closed_positions if p['signal_type'] == signal_type and p['profit'] > 0) / 
                                  sum(p['profit'] for p in self.closed_positions if p['signal_type'] == signal_type and p['profit'] <= 0)) if sum(p['profit'] for p in self.closed_positions if p['signal_type'] == signal_type and p['profit'] <= 0) != 0 else float('inf')
                
                print(f"{signal_type:^15}|{stats['total']:^8}|{stats['wins']:^8}|{stats['losses']:^8}|{win_rate:^8.2f}%|{stats['profit']:^10.2f}|{profit_factor:^8.2f}")
            
            print("-"*50)
            
        except Exception as e:
            logger.error(f"打印回测摘要出错: {str(e)}")

def convert_timeframe_input(timeframe_input):
    """
    将用户输入的时间周期值转换为MT5的时间周期常量
    
    参数:
        timeframe_input: 用户输入的时间周期值，可以是整数（分钟数）或字符串（如"H1", "D1"等）
    
    返回:
        对应的MT5时间周期常量
    """
    # 分钟数到MT5常量的映射
    minute_to_timeframe = {
        1: mt5.TIMEFRAME_M1,      # 1分钟
        5: mt5.TIMEFRAME_M5,      # 5分钟
        15: mt5.TIMEFRAME_M15,    # 15分钟
        30: mt5.TIMEFRAME_M30,    # 30分钟
        60: mt5.TIMEFRAME_H1,     # 1小时 (60分钟)
        240: mt5.TIMEFRAME_H4,    # 4小时 (240分钟)
        1440: mt5.TIMEFRAME_D1,   # 1天 (1440分钟)
        10080: mt5.TIMEFRAME_W1,  # 1周 (10080分钟)
        43200: mt5.TIMEFRAME_MN1  # 1月 (约43200分钟)
    }
    
    # 字符串到MT5常量的映射
    string_to_timeframe = {
        "M1": mt5.TIMEFRAME_M1,
        "M5": mt5.TIMEFRAME_M5,
        "M15": mt5.TIMEFRAME_M15,
        "M30": mt5.TIMEFRAME_M30,
        "H1": mt5.TIMEFRAME_H1,
        "H4": mt5.TIMEFRAME_H4,
        "H6": mt5.TIMEFRAME_H6,
        "H12": mt5.TIMEFRAME_H12,
        "D1": mt5.TIMEFRAME_D1,
        "W1": mt5.TIMEFRAME_W1,
        "MN1": mt5.TIMEFRAME_MN1
    }
    
    # 尝试将输入转换为整数
    try:
        timeframe_int = int(timeframe_input)
        if timeframe_int in minute_to_timeframe:
            return minute_to_timeframe[timeframe_int]
        else:
            logger.warning(f"未知的时间周期分钟数: {timeframe_int}，使用默认值H1")
            return mt5.TIMEFRAME_H1
    except (ValueError, TypeError):
        # 如果不是整数，尝试作为字符串匹配
        timeframe_str = str(timeframe_input).upper()
        if timeframe_str in string_to_timeframe:
            return string_to_timeframe[timeframe_str]
        else:
            logger.warning(f"未知的时间周期字符串: {timeframe_str}，使用默认值H1")
            return mt5.TIMEFRAME_H1

def run_backtest_with_params(symbols=None, timeframes=None, start_date=None, end_date=None, 
                            initial_balance=10000.0, direction="both", save_results=True):
    """
    使用指定参数运行回测
    
    参数:
        symbols: 回测品种列表，如果为None则使用默认品种
        timeframes: 回测时间周期列表，如果为None则使用默认时间周期
        start_date: 回测开始日期，如果为None则使用当前日期前30天
        end_date: 回测结束日期，如果为None则使用当前日期
        initial_balance: 初始账户余额
        direction: 交易方向，可选值为 "long"、"short" 或 "both"
        save_results: 是否保存回测结果
    
    返回:
        backtester: 回测器实例
    """
    # 创建回测器
    backtester = MT5Backtester(
        symbols=symbols,
        timeframes=timeframes,
        start_date=start_date,
        end_date=end_date,
        initial_balance=initial_balance,
        direction=direction
    )
    
    # 运行回测
    if backtester.run_backtest():
        # 打印摘要
        backtester.print_summary()
        
        # 绘制结果
        backtester.plot_results()
        
        # 保存结果
        if save_results:
            backtester.save_results()
    
    return backtester


if __name__ == "__main__":
    import argparse
    from datetime import datetime
    
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='MT5回测工具')
    parser.add_argument('--symbols', nargs='+', help='回测品种列表，例如 XAUUSDc EURUSDc')
    parser.add_argument('--timeframes', nargs='+', help='回测时间周期列表，例如 60(H1) 240(H4) 1440(D1)，或者 H1 H4 D1')
    parser.add_argument('--start_date', type=str, help='回测开始日期，格式为YYYY-MM-DD')
    parser.add_argument('--end_date', type=str, help='回测结束日期，格式为YYYY-MM-DD')
    parser.add_argument('--initial_balance', type=float, default=10000.0, help='初始账户余额')
    parser.add_argument('--direction', type=str, default='both', choices=['long', 'short', 'both'], help='交易方向')
    parser.add_argument('--debug', action='store_true', help='启用调试模式，输出更详细的日志')
    parser.add_argument('--no_save', action='store_true', help='不保存回测结果')
    
    args = parser.parse_args()
    
    # 如果启用调试模式，设置日志级别为DEBUG
    if args.debug:
        logger.setLevel(logging.DEBUG)
        # 添加控制台处理器，以便在控制台也能看到详细日志
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.DEBUG)
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)
    
    # 转换日期字符串为datetime对象
    start_date = datetime.strptime(args.start_date, '%Y-%m-%d') if args.start_date else None
    end_date = datetime.strptime(args.end_date, '%Y-%m-%d') if args.end_date else None
    
    # 转换时间周期
    timeframes = None
    if args.timeframes:
        timeframes = [convert_timeframe_input(tf) for tf in args.timeframes]
        logger.info(f"转换后的时间周期: {timeframes}")
    
    # 创建回测器实例
    backtester = MT5Backtester(
        symbols=args.symbols,
        timeframes=timeframes,
        start_date=start_date,
        end_date=end_date,
        initial_balance=args.initial_balance,
        direction=args.direction
    )
    
    # 运行回测
    success = backtester.run_backtest()
    
    if success:
        # 输出回测结果
        backtester.print_summary()
        
        # 绘制结果
        backtester.plot_results()
        
        # 保存结果
        if not args.no_save:
            backtester.save_results()
    else:
        print("回测失败，请检查日志获取详细信息。")