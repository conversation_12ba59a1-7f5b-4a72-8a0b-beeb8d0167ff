//+------------------------------------------------------------------+
//|                                    MarketInterpreter_Dashboard.mq5 |
//|                                         Copyright © 2025, htazq |
//|                                         https://github.com/htazq |
//+------------------------------------------------------------------+
#property copyright "Copyright © 2025, htazq"
#property link      "https://github.com/htazq"
#property version   "1.00"
#property description "市场解读家仪表盘 - 专业日内交易指标系统"
#property description "整合趋势分析、价格行为识别、时段分析和波动率监控"

#property indicator_chart_window
#property indicator_buffers 0
#property indicator_plots   0

// 包含所有模块
#include "Include/Defines.mqh"
#include "Include/Context_Session.mqh"
#include "Include/Analysis_KeyLevels.mqh"
#include "Include/Analysis_Volatility.mqh"
#include "Include/Analysis_PriceAction.mqh"
#include "Include/UI_Dashboard.mqh"
#include "Include/System_Alerts.mqh"

//+------------------------------------------------------------------+
//| 输入参数                                                          |
//+------------------------------------------------------------------+
input group "=== 仪表盘显示设置 ==="
input bool InpShowDashboard = true;                    // 显示仪表盘
input int InpDashboardX = DASHBOARD_X;                 // 仪表盘X坐标
input int InpDashboardY = DASHBOARD_Y;                 // 仪表盘Y坐标
input int InpFontSize = FONT_SIZE_CONTENT;             // 字体大小

input group "=== Vegas Tunnel 均线设置 ==="
input int InpVegasFast = VEGAS_EMA_FAST;               // 快速EMA周期
input int InpVegasMedium = VEGAS_EMA_MEDIUM;           // 中速EMA周期
input int InpVegasSlow = VEGAS_EMA_SLOW;               // 慢速EMA周期

input group "=== 关键价位线设置 ==="
input bool InpShowDailyOpen = true;                    // 显示日线开盘价
input color InpDailyOpenColor = clrYellow;             // 日线开盘价颜色
input bool InpShowPrevHigh = true;                     // 显示前一日最高点
input color InpPrevHighColor = clrRed;                 // 前一日最高点颜色
input bool InpShowPrevLow = true;                      // 显示前一日最低点
input color InpPrevLowColor = clrLime;                 // 前一日最低点颜色
input bool InpShowWeeklyOpen = false;                  // 显示周线开盘价
input color InpWeeklyOpenColor = clrOrange;            // 周线开盘价颜色
input bool InpShowMonthlyOpen = false;                 // 显示月线开盘价
input color InpMonthlyOpenColor = clrMagenta;          // 月线开盘价颜色
input int InpKeyLevelLineWidth = 1;                    // 关键价位线宽度

input group "=== ATR波动率设置 ==="
input int InpATRPeriod = ATR_PERIOD;                   // ATR计算周期
input int InpATRHistoryDays = ATR_HISTORY_DAYS;        // ATR历史数据天数

input group "=== 价格行为信号设置 ==="
input double InpMinBodyRatio = PA_SIGNAL_MIN_BODY_RATIO; // 最小K线实体比例
input int InpLookbackBars = PA_LOOKBACK_BARS;          // 回看K线数量

input group "=== 时区和时段设置 ==="
input int InpGMTOffsetWinter = 0;                      // GMT冬令时偏移(小时)
input int InpDSTOffset = 1;                            // 夏令时额外偏移(小时)

input group "=== 警报设置 ==="
input bool InpPAAlertEnabled = true;                   // 启用价格行为信号警报
input bool InpTrendAlertEnabled = true;                // 启用趋势变化警报
input bool InpKeyLevelAlertEnabled = true;             // 启用关键价位警报
input bool InpVolatilityAlertEnabled = true;           // 启用高波动率警报
input bool InpSessionAlertEnabled = false;             // 启用时段变化警报
input bool InpSoundEnabled = true;                     // 启用声音警报
input bool InpPushEnabled = false;                     // 启用推送通知
input double InpKeyLevelThreshold = 50.0;              // 关键价位警报阈值(点)
input double InpVolatilityThreshold = 80.0;            // 高波动率警报阈值(百分位)

//+------------------------------------------------------------------+
//| 全局变量                                                          |
//+------------------------------------------------------------------+
static datetime last_update_time = 0;
static int update_frequency = 1; // 每1秒更新一次

//+------------------------------------------------------------------+
//| 自定义指标初始化函数                                               |
//+------------------------------------------------------------------+
int OnInit()
{
    // 设置指标名称
    IndicatorSetString(INDICATOR_SHORTNAME, "市场解读家仪表盘 v1.0");
    
    // 初始化各个模块
    Session::Initialize();
    KeyLevels::Initialize();
    Volatility::Initialize(InpATRPeriod);
    PriceAction::Initialize();
    Alerts::Initialize();
    
    // 初始化仪表盘UI
    if(InpShowDashboard)
    {
        Dashboard::Initialize(InpDashboardX, InpDashboardY, InpFontSize);
    }
    
    // 启动定时器，用于实时更新
    EventSetTimer(update_frequency);
    
    Print("市场解读家仪表盘初始化完成");
    return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| 自定义指标去初始化函数                                             |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    // 停止定时器
    EventKillTimer();
    
    // 清理各个模块
    Dashboard::Deinitialize();
    Alerts::Deinitialize();
    PriceAction::Deinitialize();
    Volatility::Deinitialize();
    KeyLevels::Deinitialize();
    Session::Deinitialize();
    
    Print("市场解读家仪表盘已清理，原因: ", reason);
}

//+------------------------------------------------------------------+
//| 定时器事件处理函数                                                 |
//+------------------------------------------------------------------+
void OnTimer()
{
    // 实时更新仪表盘显示
    if(InpShowDashboard)
    {
        UpdateDashboard();
    }
}

//+------------------------------------------------------------------+
//| 自定义指标计算函数                                                 |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[])
{
    // 检查数据是否足够
    if(rates_total < InpVegasSlow + 10)
        return(0);
    
    // 更新各个分析模块
    UpdateAnalysisModules();
    
    // 更新仪表盘显示
    if(InpShowDashboard)
    {
        UpdateDashboard();
    }
    
    return(rates_total);
}

//+------------------------------------------------------------------+
//| 更新分析模块                                                       |
//+------------------------------------------------------------------+
void UpdateAnalysisModules()
{
    // 更新关键价位线
    KeyLevels::Update(InpShowDailyOpen, InpDailyOpenColor,
                     InpShowPrevHigh, InpPrevHighColor,
                     InpShowPrevLow, InpPrevLowColor,
                     InpShowWeeklyOpen, InpWeeklyOpenColor,
                     InpShowMonthlyOpen, InpMonthlyOpenColor,
                     InpKeyLevelLineWidth);
    
    // 更新ATR统计数据
    Volatility::UpdateStats();
}

//+------------------------------------------------------------------+
//| 更新仪表盘显示                                                     |
//+------------------------------------------------------------------+
void UpdateDashboard()
{
    // 获取当前时间，避免过于频繁的更新
    datetime current_time = TimeCurrent();
    if(current_time == last_update_time) return;
    last_update_time = current_time;
    
    // === 获取趋势状态 ===
    ENUM_TREND_STATE current_trend = PriceAction::GetTrendFromVegasIndicator();
    string trend_info = "";
    
    // === 获取交易时段信息 ===
    ENUM_TRADING_SESSION current_session = Session::GetCurrent(current_time, InpGMTOffsetWinter, InpDSTOffset);
    int session_activity = Session::GetSessionActivityScore(current_session);
    
    // === 获取ATR波动率信息 ===
    Volatility::ATRStats atr_stats = Volatility::GetStats();
    
    // === 获取价格行为信号 ===
    string pa_signal = PriceAction::Analyze(current_trend, 1);
    string pa_counter = PriceAction::GetCounterStatus();
    
    // === 获取关键价位数据 ===
    double daily_open = KeyLevels::GetDailyOpen();
    double prev_high = KeyLevels::GetPrevHigh();
    double prev_low = KeyLevels::GetPrevLow();
    
    // === 检查并触发警报 ===
    double current_price = SymbolInfoDouble(_Symbol, SYMBOL_BID);
    Alerts::CheckAndTrigger(pa_signal, current_trend, current_session,
                           current_price, atr_stats.percentile_rank, 
                           daily_open, prev_high, prev_low,
                           InpPAAlertEnabled, InpTrendAlertEnabled, InpKeyLevelAlertEnabled,
                           InpVolatilityAlertEnabled, InpSessionAlertEnabled,
                           InpSoundEnabled, InpPushEnabled,
                           InpKeyLevelThreshold, InpVolatilityThreshold);
    
    // === 获取最新警报消息 ===
    string alert_message = Alerts::GetLatestAlertMessage();
    color alert_color = COLOR_INFO;
    
    // 根据警报内容设置颜色
    if(StringFind(alert_message, "价格行为") >= 0)
    {
        if(StringFind(alert_message, "L1") >= 0 || StringFind(alert_message, "L2") >= 0)
            alert_color = COLOR_SUCCESS;
        else if(StringFind(alert_message, "H1") >= 0 || StringFind(alert_message, "H2") >= 0)
            alert_color = COLOR_DANGER;
    }
    else if(StringFind(alert_message, "趋势变化") >= 0)
    {
        alert_color = COLOR_WARNING;
    }
    else if(StringFind(alert_message, "关键价位") >= 0)
    {
        alert_color = COLOR_WARNING;
    }
    else if(StringFind(alert_message, "高波动率") >= 0)
    {
        alert_color = COLOR_DANGER;
    }
    
    // === 渲染仪表盘 ===
    Dashboard::Render(current_trend, trend_info,
                     current_session, session_activity,
                     atr_stats.current_value, atr_stats.volatility_level, atr_stats.percentile_rank,
                     pa_signal, pa_counter,
                     daily_open, prev_high, prev_low,
                     alert_message, alert_color);
}

//+------------------------------------------------------------------+
//| 获取Vegas Tunnel趋势状态（供外部调用）                             |
//+------------------------------------------------------------------+
ENUM_TREND_STATE GetTrendFromVegasIndicator()
{
    return PriceAction::GetTrendFromVegasIndicator();
}

//+------------------------------------------------------------------+
//| 图表事件处理函数                                                   |
//+------------------------------------------------------------------+
void OnChartEvent(const int id,
                  const long &lparam,
                  const double &dparam,
                  const string &sparam)
{
    switch(id)
    {
        case CHARTEVENT_KEYDOWN:
            // 处理键盘事件
            HandleKeyboardEvent((int)lparam);
            break;
            
        case CHARTEVENT_MOUSE_MOVE:
            // 处理鼠标移动事件（如果需要）
            break;
            
        case CHARTEVENT_OBJECT_CLICK:
            // 处理对象点击事件（如果需要）
            break;
    }
}

//+------------------------------------------------------------------+
//| 处理键盘事件                                                       |
//+------------------------------------------------------------------+
void HandleKeyboardEvent(int key_code)
{
    switch(key_code)
    {
        case 68: // 'D' 键 - 切换仪表盘显示
            if(InpShowDashboard)
            {
                static bool dashboard_visible = true;
                dashboard_visible = !dashboard_visible;
                Dashboard::SetVisible(dashboard_visible);
                Print("仪表盘显示: ", dashboard_visible ? "开启" : "关闭");
            }
            break;
            
        case 67: // 'C' 键 - 清除警报历史
            Alerts::ClearAlertHistory();
            Print("警报历史已清除");
            break;
            
        case 83: // 'S' 键 - 显示统计信息
            Print("=== 市场解读家仪表盘统计 ===");
            Print("当前趋势: ", EnumToString(GetTrendFromVegasIndicator()));
            Print("价格行为: ", PriceAction::GetCounterStatus());
            Print("ATR统计: ", Volatility::FormatATRDisplay());
            Print(Alerts::GetAlertStats());
            break;
    }
}

//+------------------------------------------------------------------+
//| 获取指标信息（供EA或其他指标调用）                                  |
//+------------------------------------------------------------------+
struct MarketInterpreterData
{
    ENUM_TREND_STATE trend_state;          // 趋势状态
    ENUM_TRADING_SESSION session;          // 交易时段
    ENUM_PA_SIGNAL_TYPE last_signal;       // 最后价格行为信号
    double atr_current;                     // 当前ATR
    double atr_percentile;                  // ATR百分位
    string volatility_level;                // 波动率水平
    double daily_open;                      // 日开盘价
    double prev_high;                       // 前日最高
    double prev_low;                        // 前日最低
    bool is_high_volatility;                // 是否高波动率
    int session_activity_score;             // 时段活跃度评分
};

//+------------------------------------------------------------------+
//| 获取完整的市场解读数据                                             |
//+------------------------------------------------------------------+
MarketInterpreterData GetMarketData()
{
    MarketInterpreterData data;
    
    // 获取趋势状态
    data.trend_state = PriceAction::GetTrendFromVegasIndicator();
    
    // 获取交易时段
    data.session = Session::GetCurrent(TimeCurrent(), InpGMTOffsetWinter, InpDSTOffset);
    data.session_activity_score = Session::GetSessionActivityScore(data.session);
    
    // 获取价格行为信号
    data.last_signal = PriceAction::GetLastSignalType();
    
    // 获取ATR数据
    Volatility::ATRStats atr_stats = Volatility::GetStats();
    data.atr_current = atr_stats.current_value;
    data.atr_percentile = atr_stats.percentile_rank;
    data.volatility_level = atr_stats.volatility_level;
    data.is_high_volatility = Volatility::IsHighVolatility();
    
    // 获取关键价位
    data.daily_open = KeyLevels::GetDailyOpen();
    data.prev_high = KeyLevels::GetPrevHigh();
    data.prev_low = KeyLevels::GetPrevLow();
    
    return data;
}

//+------------------------------------------------------------------+
//| 公共函数：供其他程序调用                                           |
//+------------------------------------------------------------------+

// 获取当前趋势状态
ENUM_TREND_STATE GetCurrentTrend()
{
    return GetTrendFromVegasIndicator();
}

// 获取当前交易时段
ENUM_TRADING_SESSION GetCurrentSession()
{
    return Session::GetCurrent(TimeCurrent(), InpGMTOffsetWinter, InpDSTOffset);
}

// 获取最后价格行为信号
ENUM_PA_SIGNAL_TYPE GetLastPriceActionSignal()
{
    return PriceAction::GetLastSignalType();
}

// 获取当前ATR值
double GetCurrentATR()
{
    return Volatility::GetCurrentValue();
}

// 检查是否为高波动率环境
bool IsHighVolatilityEnvironment()
{
    return Volatility::IsHighVolatility();
}

// 获取风险调整后的止损距离
double GetRiskAdjustedStopDistance()
{
    return Volatility::GetRiskAdjustedStopDistance();
}

// 获取动态仓位大小建议
double GetDynamicPositionSize(double risk_percent = 2.0)
{
    return Volatility::GetDynamicPositionSize(risk_percent);
}

//+------------------------------------------------------------------+
//| 版本信息和帮助                                                     |
//+------------------------------------------------------------------+
void ShowHelp()
{
    Print("=== 市场解读家仪表盘 v1.0 ===");
    Print("快捷键:");
    Print("  D - 切换仪表盘显示");
    Print("  C - 清除警报历史");
    Print("  S - 显示统计信息");
    Print("");
    Print("功能模块:");
    Print("  • Vegas Tunnel趋势分析");
    Print("  • L1/L2/H1/H2价格行为信号识别");
    Print("  • 交易时段分析和活跃度评分");
    Print("  • ATR波动率监控和百分位排名");
    Print("  • 关键价位线显示和距离计算");
    Print("  • 多类型智能警报系统");
    Print("  • 实时仪表盘可视化界面");
    Print("");
    Print("作者: htazq");
    Print("版权: Copyright © 2025");
    Print("项目地址: https://github.com/htazq");
}