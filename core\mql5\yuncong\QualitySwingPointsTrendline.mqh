//+------------------------------------------------------------------+
//|                                QualitySwingPointsTrendline.mqh   |
//|                                                                  |
//|                                                                  |
//+------------------------------------------------------------------+
#ifndef QUALITY_SWING_POINTS_TRENDLINE_MQH
#define QUALITY_SWING_POINTS_TRENDLINE_MQH

#include <Arrays/ArrayObj.mqh>

//+------------------------------------------------------------------+
//| 波峰/波谷点信息结构体                                             |
//+------------------------------------------------------------------+
struct SwingPointInfo
{
   double price;  // 波峰或波谷的价格
   int    index;  // 波峰/波谷发生的K线索引 (rates[index])
};

//+------------------------------------------------------------------+
//| 检测高质量波峰和波谷点                                            |
//+------------------------------------------------------------------+
void DetectQualitySwingPoints(
     const MqlRates &rates[],             // 输入：价格数据数组 (OHLC)
     const double &atr_values[],          // 输入：预先计算好的ATR值数组
     const int lookback_period,           // 输入：回看多少根K线
     const int swing_definition_bars,     // 输入：基础分形定义所需的左右K线数
     const double swing_amplitude_multiplier, // 输入：用于幅度过滤的ATR乘数
     const double min_swing_amplitude,    // 输入：波动点反向运动的最小绝对价格幅度 (0表示禁用)
     const int min_bars_between_swings,   // 输入：同类型高质量波动点之间的最小 K 线间隔
     SwingPointInfo &high_points[],       // 输出：用于存储检测到的高质量波峰的数组
     SwingPointInfo &low_points[]         // 输出：用于存储检测到的高质量波谷的数组
)
{
   // 初始化输出数组
   ArrayResize(high_points, 0);
   ArrayResize(low_points, 0);
   
   // 确保有足够的数据进行计算
   int rates_total = ArraySize(rates);
   if(rates_total < lookback_period || lookback_period <= 2 * swing_definition_bars)
   {
      Print("数据不足或参数设置不合理，无法检测波动点");
      return;
   }
   
   // 临时存储检测到的波峰和波谷
   int high_count = 0;
   int low_count = 0;
   SwingPointInfo temp_high_points[];
   SwingPointInfo temp_low_points[];
   
   // 迭代检查每个可能的波动点
   for(int i = swing_definition_bars; i < lookback_period - swing_definition_bars; i++)
   {
      bool is_potential_high = true;
      bool is_potential_low = true;
      
      // 基础分形检查 - 波峰
      for(int j = 1; j <= swing_definition_bars; j++)
      {
         // 检查左侧K线
         if(rates[i].high <= rates[i-j].high)
         {
            is_potential_high = false;
            break;
         }
         
         // 检查右侧K线
         if(rates[i].high <= rates[i+j].high)
         {
            is_potential_high = false;
            break;
         }
      }
      
      // 基础分形检查 - 波谷
      for(int j = 1; j <= swing_definition_bars; j++)
      {
         // 检查左侧K线
         if(rates[i].low >= rates[i-j].low)
         {
            is_potential_low = false;
            break;
         }
         
         // 检查右侧K线
         if(rates[i].low >= rates[i+j].low)
         {
            is_potential_low = false;
            break;
         }
      }
      
      // 幅度过滤 - 波峰
      if(is_potential_high)
      {
         bool amplitude_condition_met = false;
         // 向后查找显著下跌
         for(int k = i + 1; k < MathMin(i + lookback_period/2, lookback_period); k++)
         {
            // 计算幅度阈值: 取 ATR 阈值和最小绝对阈值中较大的那个
            double amplitude_threshold_atr = atr_values[i] * swing_amplitude_multiplier;
            double required_amplitude = MathMax(amplitude_threshold_atr, min_swing_amplitude);
            
            if(rates[i].high - rates[k].low >= required_amplitude)
            {
               amplitude_condition_met = true;
               break;
            }
         }
         
         if(amplitude_condition_met)
         {
            // === 新增：时间/K线间隔过滤 ===
            bool interval_ok = true;
            if (high_count > 0 && min_bars_between_swings > 0) {
               // 检查与上一个已添加的高点的索引差
               if (i - temp_high_points[high_count - 1].index < min_bars_between_swings) {
                  interval_ok = false; // 间隔太小，忽略此点
               }
            }
            
            if (interval_ok) { // 如果间隔满足要求
               // 添加到临时高点数组
               high_count++;
               ArrayResize(temp_high_points, high_count);
               temp_high_points[high_count-1].price = rates[i].high;
               temp_high_points[high_count-1].index = i;
            }
         }
      }
      
      // 幅度过滤 - 波谷
      if(is_potential_low)
      {
         bool amplitude_condition_met = false;
         // 向后查找显著上涨
         for(int k = i + 1; k < MathMin(i + lookback_period/2, lookback_period); k++)
         {
            // 计算幅度阈值: 取 ATR 阈值和最小绝对阈值中较大的那个
            double amplitude_threshold_atr = atr_values[i] * swing_amplitude_multiplier;
            double required_amplitude = MathMax(amplitude_threshold_atr, min_swing_amplitude);
            
            if(rates[k].high - rates[i].low >= required_amplitude)
            {
               amplitude_condition_met = true;
               break;
            }
         }
         
         if(amplitude_condition_met)
         {
            // === 新增：时间/K线间隔过滤 ===
            bool interval_ok = true;
            if (low_count > 0 && min_bars_between_swings > 0) {
               // 检查与上一个已添加的低点的索引差
               if (i - temp_low_points[low_count - 1].index < min_bars_between_swings) {
                  interval_ok = false; // 间隔太小，忽略此点
               }
            }
            
            if (interval_ok) { // 如果间隔满足要求
               // 添加到临时低点数组
               low_count++;
               ArrayResize(temp_low_points, low_count);
               temp_low_points[low_count-1].price = rates[i].low;
               temp_low_points[low_count-1].index = i;
            }
         }
      }
   }
   
   // 按K线索引降序排序（最新的点在数组前面）
   if(high_count > 0)
   {
      ArrayResize(high_points, high_count);
      for(int i = 0; i < high_count; i++)
      {
         for(int j = i + 1; j < high_count; j++)
         {
            if(temp_high_points[i].index < temp_high_points[j].index)
            {
               SwingPointInfo temp = temp_high_points[i];
               temp_high_points[i] = temp_high_points[j];
               temp_high_points[j] = temp;
            }
         }
      }
      
      // 复制到输出数组
      for(int i = 0; i < high_count; i++)
      {
         high_points[i] = temp_high_points[i];
      }
   }
   
   if(low_count > 0)
   {
      ArrayResize(low_points, low_count);
      for(int i = 0; i < low_count; i++)
      {
         for(int j = i + 1; j < low_count; j++)
         {
            if(temp_low_points[i].index < temp_low_points[j].index)
            {
               SwingPointInfo temp = temp_low_points[i];
               temp_low_points[i] = temp_low_points[j];
               temp_low_points[j] = temp;
            }
         }
      }
      
      // 复制到输出数组
      for(int i = 0; i < low_count; i++)
      {
         low_points[i] = temp_low_points[i];
      }
   }
}

//+------------------------------------------------------------------+
//| 计算由两个波动点定义的趋势线段参数                                |
//+------------------------------------------------------------------+
bool CalculateTrendlineSegment(
     const SwingPointInfo &sp1,       // 输入：第一个波动点 (时间上较早)
     const SwingPointInfo &sp2,       // 输入：第二个波动点 (时间上较晚)
     double &m,                       // 输出：直线的斜率
     double &c                        // 输出：直线的Y轴截距
)
{
   // 检查两个点是否在同一K线上
   if(sp1.index == sp2.index)
   {
      Print("错误：两个波动点位于同一K线上，无法构成趋势线");
      return false;
   }
   
   // 计算斜率
   m = (sp2.price - sp1.price) / (double)(sp2.index - sp1.index);
   
   // 计算Y轴截距
   c = sp1.price - m * (double)sp1.index;
   
   return true;
}

//+------------------------------------------------------------------+
//| 获取给定K线索引处的趋势线价格                                     |
//+------------------------------------------------------------------+
double GetTrendlinePrice(
    const double m,                 // 输入：趋势线的斜率
    const double c,                 // 输入：趋势线的Y轴截距
    const int bar_index             // 输入：需要计算价格的K线索引
)
{
   return m * (double)bar_index + c;
}

#endif // QUALITY_SWING_POINTS_TRENDLINE_MQH