//+------------------------------------------------------------------+
//|                                           RobustRegression.mqh    |
//|                                  Copyright 2025,  Hu<PERSON>  <PERSON><PERSON>        |
//|                                        https://www.at9.net        |
//+------------------------------------------------------------------+
#ifndef ROBUST_REGRESSION_MQH
#define ROBUST_REGRESSION_MQH

#include <Arrays/ArrayDouble.mqh>

//+------------------------------------------------------------------+
//| 计算 Theil-Sen 稳健回归                                           |
//| 该方法对异常值不敏感，比普通最小二乘法更稳健                        |
//|                                                                   |
//| 参数:                                                             |
//|   y_values[] - 因变量数组 (例如价格)，必须是时间正序！(索引0是最旧数据) |
//|   start_index - y_values 数组中计算的起始索引 (通常为 0)            |
//|   count - 用于计算的数据点数量 (n)                                 |
//|   slope - 输出参数，计算出的斜率 (m)                               |
//|   intercept - 输出参数，计算出的截距 (c)                           |
//|   r_squared - 输出参数，计算出的 R 平方系数                         |
//|                                                                   |
//| 返回值:                                                           |
//|   true - 计算成功                                                 |
//|   false - 计算失败 (数据不足或无效)                                |
//+------------------------------------------------------------------+
bool CalculateTheilSenRegression(
   const double &y_values[],      // 输入: 因变量数组 (例如价格)，必须是时间正序！(索引0是最旧数据)
   const int start_index,         // 输入: y_values 数组中计算的起始索引 (通常为 0)
   const int count,               // 输入: 用于计算的数据点数量 (n)
   double &slope,                 // 输出: 计算出的斜率 (m)
   double &intercept,             // 输出: 计算出的截距 (c)
   double &r_squared              // 输出: 计算出的 R 平方系数
)
{
   // 初始化输出参数
   slope = 0.0;
   intercept = 0.0;
   r_squared = 0.0;
   
   // 1. 输入验证
   if(count < 2)
   {
      Print("错误: 计算 Theil-Sen 回归至少需要 2 个数据点");
      return false;
   }
   
   int array_size = ArraySize(y_values);
   if(start_index < 0 || start_index + count > array_size)
   {
      Print("错误: 数据范围无效. start_index=", start_index, ", count=", count, ", array_size=", array_size);
      return false;
   }
   
   // 2. 计算所有点对的斜率
   // 预计斜率数组大小为 count * (count - 1) / 2
   CArrayDouble slopes;
   slopes.Reserve(count * (count - 1) / 2);
   
   for(int i = start_index; i < start_index + count - 1; i++)
   {
      for(int j = i + 1; j < start_index + count; j++)
      {
         // 计算 X 值 (使用相对索引)
         double x_i = i - start_index;
         double x_j = j - start_index;
         
         // 计算斜率
         double current_slope = (y_values[j] - y_values[i]) / (x_j - x_i);
         
         // 添加到斜率数组
         slopes.Add(current_slope);
      }
   }
   
   // 检查是否有有效斜率
   int slopes_count = slopes.Total();
   if(slopes_count == 0)
   {
      Print("错误: 无法计算有效斜率");
      return false;
   }
   
   // 3. 计算斜率中位数
   slopes.Sort();
   
   // 计算中位数索引
   int median_index = slopes_count / 2;
   
   // 根据数组大小是奇数还是偶数，计算中位数
   if(slopes_count % 2 == 1)
   {
      // 奇数个元素，直接取中间值
      slope = slopes.At(median_index);
   }
   else
   {
      // 偶数个元素，取中间两个值的平均
      slope = (slopes.At(median_index - 1) + slopes.At(median_index)) / 2.0;
   }
   
   // 4. 计算所有点的截距
   CArrayDouble intercepts;
   intercepts.Reserve(count);
   
   for(int i = start_index; i < start_index + count; i++)
   {
      double x_i = i - start_index;
      double current_intercept = y_values[i] - slope * x_i;
      intercepts.Add(current_intercept);
   }
   
   // 5. 计算截距中位数
   intercepts.Sort();
   
   int intercepts_count = intercepts.Total();
   int intercept_median_index = intercepts_count / 2;
   
   if(intercepts_count % 2 == 1)
   {
      intercept = intercepts.At(intercept_median_index);
   }
   else
   {
      intercept = (intercepts.At(intercept_median_index - 1) + intercepts.At(intercept_median_index)) / 2.0;
   }
   
   // 6. 计算 R²
   double mean_y = 0.0;
   for(int i = start_index; i < start_index + count; i++)
   {
      mean_y += y_values[i];
   }
   mean_y /= count;
   
   double ss_tot = 0.0; // 总平方和
   double ss_res = 0.0; // 残差平方和
   
   for(int i = start_index; i < start_index + count; i++)
   {
      double x_i = i - start_index;
      double predicted_y = slope * x_i + intercept;
      
      ss_tot += MathPow(y_values[i] - mean_y, 2);
      ss_res += MathPow(y_values[i] - predicted_y, 2);
   }
   
   // 处理所有 Y 值相同的特殊情况
   if(ss_tot == 0.0)
   {
      r_squared = 1.0; // 完美拟合
   }
   else
   {
      r_squared = 1.0 - (ss_res / ss_tot);
   }
   
   return true;
}

//+------------------------------------------------------------------+
//| 计算回归线在特定 X 位置的 Y 值                                     |
//|                                                                   |
//| 参数:                                                             |
//|   slope - 回归线斜率                                              |
//|   intercept - 回归线截距                                          |
//|   x_index - 相对于起始点的 X 索引                                  |
//|                                                                   |
//| 返回值:                                                           |
//|   回归线在指定 X 位置的 Y 值                                       |
//+------------------------------------------------------------------+
double GetRegressionValue(double slope, double intercept, int x_index)
{
   return slope * x_index + intercept;
}

#endif // ROBUST_REGRESSION_MQH