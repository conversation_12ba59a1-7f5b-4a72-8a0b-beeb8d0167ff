import MetaTrader5 as mt5
import pandas as pd
from datetime import datetime
import logging
import sys
import os
from typing import Dict, List, Optional
import time

from utils.log_utils import setup_logger
from platforms.mt5.mt5_config import TIMEFRAMES, SYMBOLS, SYMBOL_TYPES, MT5_PATH


# 设置日志
logger = setup_logger(
    'mt5_data', 
    os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), 'logs', 'mt5', 'data.log'),
    level=logging.INFO
)
class MT5DataFetcher:
    """MT5数据获取器"""
    
    def __init__(self, symbols: List[str] = None, analysis_mode: bool = False):
        # 根据模式选择品种
        if analysis_mode:
            self.symbols = SYMBOLS["analysis"]
        else:
            self.symbols = symbols if symbols is not None else SYMBOLS["default"]
            
        self.timeframes = TIMEFRAMES
        
        # 品种类别定义
        self.symbol_types = SYMBOL_TYPES

    def initialize(self) -> bool:
        """初始化MT5连接"""
        try:
            if not mt5.initialize(path=MT5_PATH):
                logger.error(f"MT5初始化失败，路径: {MT5_PATH}")
                return False
            return True
        except Exception as e:
            logger.error(f"MT5初始化出错: {str(e)}")
            return False

    def _determine_trading_directions(self):
        """确定交易方向"""
        try:
            # 默认允许双向交易
            directions = {
                symbol: {
                    timeframe: ['LONG', 'SHORT'] for timeframe in self.timeframe_map
                } for symbol in self.trading_symbols
            }
            
            # 返回交易方向配置
            return directions
            
        except Exception as e:
            logger.error(f"确定交易方向出错: {str(e)}", exc_info=True)
            # 出错时返回空字典，这将导致不进行任何交易
            return {}

    def fetch_data(self, symbol: str, timeframe: int, num_bars: int = 200) -> Optional[pd.DataFrame]:
        """获取K线数据"""
        start_time = time.time()
        try:
            # 检查参数
            if not isinstance(num_bars, int) or num_bars <= 0:
                logger.error(f"无效的K线数量: {num_bars}")
                return None
                
            # 检查时间周期是否支持
            if timeframe not in self.timeframes:
                logger.error(f"不支持的时间周期: {timeframe}")
                return None
            
            # 获取数据
            rates = mt5.copy_rates_from_pos(symbol, timeframe, 0, num_bars)
            if rates is None:
                error = mt5.last_error()
                logger.error(f"获取{symbol} {timeframe}数据失败: {error}")
                return None
            
            # 检查数据完整性
            if len(rates) < num_bars:
                logger.warning(f"{symbol} {timeframe}数据不完整: 请求{num_bars}根，实际获取{len(rates)}根")
            
            # 转换数据
            df = pd.DataFrame(rates)
            df['time'] = pd.to_datetime(df['time'], unit='s')
            df.set_index('time', inplace=True)
            
            # 检查数据质量
            if df.isnull().any().any():
                logger.warning(f"{symbol} {timeframe}数据存在空值")
                
            fetch_time = time.time() - start_time
            if fetch_time > 1.0:  # 如果获取时间超过1秒
                logger.warning(f"获取{symbol} {timeframe}数据耗时较长: {fetch_time:.2f}秒")
            else:
                logger.debug(f"获取{symbol} {timeframe}数据耗时: {fetch_time:.2f}秒")
                
            return df
            
        except Exception as e:
            logger.error(f"获取{symbol} {timeframe}数据出错: {str(e)}", exc_info=True)
            return None

    def is_trading_time(self, symbol: str) -> bool:
        """检查是否在交易时间内"""
        try:
            now = datetime.now()
            current_weekday = now.weekday()
            current_time = now.strftime("%H:%M")
            
            # 确定品种类别
            symbol_type = None
            for type_name, symbols in self.symbol_types.items():
                if symbol in symbols:
                    symbol_type = type_name
                    break
            
            if symbol_type is None:
                logger.error(f"未知品种类别: {symbol}")
                return False
            
            # 加密货币24/7交易
            if symbol_type == "crypto":
                # 检查服务器连接和交易状态
                if not mt5.terminal_info():
                    logger.error(f"{symbol}MT5连接已断开")
                    return False
                    
                symbol_info = mt5.symbol_info(symbol)
                if symbol_info is None:
                    logger.error(f"无法获取{symbol}交易信息")
                    return False
                    
                if not symbol_info.trade_mode == mt5.SYMBOL_TRADE_MODE_FULL:
                    logger.info(f"{symbol}当前不可交易: {symbol_info.trade_mode}")
                    return False
                
                return True
            
            # 外汇和贵金属的交易时间检查
            if symbol_type == "forex":
                    
                # 周六早上6点后到周一早上6点前不交易
                if current_weekday == 5 and current_time >= "06:00":  # 周六6点后
                    logger.info(f"{symbol}当前为周末休市时间")
                    return False
                if current_weekday == 6:  # 周日全天
                    logger.info(f"{symbol}当前为周末休市时间")
                    return False
                if current_weekday == 0 and current_time < "06:00":  # 周一6点前
                    logger.info(f"{symbol}当前为周末休市时间")
                    return False
                    
                # 检查服务器连接和交易状态
                if not mt5.terminal_info():
                    logger.error(f"{symbol}MT5连接已断开")
                    return False
                    
                symbol_info = mt5.symbol_info(symbol)
                if symbol_info is None:
                    logger.error(f"无法获取{symbol}交易信息")
                    return False
                    
                if not symbol_info.trade_mode == mt5.SYMBOL_TRADE_MODE_FULL:
                    logger.info(f"{symbol}当前不可交易: {symbol_info.trade_mode}")
                    return False
                    
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"检查{symbol}交易时间出错: {str(e)}", exc_info=True)
            return False
            
    def can_close_position(self, symbol: str) -> bool:
        """检查是否可以平仓（即使在非交易时间）"""
        try:
            # 检查服务器连接
            if not mt5.terminal_info():
                logger.error(f"{symbol}MT5连接已断开")
                return False
                
            # 获取品种信息
            symbol_info = mt5.symbol_info(symbol)
            if symbol_info is None:
                logger.error(f"无法获取{symbol}交易信息")
                return False
                
            # 检查是否允许平仓
            # 即使市场关闭，某些经纪商也允许平仓
            # 检查交易模式是否为FULL或CLOSEONLY
            if (symbol_info.trade_mode == mt5.SYMBOL_TRADE_MODE_FULL or 
                symbol_info.trade_mode == mt5.SYMBOL_TRADE_MODE_CLOSEONLY):
                return True
                
            logger.info(f"{symbol}当前不可平仓: {symbol_info.trade_mode}")
            return False
            
        except Exception as e:
            logger.error(f"检查{symbol}平仓状态出错: {str(e)}", exc_info=True)
            return False

if __name__ == "__main__":
    
    # 测试数据获取
    fetcher = MT5DataFetcher()
    
    # 测试初始化
    if not fetcher.initialize():
        sys.exit(1)
        
    # 测试数据获取
    for symbol in fetcher.symbols:
        if not fetcher.is_trading_time(symbol):
            print(f"{symbol} 不在交易时间")
            continue
            
        for timeframe in fetcher.timeframes:
            data = fetcher.fetch_data(symbol, timeframe)
            if data is not None:
                print(f"\n{symbol} {timeframe} 数据:")
                print(data.tail(3)) 