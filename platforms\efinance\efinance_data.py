import efinance as ef
import pandas as pd
from datetime import datetime
from typing import List, Optional, Dict, Union
import logging
import os
import sys

from utils.log_utils import setup_logger

# 设置日志
logger = setup_logger(
    'efinance_data', 
    os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), 'logs', 'efinance', 'data.log'),
    level=logging.INFO
)

class EFinanceDataFetcher:
    """国内期货数据获取器"""
    
    def __init__(self):
        """初始化数据获取器"""
        # 定义支持的时间周期
        self.timeframes = ['1d', '4h', '1h', '15m']
        
        try:
            self.futures_info = ef.futures.get_futures_base_info()
            logger.info("期货基础信息获取成功")
        except Exception as e:
            logger.error(f"获取期货基础信息失败: {str(e)}")
            raise
    
    def get_symbol_info(self, symbol: str) -> Dict:
        """获取期货品种信息"""
        try:
            matched = self.futures_info[self.futures_info['期货代码'] == symbol]
            if len(matched) == 0:
                raise ValueError(f'找不到期货代码 {symbol}')
            return matched.iloc[0].to_dict()
        except Exception as e:
            logger.error(f"获取期货信息失败: {str(e)}")
            raise

    def get_klines(self, 
                   symbol: str,
                   timeframe: str,
                   start_date: Optional[str] = None,
                   end_date: Optional[str] = None,
                   limit: int = 200) -> pd.DataFrame:
        """获取K线数据
        
        Args:
            symbol: 期货代码，如'jdm'代表鸡蛋主力
            timeframe: K线周期 ('1d','4h','1h','15m')
            start_date: 开始日期
            end_date: 结束日期
            limit: 获取最近的K线数量，默认200根
        """
        try:
            # 获取期货行情ID
            quote_id = self.get_symbol_info(symbol)['行情ID']
            logger.info(f"获取到{symbol}的行情ID: {quote_id}")
            
            # 获取日线数据
            df = ef.futures.get_quote_history(quote_id)
            logger.info(f"获取到{len(df)}条历史数据")
            
            # 统一列名
            df = df.rename(columns={
                '日期': 'datetime',
                '开盘': 'open',
                '最高': 'high', 
                '最低': 'low',
                '收盘': 'close',
                '成交量': 'tick_volume',
                '成交额': 'turnover'
            })
            
            # 设置日期索引
            df['datetime'] = pd.to_datetime(df['datetime'])
            df.set_index('datetime', inplace=True)
            
            # 根据timeframe重采样
            if timeframe != '1d':
                rule_map = {
                    '4h': '4h',
                    '1h': '1h',
                    '15m': '15min'
                }
                rule = rule_map[timeframe]
                
                df = df.resample(rule).agg({
                    'open': 'first',
                    'high': 'max',
                    'low': 'min',
                    'close': 'last',
                    'tick_volume': 'sum',
                    'turnover': 'sum'
                }).dropna()
            
            # 处理日期过滤或限制数量
            if start_date:
                df = df[df.index >= start_date]
            if end_date:
                df = df[df.index <= end_date]
            if not (start_date or end_date):  # 如果没有指定日期范围，则获取最近的K线
                df = df.tail(limit)
            
            return df
            
        except Exception as e:
            logger.error(f"获取K线数据失败: {str(e)}")
            raise
    
    def get_realtime_quotes(self) -> pd.DataFrame:
        """获取所有期货实时行情"""
        try:
            df = ef.futures.get_realtime_quotes()
            logger.info(f"获取到{len(df)}条实时行情数据")
            return df
        except Exception as e:
            logger.error(f"获取实时行情失败: {str(e)}")
            raise
    
    def get_available_symbols(self) -> List[str]:
        """获取可用的期货代码列表"""
        return self.futures_info['期货代码'].tolist()


if __name__ == "__main__":
    
    try:
        # 创建数据获取器
        fetcher = EFinanceDataFetcher()
        
        # 获取鸡蛋期货主力连续合约信息
        symbol = 'jdm'
        info = fetcher.get_symbol_info(symbol)
        print(f"\n鸡蛋期货主力合约信息:\n{info}")
        
        # 测试不同周期数据获取
        for timeframe in fetcher.timeframes:
            print(f"\n获取 {timeframe} 数据:")
            df = fetcher.get_klines(
                symbol=symbol,
                timeframe=timeframe,
                start_date='2023-01-01',
                end_date='2024-03-19'
            )
            print(f"获取到 {len(df)} 条数据")
            print(df.head(3))
        
    except Exception as e:
        logger.error(f"测试运行失败: {str(e)}") 