//+------------------------------------------------------------------+
//|                                          Analysis_PriceAction.mqh |
//|                                         Copyright © 2025, htazq |
//|                                         https://github.com/htazq |
//+------------------------------------------------------------------+
#property copyright "Copyright © 2025, htazq"
#property link      "https://github.com/htazq"

#include "Defines.mqh"

//+------------------------------------------------------------------+
//| 价格行为分析命名空间                                               |
//+------------------------------------------------------------------+
namespace PriceAction
{
    //+------------------------------------------------------------------+
    //| 私有变量                                                          |
    //+------------------------------------------------------------------+
    static int low_setup_counter = 0;          // L1/L2信号计数器
    static int high_setup_counter = 0;         // H1/H2信号计数器
    static ENUM_TREND_STATE prev_trend_state = TREND_RANGE;  // 前一个趋势状态
    static datetime last_signal_time = 0;      // 最后信号时间
    static ENUM_PA_SIGNAL_TYPE last_signal_type = PA_SIGNAL_NONE;  // 最后信号类型
    
    // Vegas Tunnel均线句柄
    static int vegas_fast_handle = INVALID_HANDLE;
    static int vegas_medium_handle = INVALID_HANDLE;
    static int vegas_slow_handle = INVALID_HANDLE;
    
    // 信号历史记录结构
    struct SignalRecord
    {
        datetime time;                  // 信号时间
        ENUM_PA_SIGNAL_TYPE type;      // 信号类型
        double price;                   // 信号价格
        int bar_index;                  // K线索引
        bool is_valid;                  // 是否有效
    };
    
    static SignalRecord signal_history[PA_MAX_SIGNAL_COUNT];
    static int signal_count = 0;
    
    //+------------------------------------------------------------------+
    //| 初始化价格行为分析模块                                             |
    //+------------------------------------------------------------------+
    void Initialize()
    {
        // 创建Vegas Tunnel均线句柄
        vegas_fast_handle = iMA(_Symbol, _Period, VEGAS_EMA_FAST, 0, MODE_EMA, PRICE_CLOSE);
        vegas_medium_handle = iMA(_Symbol, _Period, VEGAS_EMA_MEDIUM, 0, MODE_EMA, PRICE_CLOSE);
        vegas_slow_handle = iMA(_Symbol, _Period, VEGAS_EMA_SLOW, 0, MODE_EMA, PRICE_CLOSE);
        
        if(vegas_fast_handle == INVALID_HANDLE || vegas_medium_handle == INVALID_HANDLE || vegas_slow_handle == INVALID_HANDLE)
        {
            Print("错误：无法创建Vegas Tunnel均线句柄");
            return;
        }
        
        // 初始化信号历史
        for(int i = 0; i < PA_MAX_SIGNAL_COUNT; i++)
        {
            signal_history[i].time = 0;
            signal_history[i].type = PA_SIGNAL_NONE;
            signal_history[i].price = 0.0;
            signal_history[i].bar_index = -1;
            signal_history[i].is_valid = false;
        }
        
        // 重置计数器
        low_setup_counter = 0;
        high_setup_counter = 0;
        signal_count = 0;
        prev_trend_state = TREND_RANGE;
        last_signal_time = 0;
        last_signal_type = PA_SIGNAL_NONE;
        
        DEBUG_PRINT("价格行为分析模块初始化完成");
    }
    
    //+------------------------------------------------------------------+
    //| 清理价格行为分析模块                                               |
    //+------------------------------------------------------------------+
    void Deinitialize()
    {
        // 释放均线句柄
        if(vegas_fast_handle != INVALID_HANDLE)
        {
            IndicatorRelease(vegas_fast_handle);
            vegas_fast_handle = INVALID_HANDLE;
        }
        if(vegas_medium_handle != INVALID_HANDLE)
        {
            IndicatorRelease(vegas_medium_handle);
            vegas_medium_handle = INVALID_HANDLE;
        }
        if(vegas_slow_handle != INVALID_HANDLE)
        {
            IndicatorRelease(vegas_slow_handle);
            vegas_slow_handle = INVALID_HANDLE;
        }
        
        // 删除信号标记对象
        for(int i = 0; i < signal_count; i++)
        {
            string obj_name = "";
            switch(signal_history[i].type)
            {
                case PA_SIGNAL_L1: obj_name = OBJ_L1_SIGNAL + IntegerToString(i); break;
                case PA_SIGNAL_L2: obj_name = OBJ_L2_SIGNAL + IntegerToString(i); break;
                case PA_SIGNAL_H1: obj_name = OBJ_H1_SIGNAL + IntegerToString(i); break;
                case PA_SIGNAL_H2: obj_name = OBJ_H2_SIGNAL + IntegerToString(i); break;
            }
            if(obj_name != "") ObjectDelete(0, obj_name);
        }
        
        DEBUG_PRINT("价格行为分析模块已清理");
    }
    
    //+------------------------------------------------------------------+
    //| 获取Vegas Tunnel趋势状态                                           |
    //+------------------------------------------------------------------+
    ENUM_TREND_STATE GetTrendFromVegasIndicator()
    {
        if(vegas_fast_handle == INVALID_HANDLE || vegas_medium_handle == INVALID_HANDLE || vegas_slow_handle == INVALID_HANDLE)
            return TREND_RANGE;
        
        double fast_ma[3], medium_ma[3], slow_ma[3];
        
        // 获取最近3根K线的均线数据用于判断倾斜方向
        if(CopyBuffer(vegas_fast_handle, 0, 0, 3, fast_ma) <= 0 ||
           CopyBuffer(vegas_medium_handle, 0, 0, 3, medium_ma) <= 0 ||
           CopyBuffer(vegas_slow_handle, 0, 0, 3, slow_ma) <= 0)
        {
            return TREND_RANGE;
        }
        
        // 当前均线值
        double fast_current = fast_ma[0];
        double medium_current = medium_ma[0];
        double slow_current = slow_ma[0];
        
        // 前一根K线均线值
        double fast_prev = fast_ma[1];
        double medium_prev = medium_ma[1];
        double slow_prev = slow_ma[1];
        
        // 判断上升趋势：短期>中期>长期均线，且所有均线向上倾斜
        if(fast_current > medium_current && medium_current > slow_current)
        {
            // 检查均线是否向上倾斜
            if(fast_current > fast_prev && medium_current > medium_prev && slow_current > slow_prev)
            {
                return TREND_UP;
            }
        }
        
        // 判断下降趋势：短期<中期<长期均线，且所有均线向下倾斜
        if(fast_current < medium_current && medium_current < slow_current)
        {
            // 检查均线是否向下倾斜
            if(fast_current < fast_prev && medium_current < medium_prev && slow_current < slow_prev)
            {
                return TREND_DOWN;
            }
        }
        
        // 其他情况为震荡
        return TREND_RANGE;
    }
    
    //+------------------------------------------------------------------+
    //| 检查L1/L2做多信号                                                  |
    //+------------------------------------------------------------------+
    bool CheckL1L2Signal_Internal(int bar_index)
    {
        if(bar_index < 1) return false;
        
        // 获取当前K线和前一根K线数据
        double current_low = iLow(_Symbol, _Period, bar_index);
        double current_high = iHigh(_Symbol, _Period, bar_index);
        double current_open = iOpen(_Symbol, _Period, bar_index);
        double current_close = iClose(_Symbol, _Period, bar_index);
        
        double prev_low = iLow(_Symbol, _Period, bar_index + 1);
        
        // 条件1：回调发生 - 当前K线最低点低于前一根K线最低点
        bool pullback_occurred = (current_low < prev_low);
        
        // 条件2：回调失败 - 当前K线收盘看涨
        bool pullback_failed = false;
        
        // 检查K线实体大小，避免十字星等无效信号
        double body_size = MathAbs(current_close - current_open);
        double total_range = current_high - current_low;
        double body_ratio = (total_range > 0) ? body_size / total_range : 0;
        
        if(body_ratio >= PA_SIGNAL_MIN_BODY_RATIO)
        {
            // 方法1：收盘价 > 开盘价（阳线）
            if(current_close > current_open)
                pullback_failed = true;
            
            // 方法2：收盘价 > 中位价（更宽松的条件）
            else if(current_close > (current_high + current_low) / 2)
                pullback_failed = true;
        }
        
        // 同时满足两个条件才形成信号
        return (pullback_occurred && pullback_failed);
    }
    
    //+------------------------------------------------------------------+
    //| 检查H1/H2做空信号                                                  |
    //+------------------------------------------------------------------+
    bool CheckH1H2Signal_Internal(int bar_index)
    {
        if(bar_index < 1) return false;
        
        // 获取当前K线和前一根K线数据
        double current_high = iHigh(_Symbol, _Period, bar_index);
        double current_low = iLow(_Symbol, _Period, bar_index);
        double current_open = iOpen(_Symbol, _Period, bar_index);
        double current_close = iClose(_Symbol, _Period, bar_index);
        
        double prev_high = iHigh(_Symbol, _Period, bar_index + 1);
        
        // 条件1：反弹发生 - 当前K线最高点高于前一根K线最高点
        bool bounce_occurred = (current_high > prev_high);
        
        // 条件2：反弹失败 - 当前K线收盘看跌
        bool bounce_failed = false;
        
        // 检查K线实体大小
        double body_size = MathAbs(current_close - current_open);
        double total_range = current_high - current_low;
        double body_ratio = (total_range > 0) ? body_size / total_range : 0;
        
        if(body_ratio >= PA_SIGNAL_MIN_BODY_RATIO)
        {
            // 方法1：收盘价 < 开盘价（阴线）
            if(current_close < current_open)
                bounce_failed = true;
            
            // 方法2：收盘价 < 中位价（更宽松的条件）
            else if(current_close < (current_high + current_low) / 2)
                bounce_failed = true;
        }
        
        // 同时满足两个条件才形成信号
        return (bounce_occurred && bounce_failed);
    }
    
    //+------------------------------------------------------------------+
    //| 创建信号标记对象                                                   |
    //+------------------------------------------------------------------+
    void CreateSignalMarker(ENUM_PA_SIGNAL_TYPE signal_type, datetime time, double price, int index)
    {
        string obj_name = "";
        string label_text = "";
        color marker_color = clrWhite;
        
        switch(signal_type)
        {
            case PA_SIGNAL_L1:
                obj_name = OBJ_L1_SIGNAL + IntegerToString(index);
                label_text = "L1";
                marker_color = COLOR_SUCCESS;
                break;
            case PA_SIGNAL_L2:
                obj_name = OBJ_L2_SIGNAL + IntegerToString(index);
                label_text = "L2";
                marker_color = COLOR_SUCCESS;
                break;
            case PA_SIGNAL_H1:
                obj_name = OBJ_H1_SIGNAL + IntegerToString(index);
                label_text = "H1";
                marker_color = COLOR_DANGER;
                break;
            case PA_SIGNAL_H2:
                obj_name = OBJ_H2_SIGNAL + IntegerToString(index);
                label_text = "H2";
                marker_color = COLOR_DANGER;
                break;
            default:
                return;
        }
        
        // 创建箭头标记
        if(ObjectCreate(0, obj_name, OBJ_ARROW, 0, time, price))
        {
            ObjectSetInteger(0, obj_name, OBJPROP_COLOR, marker_color);
            ObjectSetInteger(0, obj_name, OBJPROP_WIDTH, 3);
            ObjectSetInteger(0, obj_name, OBJPROP_BACK, false);
            ObjectSetInteger(0, obj_name, OBJPROP_SELECTABLE, false);
            ObjectSetInteger(0, obj_name, OBJPROP_HIDDEN, true);
            
            // 设置箭头方向
            if(signal_type == PA_SIGNAL_L1 || signal_type == PA_SIGNAL_L2)
            {
                ObjectSetInteger(0, obj_name, OBJPROP_ARROWCODE, 233); // 向上箭头
                ObjectSetInteger(0, obj_name, OBJPROP_ANCHOR, ANCHOR_TOP);
            }
            else
            {
                ObjectSetInteger(0, obj_name, OBJPROP_ARROWCODE, 234); // 向下箭头
                ObjectSetInteger(0, obj_name, OBJPROP_ANCHOR, ANCHOR_BOTTOM);
            }
            
            // 添加文本标签
            string text_obj_name = obj_name + "_Text";
            if(ObjectCreate(0, text_obj_name, OBJ_TEXT, 0, time, price))
            {
                ObjectSetString(0, text_obj_name, OBJPROP_TEXT, label_text);
                ObjectSetInteger(0, text_obj_name, OBJPROP_COLOR, marker_color);
                ObjectSetInteger(0, text_obj_name, OBJPROP_FONTSIZE, 10);
                ObjectSetInteger(0, text_obj_name, OBJPROP_BACK, false);
                ObjectSetInteger(0, text_obj_name, OBJPROP_SELECTABLE, false);
                ObjectSetInteger(0, text_obj_name, OBJPROP_HIDDEN, true);
                
                if(signal_type == PA_SIGNAL_L1 || signal_type == PA_SIGNAL_L2)
                    ObjectSetInteger(0, text_obj_name, OBJPROP_ANCHOR, ANCHOR_UPPER);
                else
                    ObjectSetInteger(0, text_obj_name, OBJPROP_ANCHOR, ANCHOR_LOWER);
            }
        }
    }
    
    //+------------------------------------------------------------------+
    //| 记录信号到历史                                                     |
    //+------------------------------------------------------------------+
    void RecordSignal(ENUM_PA_SIGNAL_TYPE signal_type, datetime time, double price, int bar_index)
    {
        if(signal_count >= PA_MAX_SIGNAL_COUNT)
        {
            // 移除最旧的信号记录
            for(int i = 0; i < PA_MAX_SIGNAL_COUNT - 1; i++)
            {
                signal_history[i] = signal_history[i + 1];
            }
            signal_count = PA_MAX_SIGNAL_COUNT - 1;
        }
        
        // 添加新信号
        signal_history[signal_count].time = time;
        signal_history[signal_count].type = signal_type;
        signal_history[signal_count].price = price;
        signal_history[signal_count].bar_index = bar_index;
        signal_history[signal_count].is_valid = true;
        
        // 创建图形标记
        CreateSignalMarker(signal_type, time, price, signal_count);
        
        signal_count++;
        
        DEBUG_PRINT(StringFormat("记录价格行为信号: %s, 时间: %s, 价格: %.5f", 
                    EnumToString(signal_type), TimeToString(time), price));
    }
    
    //+------------------------------------------------------------------+
    //| 主分析函数                                                         |
    //+------------------------------------------------------------------+
    string Analyze(ENUM_TREND_STATE current_trend, int bar_index_to_check = 1)
    {
        string analysis_result = "";
        
        // 检查趋势状态变化，重置计数器
        if(current_trend != prev_trend_state)
        {
            low_setup_counter = 0;
            high_setup_counter = 0;
            prev_trend_state = current_trend;
            DEBUG_PRINT(StringFormat("趋势状态变化: %s, 重置信号计数器", EnumToString(current_trend)));
        }
        
        // 获取当前K线时间，避免重复分析同一根K线
        datetime current_bar_time = iTime(_Symbol, _Period, bar_index_to_check);
        if(current_bar_time <= last_signal_time)
        {
            // 返回上次的分析结果
            if(last_signal_type != PA_SIGNAL_NONE)
                return EnumToString(last_signal_type);
            else
                return "无信号";
        }
        
        ENUM_PA_SIGNAL_TYPE detected_signal = PA_SIGNAL_NONE;
        
        // 在上升趋势中寻找L1/L2做多信号
        if(current_trend == TREND_UP)
        {
            if(CheckL1L2Signal_Internal(bar_index_to_check))
            {
                low_setup_counter++;
                
                if(low_setup_counter == 1)
                {
                    detected_signal = PA_SIGNAL_L1;
                    analysis_result = "L1做多信号";
                }
                else if(low_setup_counter == 2)
                {
                    detected_signal = PA_SIGNAL_L2;
                    analysis_result = "L2做多信号";
                }
                else
                {
                    // 超过L2后，重置计数器
                    low_setup_counter = 1;
                    detected_signal = PA_SIGNAL_L1;
                    analysis_result = "L1做多信号 (重置)";
                }
                
                // 记录信号
                double signal_price = iClose(_Symbol, _Period, bar_index_to_check);
                RecordSignal(detected_signal, current_bar_time, signal_price, bar_index_to_check);
            }
        }
        
        // 在下降趋势中寻找H1/H2做空信号
        else if(current_trend == TREND_DOWN)
        {
            if(CheckH1H2Signal_Internal(bar_index_to_check))
            {
                high_setup_counter++;
                
                if(high_setup_counter == 1)
                {
                    detected_signal = PA_SIGNAL_H1;
                    analysis_result = "H1做空信号";
                }
                else if(high_setup_counter == 2)
                {
                    detected_signal = PA_SIGNAL_H2;
                    analysis_result = "H2做空信号";
                }
                else
                {
                    // 超过H2后，重置计数器
                    high_setup_counter = 1;
                    detected_signal = PA_SIGNAL_H1;
                    analysis_result = "H1做空信号 (重置)";
                }
                
                // 记录信号
                double signal_price = iClose(_Symbol, _Period, bar_index_to_check);
                RecordSignal(detected_signal, current_bar_time, signal_price, bar_index_to_check);
            }
        }
        
        // 更新最后信号信息
        last_signal_time = current_bar_time;
        last_signal_type = detected_signal;
        
        // 如果没有检测到信号
        if(analysis_result == "")
        {
            analysis_result = "无信号";
        }
        
        return analysis_result;
    }
    
    //+------------------------------------------------------------------+
    //| 获取最后检测到的信号类型                                           |
    //+------------------------------------------------------------------+
    ENUM_PA_SIGNAL_TYPE GetLastSignalType()
    {
        return last_signal_type;
    }
    
    //+------------------------------------------------------------------+
    //| 获取信号计数器状态                                                 |
    //+------------------------------------------------------------------+
    string GetCounterStatus()
    {
        return StringFormat("L计数: %d, H计数: %d", low_setup_counter, high_setup_counter);
    }
    
    //+------------------------------------------------------------------+
    //| 获取信号历史                                                       |
    //+------------------------------------------------------------------+
    string GetSignalHistory(int max_records = 3)
    {
        string history_text = "";
        int records_to_show = MathMin(signal_count, max_records);
        
        for(int i = signal_count - records_to_show; i < signal_count; i++)
        {
            if(i >= 0 && signal_history[i].is_valid)
            {
                history_text += StringFormat("%s@%.5f ", 
                    EnumToString(signal_history[i].type), 
                    signal_history[i].price);
            }
        }
        
        return history_text;
    }
}