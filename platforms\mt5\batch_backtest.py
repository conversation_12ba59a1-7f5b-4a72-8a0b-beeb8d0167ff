"""
MT5批量回测脚本
用于自动化测试多个品种、多个时间周期组合的回测性能
"""
import os
import sys
import time
import logging
import argparse
from datetime import datetime, timedelta
from itertools import combinations
import pandas as pd
import matplotlib.pyplot as plt
import MetaTrader5 as mt5
from mt5_backtest import MT5Backtester, convert_timeframe_input

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(os.path.join(os.path.dirname(os.path.abspath(__file__)), 'batch_backtest.log')),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('batch_backtest')

class BatchBacktester:
    """批量回测管理器"""
    
    def __init__(self, symbols, timeframes, start_date, end_date, initial_balance=10000.0, direction="both", custom_sl_tp_settings=None):
        """
        初始化批量回测管理器
        
        参数:
            symbols: 回测品种列表
            timeframes: 回测时间周期列表
            start_date: 回测开始日期
            end_date: 回测结束日期
            initial_balance: 初始账户余额
            direction: 交易方向，可选值为 "long"、"short" 或 "both"
            custom_sl_tp_settings: 自定义止盈止损设置
        """
        self.symbols = symbols
        self.timeframes = timeframes
        self.start_date = start_date
        self.end_date = end_date
        self.initial_balance = initial_balance
        self.direction = direction
        self.custom_sl_tp_settings = custom_sl_tp_settings or {}
        self.results = []
        
        # 创建结果目录
        self.results_dir = os.path.join(
            os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))),
            'results', 'batch_backtest', datetime.now().strftime('%Y%m%d_%H%M%S')
        )
        os.makedirs(self.results_dir, exist_ok=True)
        logger.info(f"批量回测结果将保存在: {self.results_dir}")
        
        # 记录自定义止盈止损设置
        if self.custom_sl_tp_settings:
            logger.info(f"使用自定义止盈止损设置: {self.custom_sl_tp_settings}")

    def run_single_timeframe_tests(self):
        """运行单一时间周期的回测"""
        logger.info("开始单一时间周期回测...")
        
        for symbol in self.symbols:
            for timeframe in self.timeframes:
                timeframe_name = self._get_timeframe_name(timeframe)
                logger.info(f"开始回测 {symbol} 在 {timeframe_name} 时间周期")
                
                # 创建回测器并传递自定义止盈止损设置
                backtester = MT5Backtester(
                    symbols=[symbol],
                    timeframes=[timeframe],
                    start_date=self.start_date,
                    end_date=self.end_date,
                    initial_balance=self.initial_balance,
                    direction=self.direction,
                    custom_sl_tp_settings=self.custom_sl_tp_settings
                )
                
                # 运行回测
                start_time = time.time()
                success = backtester.run_backtest()
                end_time = time.time()
                
                if success:
                    # 保存结果
                    result_folder = os.path.join(self.results_dir, f"{symbol}_{timeframe_name}")
                    os.makedirs(result_folder, exist_ok=True)
                    backtester.save_results(folder_path=result_folder)
                    
                    # 记录结果
                    self.results.append({
                        'symbol': symbol,
                        'timeframes': [timeframe_name],
                        'net_profit': backtester.stats['net_profit'],
                        'return_percent': backtester.stats['return_percent'],
                        'win_rate': backtester.stats['win_rate'],
                        'profit_factor': backtester.stats['profit_factor'],
                        'max_drawdown_percent': backtester.stats['max_drawdown_percent'],
                        'sharpe_ratio': backtester.stats.get('sharpe_ratio', 0),
                        'total_trades': backtester.stats['total_trades'],
                        'margin_call': backtester.stats.get('margin_call', False),
                        'execution_time': end_time - start_time
                    })
                    
                    logger.info(f"完成 {symbol} 在 {timeframe_name} 时间周期的回测，"
                               f"净利润: ${backtester.stats['net_profit']:.2f}, "
                               f"收益率: {backtester.stats['return_percent']:.2f}%, "
                               f"耗时: {end_time - start_time:.2f}秒")
                else:
                    logger.error(f"{symbol} 在 {timeframe_name} 时间周期的回测失败")
        
        logger.info("单一时间周期回测完成")
    
    def run_combined_timeframe_tests(self):
        """运行组合时间周期的回测"""
        logger.info("开始组合时间周期回测...")
        
        # 生成时间周期组合
        timeframe_combinations = []
        for i in range(2, len(self.timeframes) + 1):
            # 只考虑按顺序的组合，例如[15M, 1H]，而不是[1H, 15M]
            # 这是因为我们通常希望较小的时间周期在前，较大的时间周期在后
            timeframe_combinations.append(self.timeframes[:i])
        
        for symbol in self.symbols:
            for timeframe_combo in timeframe_combinations:
                timeframe_names = [self._get_timeframe_name(tf) for tf in timeframe_combo]
                combo_name = "_".join(timeframe_names)
                logger.info(f"开始回测 {symbol} 在 {combo_name} 时间周期组合")
                
                # 创建回测器并传递自定义止盈止损设置
                backtester = MT5Backtester(
                    symbols=[symbol],
                    timeframes=list(combo),
                    start_date=self.start_date,
                    end_date=self.end_date,
                    initial_balance=self.initial_balance,
                    direction=self.direction,
                    custom_sl_tp_settings=self.custom_sl_tp_settings
                )
                
                # 运行回测
                start_time = time.time()
                success = backtester.run_backtest()
                end_time = time.time()
                
                if success:
                    # 保存结果
                    result_folder = os.path.join(self.results_dir, f"{symbol}_{combo_name}")
                    os.makedirs(result_folder, exist_ok=True)
                    backtester.save_results(folder_path=result_folder)
                    
                    # 记录结果
                    self.results.append({
                        'symbol': symbol,
                        'timeframes': timeframe_names,
                        'net_profit': backtester.stats['net_profit'],
                        'return_percent': backtester.stats['return_percent'],
                        'win_rate': backtester.stats['win_rate'],
                        'profit_factor': backtester.stats['profit_factor'],
                        'max_drawdown_percent': backtester.stats['max_drawdown_percent'],
                        'sharpe_ratio': backtester.stats.get('sharpe_ratio', 0),
                        'total_trades': backtester.stats['total_trades'],
                        'margin_call': backtester.stats.get('margin_call', False),
                        'execution_time': end_time - start_time
                    })
                    
                    logger.info(f"完成 {symbol} 在 {combo_name} 时间周期组合的回测，"
                               f"净利润: ${backtester.stats['net_profit']:.2f}, "
                               f"收益率: {backtester.stats['return_percent']:.2f}%, "
                               f"耗时: {end_time - start_time:.2f}秒")
                else:
                    logger.error(f"{symbol} 在 {combo_name} 时间周期组合的回测失败")
        
        logger.info("组合时间周期回测完成")
    
    def generate_summary_report(self):
        """生成汇总报告"""
        if not self.results:
            logger.warning("没有回测结果，无法生成报告")
            return
        
        logger.info("生成汇总报告...")
        
        # 创建结果DataFrame
        results_df = pd.DataFrame(self.results)
        
        # 保存结果CSV
        csv_path = os.path.join(self.results_dir, 'summary_results.csv')
        results_df.to_csv(csv_path, index=False)
        
        # 按收益率排序，但将爆仓的结果放在最后
        # 先按是否爆仓排序，再按收益率排序
        results_df = results_df.sort_values(['margin_call', 'return_percent'], ascending=[True, False])
        
        # 生成汇总报告
        report_path = os.path.join(self.results_dir, 'summary_report.txt')
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("="*80 + "\n")
            f.write(f"{'批量回测汇总报告':^80}\n")
            f.write("="*80 + "\n")
            f.write(f"回测周期: {self.start_date.strftime('%Y-%m-%d')} 至 {self.end_date.strftime('%Y-%m-%d')}\n")
            f.write(f"初始资金: ${self.initial_balance:.2f}\n")
            f.write(f"交易方向: {self.direction}\n")
            f.write(f"回测品种: {', '.join(self.symbols)}\n")
            f.write(f"时间周期: {', '.join([self._get_timeframe_name(tf) for tf in self.timeframes])}\n")
            f.write(f"总回测次数: {len(self.results)}\n")
            f.write(f"爆仓次数: {results_df['margin_call'].sum()}\n")  # 添加爆仓次数统计
            f.write("\n")
            
            # 最佳组合
            best_result = results_df.iloc[0]
            f.write("最佳回测组合:\n")
            f.write(f"品种: {best_result['symbol']}\n")
            f.write(f"时间周期: {', '.join(best_result['timeframes'])}\n")
            f.write(f"净利润: ${best_result['net_profit']:.2f}\n")
            f.write(f"收益率: {best_result['return_percent']:.2f}%\n")
            f.write(f"胜率: {best_result['win_rate']:.2f}%\n")
            f.write(f"盈亏比: {best_result['profit_factor']:.2f}\n")
            f.write(f"最大回撤: {best_result['max_drawdown_percent']:.2f}%\n")
            f.write(f"夏普比率: {best_result['sharpe_ratio']:.2f}\n")
            f.write(f"总交易次数: {best_result['total_trades']}\n")
            f.write(f"是否爆仓: {'是' if best_result['margin_call'] else '否'}\n")
            f.write("\n")
            
            # 按品种汇总
            f.write("各品种最佳组合:\n")
            for symbol in self.symbols:
                symbol_results = results_df[results_df['symbol'] == symbol]
                if not symbol_results.empty:
                    best_symbol_result = symbol_results.iloc[0]
                    f.write(f"{symbol}: {', '.join(best_symbol_result['timeframes'])}, "
                           f"收益率: {best_symbol_result['return_percent']:.2f}%, "
                           f"净利润: ${best_symbol_result['net_profit']:.2f}\n")
            f.write("\n")
            
            # 前10名结果
            # 前10名结果
            f.write("前10名回测结果:\n")
            f.write(f"{'排名':^4}|{'品种':^10}|{'时间周期':^20}|{'收益率':^10}|{'净利润':^10}|{'胜率':^8}|{'盈亏比':^8}|{'最大回撤':^8}|{'交易次数':^8}|{'爆仓':^6}\n")
            f.write("-"*96 + "\n")
            
            for i, row in results_df.head(10).iterrows():
                timeframes_str = ", ".join(row['timeframes'])
                if len(timeframes_str) > 18:
                    timeframes_str = timeframes_str[:15] + "..."
                
                f.write(f"{i+1:^4}|{row['symbol']:^10}|{timeframes_str:^20}|"
                       f"{row['return_percent']:^10.2f}%|${row['net_profit']:^9.2f}|"
                       f"{row['win_rate']:^8.2f}%|{row['profit_factor']:^8.2f}|"
                       f"{row['max_drawdown_percent']:^8.2f}%|{row['total_trades']:^8}|{'是' if row['margin_call'] else '否':^6}\n")
            
            # 添加爆仓情况统计
            f.write("\n爆仓情况统计:\n")
            margin_call_stats = results_df.groupby(['symbol', 'margin_call']).size().unstack(fill_value=0)
            if True in margin_call_stats.columns:
                margin_call_percent = margin_call_stats[True] / (margin_call_stats[True] + margin_call_stats[False]) * 100
                f.write(f"{'品种':^10}|{'爆仓次数':^10}|{'总测试次数':^12}|{'爆仓率':^8}\n")
                f.write("-"*44 + "\n")
                for symbol in self.symbols:
                    if symbol in margin_call_percent.index:
                        total_tests = margin_call_stats.loc[symbol, True] + margin_call_stats.loc[symbol, False]
                        f.write(f"{symbol:^10}|{margin_call_stats.loc[symbol, True]:^10}|{total_tests:^12}|{margin_call_percent[symbol]:^8.2f}%\n")
        # 绘制结果对比图
        self._plot_comparison_charts()
        
        logger.info(f"汇总报告已生成: {report_path}")
    
    def _plot_comparison_charts(self):
        """绘制结果对比图表"""
        if not self.results:
            return
        
        results_df = pd.DataFrame(self.results)
        
        # 按品种和时间周期组合分组
        results_df['timeframes_str'] = results_df['timeframes'].apply(lambda x: ', '.join(x))
        
        # 1. 收益率对比图
        plt.figure(figsize=(12, 8))
        
        # 按品种分组
        for symbol in self.symbols:
            symbol_data = results_df[results_df['symbol'] == symbol]
            plt.plot(symbol_data['timeframes_str'], symbol_data['return_percent'], 
                    marker='o', label=symbol)
        
        plt.title('各品种在不同时间周期组合下的收益率对比')
        plt.xlabel('时间周期组合')
        plt.ylabel('收益率 (%)')
        plt.xticks(rotation=45)
        plt.grid(True)
        plt.legend()
        plt.tight_layout()
        plt.savefig(os.path.join(self.results_dir, 'return_comparison.png'))
        
        # 2. 收益率与最大回撤散点图
        plt.figure(figsize=(10, 8))
        
        for symbol in self.symbols:
            symbol_data = results_df[results_df['symbol'] == symbol]
            plt.scatter(symbol_data['max_drawdown_percent'], symbol_data['return_percent'], 
                       label=symbol, alpha=0.7, s=50)
        
        plt.title('收益率与最大回撤关系')
        plt.xlabel('最大回撤 (%)')
        plt.ylabel('收益率 (%)')
        plt.grid(True)
        plt.legend()
        plt.tight_layout()
        plt.savefig(os.path.join(self.results_dir, 'risk_return.png'))
        
        # 3. 胜率与盈亏比散点图
        plt.figure(figsize=(10, 8))
        
        for symbol in self.symbols:
            symbol_data = results_df[results_df['symbol'] == symbol]
            plt.scatter(symbol_data['win_rate'], symbol_data['profit_factor'], 
                       label=symbol, alpha=0.7, s=50)
        
        plt.title('胜率与盈亏比关系')
        plt.xlabel('胜率 (%)')
        plt.ylabel('盈亏比')
        plt.grid(True)
        plt.legend()
        plt.tight_layout()
        plt.savefig(os.path.join(self.results_dir, 'win_rate_profit_factor.png'))
        
        # 4. 交易次数与收益率散点图
        plt.figure(figsize=(10, 8))
        
        for symbol in self.symbols:
            symbol_data = results_df[results_df['symbol'] == symbol]
            plt.scatter(symbol_data['total_trades'], symbol_data['return_percent'], 
                       label=symbol, alpha=0.7, s=50)
        
        plt.title('交易次数与收益率关系')
        plt.xlabel('交易次数')
        plt.ylabel('收益率 (%)')
        plt.grid(True)
        plt.legend()
        plt.tight_layout()
        plt.savefig(os.path.join(self.results_dir, 'trades_return.png'))
        
        plt.close('all')
    
    def _get_timeframe_name(self, timeframe):
        """获取时间周期的名称"""
        # MT5时间周期常量到名称的映射
        timeframe_names = {
            1: "M1",
            5: "M5",
            15: "M15",
            30: "M30",
            16385: "H1",
            16388: "H4",
            16390: "H6",
            16392: "H12",
            16408: "D1",
            32769: "W1",
            49153: "MN1"
        }
        
        return timeframe_names.get(timeframe, str(timeframe))
    
    def run_all_tests(self):
        """运行所有回测"""
        logger.info("开始批量回测...")
        
        # 运行单一时间周期回测
        self.run_single_timeframe_tests()
        
        # 运行组合时间周期回测
        self.run_combined_timeframe_tests()
        
        # 生成汇总报告
        self.generate_summary_report()
        
        logger.info("批量回测完成")
        
        return self.results

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='MT5批量回测工具')
    parser.add_argument('--symbols', nargs='+', default=['XAUUSDc'], help='回测品种列表')
    parser.add_argument('--timeframes', nargs='+', default=['1h', '4h'], help='回测时间周期列表')
    parser.add_argument('--start_date', type=str, default=(datetime.now() - timedelta(days=90)).strftime('%Y-%m-%d'), help='回测开始日期 (YYYY-MM-DD)')
    parser.add_argument('--end_date', type=str, default=datetime.now().strftime('%Y-%m-%d'), help='回测结束日期 (YYYY-MM-DD)')
    parser.add_argument('--initial_balance', type=float, default=10000.0, help='初始账户余额')
    parser.add_argument('--direction', type=str, default='both', choices=['long', 'short', 'both'], help='交易方向')
    parser.add_argument('--mode', type=str, default='single', choices=['single', 'combined', 'both'], help='回测模式: single(单一时间周期), combined(组合时间周期), both(两者都执行)')
    parser.add_argument('--custom_sl_tp', action='store_true', help='使用自定义止盈止损设置')
    
    args = parser.parse_args()
    
    # 解析日期
    start_date = datetime.strptime(args.start_date, '%Y-%m-%d')
    end_date = datetime.strptime(args.end_date, '%Y-%m-%d')
    
    # 解析时间周期
    timeframes = [convert_timeframe_input(tf) for tf in args.timeframes]
    
    # 创建自定义止盈止损设置
    custom_sl_tp_settings = None
    if args.custom_sl_tp:
        custom_sl_tp_settings = {
            'XAUUSDc': {
                'default': {
                    'sl_type': 'fixed',
                    'sl_fixed_points': 3,  # 3个点止损
                    'tp_type': 'fixed',
                    'tp_fixed_points': 10  # 10个点止盈
                }
            },
            'BTCUSDc': {
                'default': {
                    'sl_type': 'fixed',
                    'sl_fixed_points': 50,  # 50个点止损
                    'tp_type': 'fixed',
                    'tp_fixed_points': 150  # 150个点止盈
                }
            }
        }
        logger.info("使用自定义止盈止损设置")
    
    # 创建批量回测器
    batch_tester = BatchBacktester(
        symbols=args.symbols,
        timeframes=timeframes,
        start_date=start_date,
        end_date=end_date,
        initial_balance=args.initial_balance,
        direction=args.direction,
        custom_sl_tp_settings=custom_sl_tp_settings
    )
    
    # 运行回测
    if args.mode in ['single', 'both']:
        batch_tester.run_single_timeframe_tests()
    
    if args.mode in ['combined', 'both']:
        batch_tester.run_combined_timeframe_tests()
    
    # 生成汇总报告
    batch_tester.generate_summary_report()

if __name__ == "__main__":
    # 确保MT5已初始化
    if not mt5.initialize():
        logger.error("MT5初始化失败")
        sys.exit(1)
    
    try:
        main()
    finally:
        mt5.shutdown()