---
description: 
globs: 
alwaysApply: false
---
# 代码质量检查清单

## 提交前自检
每次重要代码提交前，验证：
- ✅ **需求合规性**: 完全满足当前任务需求和范围
- ✅ **Context7验证**: 按确认级别执行并正确标注
- ✅ **错误处理**: 完整的错误处理机制，符合用户配置策略
- ✅ **代码风格**: 与现有项目风格一致或遵循通用标准
- ✅ **依赖管理**: 没有引入不必要的复杂性或未声明的依赖
- ✅ **安全性能**: 基于Context7知识，无潜在安全或性能问题
- ✅ **文档**: 清晰充分的注释和文档（特别是复杂逻辑）

## 错误处理策略

### 自动修复（仅限简单任务）
- 常见、低风险的语法错误或已知问题
- 必须记录修复过程和理由，修复后通知用户

### 报告后修复（标准模式默认）  
- 首先提供详细问题报告、原因分析和建议修复方案
- 等待用户确认后执行修复

### 停止并询问（复杂任务或用户要求）
- 对任何不确定或高风险错误立即停止
- 请求用户指导和明确方向

## 质量保证机制

### 自动升级策略
- **错误触发升级**: AI代码在用户环境中失败时，自动将Context7验证升级到最严格级别
- **风险警告**: 对有深远影响的决策主动警告潜在风险，建议验证升级
- **用户覆盖**: 用户可随时要求对任何信息进行更高验证标准

### 代码标准
- **健壮性**: 包含适当的错误处理（try-catch、空值检查、边界处理）
- **清晰性**: 编写自文档化代码，变量名和函数目的清晰
- **可维护性**: 构建易于未来修改和扩展的代码结构
- **性能**: 考虑基本性能影响，避免明显低效
- **安全性**: 遵循基本安全实践，避免常见漏洞

### 文档要求
- **复杂逻辑**: 为非显而易见的代码添加清晰的行内注释
- **公共接口**: 记录所有公共函数、类和API
- **架构决策**: 注释重要的设计选择和权衡
- **依赖关系**: 记录外部依赖及其目的
- **版本信息**: 包含Context7验证详情和知识来源


