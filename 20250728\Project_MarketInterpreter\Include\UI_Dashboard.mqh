//+------------------------------------------------------------------+
//|                                                  UI_Dashboard.mqh |
//|                                         Copyright © 2025, htazq |
//|                                         https://github.com/htazq |
//+------------------------------------------------------------------+
#property copyright "Copyright © 2025, htazq"
#property link      "https://github.com/htazq"

#include "Defines.mqh"

//+------------------------------------------------------------------+
//| 仪表盘UI命名空间                                                   |
//+------------------------------------------------------------------+
namespace Dashboard
{
    //+------------------------------------------------------------------+
    //| 私有变量                                                          |
    //+------------------------------------------------------------------+
    static int dashboard_x = DASHBOARD_X;
    static int dashboard_y = DASHBOARD_Y;
    static int font_size = FONT_SIZE_CONTENT;
    static bool is_initialized = false;
    
    // 面板位置计算
    struct PanelPosition
    {
        int x, y, width, height;
    };
    
    static PanelPosition trend_panel;
    static PanelPosition signal_panel;
    static PanelPosition session_panel;
    static PanelPosition atr_panel;
    static PanelPosition keylevels_panel;
    static PanelPosition alert_panel;
    
    //+------------------------------------------------------------------+
    //| 计算面板位置                                                       |
    //+------------------------------------------------------------------+
    void CalculatePanelPositions()
    {
        int panel_width = DASHBOARD_WIDTH / 2 - PANEL_MARGIN;
        int panel_height = PANEL_HEIGHT;
        
        // 趋势分析面板 - 左上角
        trend_panel.x = dashboard_x;
        trend_panel.y = dashboard_y;
        trend_panel.width = panel_width;
        trend_panel.height = panel_height;
        
        // 信号识别面板 - 右上角
        signal_panel.x = dashboard_x + panel_width + PANEL_MARGIN;
        signal_panel.y = dashboard_y;
        signal_panel.width = panel_width;
        signal_panel.height = panel_height;
        
        // 交易时段面板 - 左中
        session_panel.x = dashboard_x;
        session_panel.y = dashboard_y + panel_height + PANEL_MARGIN;
        session_panel.width = panel_width;
        session_panel.height = panel_height;
        
        // ATR波动率面板 - 右中
        atr_panel.x = dashboard_x + panel_width + PANEL_MARGIN;
        atr_panel.y = dashboard_y + panel_height + PANEL_MARGIN;
        atr_panel.width = panel_width;
        atr_panel.height = panel_height;
        
        // 关键价位面板 - 左下
        keylevels_panel.x = dashboard_x;
        keylevels_panel.y = dashboard_y + 2 * (panel_height + PANEL_MARGIN);
        keylevels_panel.width = panel_width;
        keylevels_panel.height = panel_height * 2;
        
        // 警报面板 - 右下
        alert_panel.x = dashboard_x + panel_width + PANEL_MARGIN;
        alert_panel.y = dashboard_y + 2 * (panel_height + PANEL_MARGIN);
        alert_panel.width = panel_width;
        alert_panel.height = panel_height * 2;
    }
    
    //+------------------------------------------------------------------+
    //| 创建面板背景                                                       |
    //+------------------------------------------------------------------+
    void CreatePanelBackground(string name, PanelPosition pos, color bg_color = COLOR_BACKGROUND)
    {
        if(ObjectCreate(0, name, OBJ_RECTANGLE_LABEL, 0, 0, 0))
        {
            ObjectSetInteger(0, name, OBJPROP_XDISTANCE, pos.x);
            ObjectSetInteger(0, name, OBJPROP_YDISTANCE, pos.y);
            ObjectSetInteger(0, name, OBJPROP_XSIZE, pos.width);
            ObjectSetInteger(0, name, OBJPROP_YSIZE, pos.height);
            ObjectSetInteger(0, name, OBJPROP_BGCOLOR, bg_color);
            ObjectSetInteger(0, name, OBJPROP_BORDER_TYPE, BORDER_FLAT);
            ObjectSetInteger(0, name, OBJPROP_CORNER, CORNER_LEFT_UPPER);
            ObjectSetInteger(0, name, OBJPROP_COLOR, COLOR_ACCENT);
            ObjectSetInteger(0, name, OBJPROP_STYLE, STYLE_SOLID);
            ObjectSetInteger(0, name, OBJPROP_WIDTH, 1);
            ObjectSetInteger(0, name, OBJPROP_BACK, false);
            ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
            ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
        }
    }
    
    //+------------------------------------------------------------------+
    //| 创建文本标签                                                       |
    //+------------------------------------------------------------------+
    void CreateTextLabel(string name, int x, int y, string text, color text_color = COLOR_INFO, 
                        int size = FONT_SIZE_CONTENT, string font = "Arial")
    {
        if(ObjectCreate(0, name, OBJ_LABEL, 0, 0, 0))
        {
            ObjectSetInteger(0, name, OBJPROP_XDISTANCE, x);
            ObjectSetInteger(0, name, OBJPROP_YDISTANCE, y);
            ObjectSetString(0, name, OBJPROP_TEXT, text);
            ObjectSetString(0, name, OBJPROP_FONT, font);
            ObjectSetInteger(0, name, OBJPROP_FONTSIZE, size);
            ObjectSetInteger(0, name, OBJPROP_COLOR, text_color);
            ObjectSetInteger(0, name, OBJPROP_CORNER, CORNER_LEFT_UPPER);
            ObjectSetInteger(0, name, OBJPROP_ANCHOR, ANCHOR_LEFT_UPPER);
            ObjectSetInteger(0, name, OBJPROP_BACK, false);
            ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
            ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
        }
    }
    
    //+------------------------------------------------------------------+
    //| 更新文本标签                                                       |
    //+------------------------------------------------------------------+
    //| 设置仪表盘可见性                                                   |
    //+------------------------------------------------------------------+
    void SetVisible(bool visible)
    {
        // 设置所有仪表盘对象的可见性
        for(int i = ObjectsTotal(0) - 1; i >= 0; i--)
        {
            string obj_name = ObjectName(0, i);
            if(StringFind(obj_name, OBJ_PREFIX) == 0)
            {
                ObjectSetInteger(0, obj_name, OBJPROP_TIMEFRAMES, visible ? OBJ_ALL_PERIODS : OBJ_NO_PERIODS);
            }
        }
        
        DEBUG_PRINT(StringFormat("仪表盘可见性设置为: %s", visible ? "显示" : "隐藏"));
    }
    
    //+------------------------------------------------------------------+
    //| 清理仪表盘UI模块                                                   |
    //+------------------------------------------------------------------+
    void Deinitialize()
    {
        // 删除所有仪表盘对象
        ObjectsDeleteAll(0, OBJ_PREFIX);
        
        DEBUG_PRINT("仪表盘UI模块已清理");
    }
    
    //+------------------------------------------------------------------+
    //| 初始化仪表盘                                                       |
    //+------------------------------------------------------------------+
    void Initialize(int x = DASHBOARD_X, int y = DASHBOARD_Y, int font_sz = FONT_SIZE_CONTENT)
    {
        if(is_initialized) return;
        
        dashboard_x = x;
        dashboard_y = y;
        font_size = font_sz;
        
        // 计算面板位置
        CalculatePanelPositions();
        
        // 创建主背景
        PanelPosition main_bg;
        main_bg.x = dashboard_x - 5;
        main_bg.y = dashboard_y - 5;
        main_bg.width = DASHBOARD_WIDTH + 10;
        main_bg.height = DASHBOARD_HEIGHT + 10;
        CreatePanelBackground(OBJ_DASHBOARD_BACKGROUND, main_bg);
        
        // 创建各个面板背景
        CreatePanelBackground(OBJ_PREFIX + "TrendBG", trend_panel);
        CreatePanelBackground(OBJ_PREFIX + "SignalBG", signal_panel);
        CreatePanelBackground(OBJ_PREFIX + "SessionBG", session_panel);
        CreatePanelBackground(OBJ_PREFIX + "ATRBG", atr_panel);
        CreatePanelBackground(OBJ_PREFIX + "KeyLevelsBG", keylevels_panel);
        CreatePanelBackground(OBJ_PREFIX + "AlertBG", alert_panel);
        
        // 创建面板标题
        CreateTextLabel(OBJ_PREFIX + "TrendTitle", 
            trend_panel.x + 5, trend_panel.y + 5, 
            "趋势状态", COLOR_ACCENT, FONT_SIZE_TITLE, "Arial Bold");
            
        CreateTextLabel(OBJ_PREFIX + "SignalTitle", 
            signal_panel.x + 5, signal_panel.y + 5, 
            "价格行为", COLOR_ACCENT, FONT_SIZE_TITLE, "Arial Bold");
            
        CreateTextLabel(OBJ_PREFIX + "SessionTitle", 
            session_panel.x + 5, session_panel.y + 5, 
            "交易时段", COLOR_ACCENT, FONT_SIZE_TITLE, "Arial Bold");
            
        CreateTextLabel(OBJ_PREFIX + "ATRTitle", 
            atr_panel.x + 5, atr_panel.y + 5, 
            "波动率", COLOR_ACCENT, FONT_SIZE_TITLE, "Arial Bold");
            
        CreateTextLabel(OBJ_PREFIX + "KeyLevelsTitle", 
            keylevels_panel.x + 5, keylevels_panel.y + 5, 
            "关键价位", COLOR_ACCENT, FONT_SIZE_TITLE, "Arial Bold");
            
        CreateTextLabel(OBJ_PREFIX + "AlertTitle", 
            alert_panel.x + 5, alert_panel.y + 5, 
            "警报信息", COLOR_ACCENT, FONT_SIZE_TITLE, "Arial Bold");
        
        // 创建内容标签（初始为空）
        CreateTextLabel(OBJ_PREFIX + "TrendContent", 
            trend_panel.x + 5, trend_panel.y + 25, 
            "初始化中...", COLOR_INFO, font_size);
            
        CreateTextLabel(OBJ_PREFIX + "SignalContent", 
            signal_panel.x + 5, signal_panel.y + 25, 
            "等待信号...", COLOR_INFO, font_size);
            
        CreateTextLabel(OBJ_PREFIX + "SessionContent", 
            session_panel.x + 5, session_panel.y + 25, 
            "分析时段...", COLOR_INFO, font_size);
            
        CreateTextLabel(OBJ_PREFIX + "ATRContent", 
            atr_panel.x + 5, atr_panel.y + 25, 
            "计算中...", COLOR_INFO, font_size);
            
        CreateTextLabel(OBJ_PREFIX + "KeyLevelsContent", 
            keylevels_panel.x + 5, keylevels_panel.y + 25, 
            "加载价位...", COLOR_INFO, font_size);
            
        CreateTextLabel(OBJ_PREFIX + "AlertContent", 
            alert_panel.x + 5, alert_panel.y + 25, 
            "无警报", COLOR_INFO, font_size);
        
        is_initialized = true;
        DEBUG_PRINT("仪表盘UI初始化完成");
    }
    
    //+------------------------------------------------------------------+
    //| 渲染趋势状态面板                                                   |
    //+------------------------------------------------------------------+
    void RenderTrendPanel(ENUM_TREND_STATE trend, string additional_info = "")
    {
        string trend_text = "";
        color trend_color = COLOR_INFO;
        
        switch(trend)
        {
            case TREND_UP:
                trend_text = "上升趋势";
                trend_color = COLOR_TREND_UP;
                break;
            case TREND_DOWN:
                trend_text = "下降趋势";
                trend_color = COLOR_TREND_DOWN;
                break;
            case TREND_RANGE:
                trend_text = "震荡区间";
                trend_color = COLOR_TREND_RANGE;
                break;
        }
        
        if(additional_info != "")
            trend_text += "\n" + additional_info;
        
        UpdateTextLabel(OBJ_PREFIX + "TrendContent", trend_text, trend_color);
    }
    
    //+------------------------------------------------------------------+
    //| 渲染信号识别面板                                                   |
    //+------------------------------------------------------------------+
    void RenderSignalPanel(string pa_signal, string counter_status = "")
    {
        string signal_text = pa_signal;
        color signal_color = COLOR_INFO;
        
        // 根据信号类型设置颜色
        if(StringFind(pa_signal, "L1") >= 0 || StringFind(pa_signal, "L2") >= 0)
        {
            signal_color = COLOR_SUCCESS;
        }
        else if(StringFind(pa_signal, "H1") >= 0 || StringFind(pa_signal, "H2") >= 0)
        {
            signal_color = COLOR_DANGER;
        }
        
        if(counter_status != "")
            signal_text += "\n" + counter_status;
        
        UpdateTextLabel(OBJ_PREFIX + "SignalContent", signal_text, signal_color);
    }
    
    //+------------------------------------------------------------------+
    //| 渲染交易时段面板                                                   |
    //+------------------------------------------------------------------+
    void RenderSessionPanel(ENUM_TRADING_SESSION session, int activity_score = 0)
    {
        string session_name = "";
        color session_color = COLOR_INFO;
        
        switch(session)
        {
            case SESSION_ASIA:
                session_name = "亚洲时段";
                session_color = COLOR_SESSION_ASIA;
                break;
            case SESSION_LONDON:
                session_name = "伦敦时段";
                session_color = COLOR_SESSION_LONDON;
                break;
            case SESSION_NEWYORK:
                session_name = "纽约时段";
                session_color = COLOR_SESSION_NEWYORK;
                break;
            case SESSION_OVERLAP:
                session_name = "重叠时段";
                session_color = COLOR_SESSION_OVERLAP;
                break;
            default:
                session_name = "其他时段";
                session_color = COLOR_INFO;
                break;
        }
        
        string session_text = session_name;
        if(activity_score > 0)
            session_text += StringFormat("\n活跃度: %d%%", activity_score);
        
        UpdateTextLabel(OBJ_PREFIX + "SessionContent", session_text, session_color);
    }
    
    //+------------------------------------------------------------------+
    //| 渲染ATR波动率面板                                                  |
    //+------------------------------------------------------------------+
    void RenderATRPanel(double atr_value, string volatility_level = "", double percentile = 0)
    {
        string atr_text = StringFormat("ATR: %s", FORMAT_PRICE(atr_value));
        
        if(volatility_level != "")
            atr_text += "\n水平: " + volatility_level;
            
        if(percentile > 0)
            atr_text += StringFormat("\n百分位: %.1f%%", percentile);
        
        // 根据波动率水平设置颜色
        color atr_color = COLOR_INFO;
        if(volatility_level == "极高")
            atr_color = COLOR_DANGER;
        else if(volatility_level == "高")
            atr_color = COLOR_WARNING;
        else if(volatility_level == "中等")
            atr_color = COLOR_ACCENT;
        else if(volatility_level == "低" || volatility_level == "极低")
            atr_color = COLOR_SUCCESS;
        
        UpdateTextLabel(OBJ_PREFIX + "ATRContent", atr_text, atr_color);
    }
    
    //+------------------------------------------------------------------+
    //| 渲染关键价位面板                                                   |
    //+------------------------------------------------------------------+
    void RenderKeyLevelsPanel(double daily_open, double prev_high, double prev_low, 
                             double current_price = 0)
    {
        string levels_text = "";
        
        if(daily_open > 0)
        {
            levels_text += StringFormat("日开: %s", FORMAT_PRICE(daily_open));
            if(current_price > 0)
            {
                int distance = (int)MathRound((current_price - daily_open) / _Point);
                levels_text += StringFormat(" (%+d)", distance);
            }
            levels_text += "\n";
        }
        
        if(prev_high > 0)
        {
            levels_text += StringFormat("昨高: %s", FORMAT_PRICE(prev_high));
            if(current_price > 0)
            {
                int distance = (int)MathRound((current_price - prev_high) / _Point);
                levels_text += StringFormat(" (%+d)", distance);
            }
            levels_text += "\n";
        }
        
        if(prev_low > 0)
        {
            levels_text += StringFormat("昨低: %s", FORMAT_PRICE(prev_low));
            if(current_price > 0)
            {
                int distance = (int)MathRound((current_price - prev_low) / _Point);
                levels_text += StringFormat(" (%+d)", distance);
            }
        }
        
        UpdateTextLabel(OBJ_PREFIX + "KeyLevelsContent", levels_text, COLOR_INFO);
    }
    
    //+------------------------------------------------------------------+
    //| 渲染警报面板                                                       |
    //+------------------------------------------------------------------+
    void RenderAlertPanel(string alert_message, color alert_color = COLOR_INFO)
    {
        if(alert_message == "")
            alert_message = "无警报";
        
        UpdateTextLabel(OBJ_PREFIX + "AlertContent", alert_message, alert_color);
    }
    
    //+------------------------------------------------------------------+
    //| 主渲染函数                                                         |
    //+------------------------------------------------------------------+
    void Render(ENUM_TREND_STATE trend, string trend_info,
                ENUM_TRADING_SESSION session, int session_activity,
                double atr_value, string volatility_level, double atr_percentile,
                string pa_signal, string pa_counter,
                double daily_open, double prev_high, double prev_low,
                string alert_msg = "", color alert_clr = COLOR_INFO)
    {
        if(!is_initialized) return;
        
        // 渲染各个面板
        RenderTrendPanel(trend, trend_info);
        RenderSessionPanel(session, session_activity);
        RenderATRPanel(atr_value, volatility_level, atr_percentile);
        RenderSignalPanel(pa_signal, pa_counter);
        
        // 获取当前价格用于距离计算
        double current_price = SymbolInfoDouble(_Symbol, SYMBOL_BID);
        RenderKeyLevelsPanel(daily_open, prev_high, prev_low, current_price);
        
        RenderAlertPanel(alert_msg, alert_clr);
        
        // 强制重绘图表
        ChartRedraw();
    }
    
    //+------------------------------------------------------------------+
    //| 清理仪表盘                                                         |
    //+------------------------------------------------------------------+
    void Deinitialize()
    {
        if(!is_initialized) return;
        
        // 删除所有仪表盘对象
        ObjectDelete(0, OBJ_DASHBOARD_BACKGROUND);
        ObjectDelete(0, OBJ_PREFIX + "TrendBG");
        ObjectDelete(0, OBJ_PREFIX + "SignalBG");
        ObjectDelete(0, OBJ_PREFIX + "SessionBG");
        ObjectDelete(0, OBJ_PREFIX + "ATRBG");
        ObjectDelete(0, OBJ_PREFIX + "KeyLevelsBG");
        ObjectDelete(0, OBJ_PREFIX + "AlertBG");
        
        // 删除标题标签
        ObjectDelete(0, OBJ_PREFIX + "TrendTitle");
        ObjectDelete(0, OBJ_PREFIX + "SignalTitle");
        ObjectDelete(0, OBJ_PREFIX + "SessionTitle");
        ObjectDelete(0, OBJ_PREFIX + "ATRTitle");
        ObjectDelete(0, OBJ_PREFIX + "KeyLevelsTitle");
        ObjectDelete(0, OBJ_PREFIX + "AlertTitle");
        
        // 删除内容标签
        ObjectDelete(0, OBJ_PREFIX + "TrendContent");
        ObjectDelete(0, OBJ_PREFIX + "SignalContent");
        ObjectDelete(0, OBJ_PREFIX + "SessionContent");
        ObjectDelete(0, OBJ_PREFIX + "ATRContent");
        ObjectDelete(0, OBJ_PREFIX + "KeyLevelsContent");
        ObjectDelete(0, OBJ_PREFIX + "AlertContent");
        
        is_initialized = false;
        DEBUG_PRINT("仪表盘UI已清理");
    }
    
    //+------------------------------------------------------------------+
    //| 设置仪表盘位置                                                     |
    //+------------------------------------------------------------------+
    void SetPosition(int x, int y)
    {
        dashboard_x = x;
        dashboard_y = y;
        
        if(is_initialized)
        {
            // 重新计算位置
            CalculatePanelPositions();
            
            // 更新所有对象位置（这里需要重新创建对象）
            Deinitialize();
            Initialize(x, y, font_size);
        }
    }
    
    //+------------------------------------------------------------------+
    //| 切换仪表盘可见性                                                   |
    //+------------------------------------------------------------------+
    void SetVisible(bool visible)
    {
        // 遍历所有仪表盘对象，设置可见性
        string objects[] = {
            OBJ_DASHBOARD_BACKGROUND,
            OBJ_PREFIX + "TrendBG", OBJ_PREFIX + "SignalBG", OBJ_PREFIX + "SessionBG",
            OBJ_PREFIX + "ATRBG", OBJ_PREFIX + "KeyLevelsBG", OBJ_PREFIX + "AlertBG",
            OBJ_PREFIX + "TrendTitle", OBJ_PREFIX + "SignalTitle", OBJ_PREFIX + "SessionTitle",
            OBJ_PREFIX + "ATRTitle", OBJ_PREFIX + "KeyLevelsTitle", OBJ_PREFIX + "AlertTitle",
            OBJ_PREFIX + "TrendContent", OBJ_PREFIX + "SignalContent", OBJ_PREFIX + "SessionContent",
            OBJ_PREFIX + "ATRContent", OBJ_PREFIX + "KeyLevelsContent", OBJ_PREFIX + "AlertContent"
        };
        
        for(int i = 0; i < ArraySize(objects); i++)
        {
            if(ObjectFind(0, objects[i]) >= 0)
            {
                ObjectSetInteger(0, objects[i], OBJPROP_TIMEFRAMES, visible ? OBJ_ALL_PERIODS : OBJ_NO_PERIODS);
            }
        }
        
        ChartRedraw();
    }
}