//+------------------------------------------------------------------+
//|                                     交易时段实时仪表盘_终极版.mq5 |
//|                                  Copyright 2025, MetaQuotes Ltd. |
//|                                              https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, MetaQuotes Ltd."
#property link      "https://www.mql5.com"
#property version   "12.00"
#property description "纽约时段拆分版：重叠时段（多方角力）+ 纽约后半段（一方主导）"
#property description "v12特性：将纽约时段正确拆分为重叠时段和纽约后半段"
#property description "核心升级：重叠时段=纽约开盘到伦敦收盘，纽约时段=伦敦收盘到纽约收盘"
#property indicator_chart_window
#property indicator_plots 0

//+------------------------------------------------------------------+
//| 交易时段枚举定义                                                 |
//+------------------------------------------------------------------+
enum ENUM_TRADING_SESSION
{
    SESSION_ASIA,    // 亚洲时段
    SESSION_LONDON,  // 伦敦时段（伦敦开盘到纽约开盘）
    SESSION_OVERLAP, // 重叠时段（纽约开盘到伦敦收盘，多方角力）
    SESSION_NEWYORK, // 纽约后半段（伦敦收盘到纽约收盘，一方主导）
    SESSION_NONE     // 非主要时段
};

//+------------------------------------------------------------------+
//| 动态夏令时判断引擎 - 纽约夏令时判断                              |
//+------------------------------------------------------------------+
bool IsNewYorkInDST(datetime for_date)
{
    MqlDateTime dt;
    TimeToStruct(for_date, dt);
    int year = dt.year;
    
    // 计算三月第二个星期日
    datetime march_first = StringToTime(IntegerToString(year) + ".03.01 00:00:00");
    MqlDateTime march_dt;
    TimeToStruct(march_first, march_dt);
    
    int first_day_of_week = march_dt.day_of_week; // 0=Sunday, 1=Monday, ...
    int days_to_first_sunday = (first_day_of_week == 0) ? 0 : (7 - first_day_of_week);
    int second_sunday_day = 1 + days_to_first_sunday + 7; // 第二个星期日
    
    datetime dst_start_date = StringToTime(IntegerToString(year) + ".03." + IntegerToString(second_sunday_day) + " 07:00:00"); // 2AM EST = 7AM GMT
    
    // 计算十一月第一个星期日
    datetime nov_first = StringToTime(IntegerToString(year) + ".11.01 00:00:00");
    MqlDateTime nov_dt;
    TimeToStruct(nov_first, nov_dt);
    
    int nov_first_day_of_week = nov_dt.day_of_week;
    int days_to_nov_first_sunday = (nov_first_day_of_week == 0) ? 0 : (7 - nov_first_day_of_week);
    int first_sunday_day = 1 + days_to_nov_first_sunday;
    
    datetime dst_end_date = StringToTime(IntegerToString(year) + ".11." + IntegerToString(first_sunday_day) + " 06:00:00"); // 2AM EST = 6AM GMT
    
    return (for_date >= dst_start_date && for_date < dst_end_date);
}

//+------------------------------------------------------------------+
//| 动态夏令时判断引擎 - 伦敦夏令时判断                              |
//+------------------------------------------------------------------+
bool IsLondonInDST(datetime for_date)
{
    MqlDateTime dt;
    TimeToStruct(for_date, dt);
    int year = dt.year;
    
    // 计算三月最后一个星期日
    datetime march_31 = StringToTime(IntegerToString(year) + ".03.31 00:00:00");
    MqlDateTime march_dt;
    TimeToStruct(march_31, march_dt);
    
    int march_31_day_of_week = march_dt.day_of_week;
    int days_back_to_sunday = (march_31_day_of_week == 0) ? 0 : march_31_day_of_week;
    int last_sunday_day = 31 - days_back_to_sunday;
    
    datetime dst_start_date = StringToTime(IntegerToString(year) + ".03." + IntegerToString(last_sunday_day) + " 01:00:00"); // 1AM GMT
    
    // 计算十月最后一个星期日
    datetime oct_31 = StringToTime(IntegerToString(year) + ".10.31 00:00:00");
    MqlDateTime oct_dt;
    TimeToStruct(oct_31, oct_dt);
    
    int oct_31_day_of_week = oct_dt.day_of_week;
    int oct_days_back_to_sunday = (oct_31_day_of_week == 0) ? 0 : oct_31_day_of_week;
    int oct_last_sunday_day = 31 - oct_days_back_to_sunday;
    
    datetime dst_end_date = StringToTime(IntegerToString(year) + ".10." + IntegerToString(oct_last_sunday_day) + " 01:00:00"); // 1AM GMT
    
    return (for_date >= dst_start_date && for_date < dst_end_date);
}

//+------------------------------------------------------------------+
//| 交易时段数据结构 - 每日蓝图的基本单位                            |
//+------------------------------------------------------------------+
struct TradingSession
{
    string   name;         // 时段名称 (e.g., "亚洲时段")
    datetime startTimeGMT; // 当天的GMT开始时间
    datetime endTimeGMT;   // 用于无缝衔接的逻辑结束时间
    datetime rawEndTimeGMT;// 用于重叠判断的自然结束时间 (e.g., start + 9 hours)
    color    sessionColor; // 时段颜色
    ENUM_TRADING_SESSION sessionEnum; // 对应的枚举值
};

// 全局蓝图数组 - 存储亚洲、伦敦、纽约三个时段
TradingSession g_session_blueprint[3];

//+------------------------------------------------------------------+
//| 用户输入参数 - 现代化UI设计                                      |
//+------------------------------------------------------------------+
input group "========== 仪表盘美观设置 =========="
input bool  InpShowDashboard = true;                    // 总开关：显示/隐藏仪表盘
input bool  InpShowToggleButton = true;                 // 显示展开/收缩按钮
input ENUM_BASE_CORNER InpCorner = CORNER_LEFT_UPPER;   // 位置：左上角（避免遮挡）
input uint  InpXOffset = 15;                           // X轴偏移量
input uint  InpYOffset = 35;                           // Y轴偏移量
input int   InpFontSize = 10;                          // 字体大小（清晰可读）
input string InpFontName = "Consolas";                 // 字体名称（等宽字体）
input color InpBackgroundColor = C'40,45,55';          // 背景颜色（深蓝灰）
input int   InpBackgroundAlpha = 180;                  // 背景透明度（半透明）
input color InpBorderColor = C'85,170,255';            // 边框颜色（亮蓝色）
input int   InpPanelWidth = 280;                       // 面板宽度（适中设计）
input int   InpLineHeight = 18;                        // 行高（舒适间距）
input color InpButtonColor = C'70,130,180';            // 按钮颜色（钢蓝色）
input color InpButtonTextColor = clrWhite;             // 按钮文字颜色

input group "========== 时段颜色主题 =========="
input color InpColorAsia    = C'160,160,160';          // 亚洲时段（银灰色）
input color InpColorLondon  = C'50,205,50';           // 伦敦时段（亮绿色）
input color InpColorNY      = C'30,144,255';          // 纽约时段（道奇蓝）
input color InpColorOverlap = C'255,165,0';           // 重叠时段（橙色）
input color InpColorNone    = C'128,128,128';         // 非主要时段（灰色）

input group "========== 波动率分析设置 =========="
input bool InpShowVolatility = true;                   // 显示波动率分析模块
input int  InpAvgDays = 20;                           // 计算日均波幅的天数
input int  InpAtrPeriod = 14;                         // ATR指标的周期

input group "========== 时段时间设置 =========="
input int InpAsiaStart = 0;                           // 亚洲时段开始时间 (GMT)
input int InpAsiaEnd = 9;                             // 亚洲时段结束时间 (GMT)
input int InpLondonStart = 8;                         // 伦敦时段开始时间 (GMT)
input int InpLondonEnd = 17;                          // 伦敦时段结束时间 (GMT)
input int InpNYStart = 13;                            // 纽约时段开始时间 (GMT)
input int InpNYEnd = 22;                              // 纽约时段结束时间 (GMT)

//+------------------------------------------------------------------+
//| 时段统计结构体                                                   |
//+------------------------------------------------------------------+
struct SessionStats
{
    double asia_avg_range;      // 亚洲时段平均真实波幅
    double london_avg_range;    // 伦敦时段平均真实波幅
    double overlap_avg_range;   // 重叠时段平均真实波幅
    double newyork_avg_range;   // 纽约时段平均真实波幅
};

//+------------------------------------------------------------------+
//| 专业仪表盘面板类 - 多行显示的终极解决方案                        |
//+------------------------------------------------------------------+
class CDashboardPanel
{
private:
    // 面板属性
    long              m_chart_id;
    string            m_name_prefix;
    int               m_x_pos;
    int               m_y_pos;
    int               m_panel_width;
    int               m_panel_height;
    
    // 文本行定义（每行独立的OBJ_LABEL）
    string            m_text_lines[10];
    color             m_line_colors[10];
    string            m_last_content[10];  // 缓存上次内容，优化性能
    
    // 不创建任何背景 - 纯文字显示，完全不遮挡K线
    void CreateBackground()
    {
        // 什么都不做 - 彻底移除背景和阴影
        // 只显示文字，完全透明
    }
    
    // 创建展开/收缩按钮
    void CreateToggleButton()
    {
        if(!InpShowToggleButton) return;
        
        ObjectCreate(m_chart_id, TOGGLE_BUTTON_NAME, OBJ_BUTTON, 0, 0, 0);
        ObjectSetInteger(m_chart_id, TOGGLE_BUTTON_NAME, OBJPROP_CORNER, InpCorner);
        ObjectSetInteger(m_chart_id, TOGGLE_BUTTON_NAME, OBJPROP_XDISTANCE, m_x_pos);
        ObjectSetInteger(m_chart_id, TOGGLE_BUTTON_NAME, OBJPROP_YDISTANCE, m_y_pos);
        ObjectSetInteger(m_chart_id, TOGGLE_BUTTON_NAME, OBJPROP_XSIZE, 25);
        ObjectSetInteger(m_chart_id, TOGGLE_BUTTON_NAME, OBJPROP_YSIZE, 20);
        ObjectSetInteger(m_chart_id, TOGGLE_BUTTON_NAME, OBJPROP_BGCOLOR, InpButtonColor);
        ObjectSetInteger(m_chart_id, TOGGLE_BUTTON_NAME, OBJPROP_COLOR, InpButtonTextColor);
        ObjectSetInteger(m_chart_id, TOGGLE_BUTTON_NAME, OBJPROP_BORDER_COLOR, InpBorderColor);
        ObjectSetString(m_chart_id, TOGGLE_BUTTON_NAME, OBJPROP_FONT, "Arial");
        ObjectSetInteger(m_chart_id, TOGGLE_BUTTON_NAME, OBJPROP_FONTSIZE, 8);
        ObjectSetString(m_chart_id, TOGGLE_BUTTON_NAME, OBJPROP_TEXT, g_isExpanded ? "▼" : "?");
        ObjectSetInteger(m_chart_id, TOGGLE_BUTTON_NAME, OBJPROP_SELECTABLE, false);
        ObjectSetInteger(m_chart_id, TOGGLE_BUTTON_NAME, OBJPROP_HIDDEN, true);
        ObjectSetInteger(m_chart_id, TOGGLE_BUTTON_NAME, OBJPROP_ZORDER, 2);
    }
    
    // 更新按钮状态
    void UpdateToggleButton()
    {
        if(!InpShowToggleButton) return;
        
        ObjectSetString(m_chart_id, TOGGLE_BUTTON_NAME, OBJPROP_TEXT, g_isExpanded ? "▼" : "?");
    }
    
    // 创建单行文本标签
    void CreateTextLine(int line_index, string initial_text, color text_color)
    {
        string line_name = m_name_prefix + "line_" + IntegerToString(line_index);
        
        ObjectCreate(m_chart_id, line_name, OBJ_LABEL, 0, 0, 0);
        ObjectSetInteger(m_chart_id, line_name, OBJPROP_CORNER, InpCorner);
        ObjectSetInteger(m_chart_id, line_name, OBJPROP_XDISTANCE, m_x_pos + 12);
        ObjectSetInteger(m_chart_id, line_name, OBJPROP_YDISTANCE, m_y_pos + 12 + (line_index * InpLineHeight));
        ObjectSetInteger(m_chart_id, line_name, OBJPROP_COLOR, text_color);
        ObjectSetString(m_chart_id, line_name, OBJPROP_FONT, InpFontName);
        ObjectSetInteger(m_chart_id, line_name, OBJPROP_FONTSIZE, InpFontSize);
        ObjectSetString(m_chart_id, line_name, OBJPROP_TEXT, initial_text);
        ObjectSetInteger(m_chart_id, line_name, OBJPROP_BACK, false);
        ObjectSetInteger(m_chart_id, line_name, OBJPROP_SELECTABLE, false);
        ObjectSetInteger(m_chart_id, line_name, OBJPROP_SELECTED, false);
        ObjectSetInteger(m_chart_id, line_name, OBJPROP_HIDDEN, true);
        ObjectSetInteger(m_chart_id, line_name, OBJPROP_ZORDER, 1);
        
        // 存储行名称
        m_text_lines[line_index] = line_name;
        m_line_colors[line_index] = text_color;
        m_last_content[line_index] = initial_text;
    }
    
    // ColorToARGB函数实现
    color ColorToARGB(color rgb_color, int alpha)
    {
        int r = (rgb_color & 0xFF);
        int g = (rgb_color >> 8) & 0xFF;
        int b = (rgb_color >> 16) & 0xFF;
        
        return (color)((alpha << 24) | (b << 16) | (g << 8) | r);
    }

public:
    // 构造函数
    CDashboardPanel(void) : m_chart_id(0), m_name_prefix("Dashboard_"), 
                           m_x_pos(0), m_y_pos(0), m_panel_width(280), m_panel_height(200)
    {
    }
    
    // 析构函数
    ~CDashboardPanel(void)
    {
        Destroy();
    }
    
    // 创建完整面板
    void Create(long chart_id, int x, int y)
    {
        m_chart_id = chart_id;
        m_x_pos = x;
        m_y_pos = y;
        m_panel_width = InpPanelWidth;
        m_panel_height = (InpLineHeight * 10) + 24; // 10行 + 内边距
        
        // 创建展开/收缩按钮
        CreateToggleButton();
        
        // 不创建背景 - 纯文字显示
        // CreateBackground(); // 完全注释掉
        
        // 创建所有文本行（初始化为空）
        CreateTextLine(0, "[ 交易时段与波动率监控 ]", clrWhite);
        CreateTextLine(1, "当前时段:     初始化中...", clrWhite);
        CreateTextLine(2, "时段均幅:     计算中...", clrWhite);
        CreateTextLine(3, "今日已走/剩余: 计算中...", clrWhite);
        CreateTextLine(4, "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━", clrDarkGray);
        CreateTextLine(5, "日均波幅(20D): 计算中...", clrWhite);
        CreateTextLine(6, "今日已走:     计算中...", clrWhite);
        CreateTextLine(7, "预估剩余:     计算中...", clrWhite);
        CreateTextLine(8, "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━", clrDarkGray);
        CreateTextLine(9, "时段均波(总): 亚洲:--- 伦敦:--- 重叠:--- 纽约:---", clrWhite);
        
        // 根据初始状态设置显示
        SetPanelVisibility(g_isExpanded);
        
        ChartRedraw(m_chart_id);
    }
    
    // 设置面板显示/隐藏
    void SetPanelVisibility(bool visible)
    {
        for(int i = 0; i < 10; i++)
        {
            ObjectSetInteger(m_chart_id, m_text_lines[i], OBJPROP_TIMEFRAMES, visible ? OBJ_ALL_PERIODS : OBJ_NO_PERIODS);
        }
        UpdateToggleButton();
    }
    
    // 切换面板状态
    void TogglePanel()
    {
        g_isExpanded = !g_isExpanded;
        SetPanelVisibility(g_isExpanded);
        ChartRedraw(m_chart_id);
    }
    
    // 更新显示内容
    void UpdateDisplay(ENUM_TRADING_SESSION current_session, string session_name, 
                      double avg_range, double today_range, double remaining_range, 
                      double percentage, SessionStats &stats,
                      double session_avg_range, double session_today_range, 
                      double session_remaining_range, double session_percentage)
    {
        // 始终更新按钮状态，无论面板是否展开
        UpdateToggleButton();
        
        // 如果面板收缩状态，只更新按钮，不更新内容
        if(!g_isExpanded) return;
        
        // 根据当前时段设置主题颜色
        color session_color = GetSessionColor(current_session);
        
        // 准备所有行的内容
        string line_contents[10];
        color line_colors[10];
        
        line_contents[0] = "[ 交易时段与波动率监控 ]";
        line_colors[0] = clrWhite;
        
        line_contents[1] = "当前时段:     " + session_name;
        line_colors[1] = session_color;
        
        line_contents[2] = "时段均幅:     " + DoubleToString(session_avg_range, 5);
        line_colors[2] = clrWhite;
        
        line_contents[3] = "今日已走/剩余: " + DoubleToString(session_today_range, 5) + " / " + 
                          DoubleToString(session_remaining_range, 5) + " (" + IntegerToString((int)session_percentage) + "%)";
        line_colors[3] = (session_percentage > 80) ? clrOrange : clrWhite;
        
        line_contents[4] = "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━";
        line_colors[4] = clrDarkGray;
        
        line_contents[5] = "日均波幅(20D): " + DoubleToString(avg_range, 5);
        line_colors[5] = clrWhite;
        
        line_contents[6] = "今日已走:     " + DoubleToString(today_range, 5) + " (" + IntegerToString((int)percentage) + "%)";
        line_colors[6] = (percentage > 80) ? clrOrange : clrWhite;
        
        // 改进预估剩余显示逻辑
        string remaining_text;
        color remaining_color;
        if(remaining_range < 0)
        {
            remaining_text = "预估剩余:     已超出 " + DoubleToString(MathAbs(remaining_range), 5);
            remaining_color = clrRed;
        }
        else if(remaining_range < avg_range * 0.1) // 剩余不足10%
        {
            remaining_text = "预估剩余:     " + DoubleToString(remaining_range, 5) + " (即将完成)";
            remaining_color = clrYellow;
        }
        else
        {
            remaining_text = "预估剩余:     " + DoubleToString(remaining_range, 5);
            remaining_color = clrLightGreen;
        }
        
        line_contents[7] = remaining_text;
        line_colors[7] = remaining_color;
        
        line_contents[8] = "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━";
        line_colors[8] = clrDarkGray;
        
        line_contents[9] = "时段均波(总): 亚洲:" + DoubleToString(stats.asia_avg_range, 3) + 
                          " 伦敦:" + DoubleToString(stats.london_avg_range, 3) + 
                          " 重叠:" + DoubleToString(stats.overlap_avg_range, 3) + 
                          " 纽约:" + DoubleToString(stats.newyork_avg_range, 3);
        line_colors[9] = clrLightBlue;
        
        // 强制更新所有行（确保刷新）- 增强版
        bool need_redraw = false;
        static int display_update_count = 0;
        display_update_count++;
        
        for(int i = 0; i < 10; i++)
        {
            // 检查内容或颜色是否变化，或者强制更新标志
            bool content_changed = (line_contents[i] != m_last_content[i]);
            bool color_changed = (line_colors[i] != m_line_colors[i]);
            
            if(content_changed || color_changed || g_force_update)
            {
                if(display_update_count % 20 == 1) // 每20次更新打印一次日志
                {
                    Print("【显示更新】行", i, " 内容变化:", content_changed, 
                          " 颜色变化:", color_changed, " 强制更新:", g_force_update);
                    if(content_changed)
                    {
                        Print("【显示更新】行", i, " 内容: '", m_last_content[i], "' -> '", line_contents[i], "'");
                    }
                }
                
                ObjectSetString(m_chart_id, m_text_lines[i], OBJPROP_TEXT, line_contents[i]);
                ObjectSetInteger(m_chart_id, m_text_lines[i], OBJPROP_COLOR, line_colors[i]);
                
                m_last_content[i] = line_contents[i];
                m_line_colors[i] = line_colors[i];
                need_redraw = true;
            }
        }
        
        // 增强重绘逻辑：更频繁的强制重绘
        static datetime last_force_redraw = 0;
        datetime current_time = TimeCurrent();
        
        // 在强制更新模式下，立即重绘
        if(g_force_update || need_redraw || current_time - last_force_redraw >= 5)
        {
            if(display_update_count % 20 == 1)
            {
                Print("【显示更新】触发重绘 - 强制更新:", g_force_update, 
                      " 需要重绘:", need_redraw, " 时间间隔:", (current_time - last_force_redraw));
            }
            
            ChartRedraw(m_chart_id);
            last_force_redraw = current_time;
        }
    }
    
    // 销毁面板
    void Destroy()
    {
        // 删除所有文本行
        for(int i = 0; i < 10; i++)
        {
            ObjectDelete(m_chart_id, m_text_lines[i]);
        }
        
        // 删除按钮
        ObjectDelete(m_chart_id, TOGGLE_BUTTON_NAME);
        
        // 不需要删除背景对象，因为没有创建
        // ObjectDelete(m_chart_id, m_name_prefix + "background");
        // ObjectDelete(m_chart_id, m_name_prefix + "shadow");
    }
    
    // 获取时段颜色
    color GetSessionColor(ENUM_TRADING_SESSION session)
    {
        switch(session)
        {
            case SESSION_ASIA:    return InpColorAsia;
            case SESSION_LONDON:  return InpColorLondon;
            case SESSION_OVERLAP: return InpColorOverlap;
            case SESSION_NEWYORK: return InpColorNY;
            default:              return InpColorNone;
        }
    }
};

//+------------------------------------------------------------------+
//| 极简变化驱动缓存结构体（v11升级版）                              |
//+------------------------------------------------------------------+
struct SimpleChangeCache
{
    // 价格变化检测缓存
    double cached_high;                                 // 缓存的日内最高价
    double cached_low;                                  // 缓存的日内最低价
    
    // 计算结果缓存
    double daily_avg_range;                             // 日均波幅
    double today_range;                                 // 今日已走波幅
    double remaining_range;                             // 预估剩余
    double percentage;                                  // 完成百分比
    
    // 时段信息
    ENUM_TRADING_SESSION current_session;               // 当前时段
    string session_name;                                // 时段名称
    
    // 时段统计数据
    SessionStats session_stats;                         // 时段统计
    
    // v11新增：时段内波动率分析缓存
    double current_session_avg_range;                   // 当前时段历史均幅
    double current_session_today_range;                 // 当前时段今日现幅
    double current_session_remaining_range;             // 当前时段剩余空间
    double current_session_percentage;                  // 当前时段完成进度
    
    // 时间戳
    datetime last_data_update;                          // 上次数据更新时间
    datetime last_backup_update;                        // 上次兜底更新时间
    datetime last_daily_calc;                           // 上次日均计算时间
};

//+------------------------------------------------------------------+
//| 全局变量                                                         |
//+------------------------------------------------------------------+
CDashboardPanel g_dashboard;                            // 仪表盘面板实例
SimpleChangeCache g_cache;                              // 极简变化驱动缓存实例
SessionStats g_sessionStats;                            // 时段统计数据全局变量

// 展开/收缩按钮相关变量
bool g_isExpanded = true;                               // 面板展开状态
const string TOGGLE_BUTTON_NAME = "Dashboard_ToggleBtn"; // 按钮对象名称

// 性能优化变量
static int g_timer_count = 0;                           // 定时器计数器
static bool g_force_update = false;                     // 强制更新标志

//+------------------------------------------------------------------+
//| 打印时段详细信息（独立函数）                                     |
//+------------------------------------------------------------------+
void PrintSessionDetailInfo(string context = "")
{
    datetime current_time = TimeCurrent();
    
    // 获取当天的夏令时状态
    bool london_dst = IsLondonInDST(current_time);
    bool newyork_dst = IsNewYorkInDST(current_time);
    
    // 获取当天0点GMT时间作为基准
    datetime today_start = (datetime)(current_time / 86400) * 86400;
    
    // v12关键时间点计算
    datetime london_open = today_start + (london_dst ? 7*3600 : 8*3600);      // 伦敦开盘
    datetime london_close = today_start + (london_dst ? 16*3600 : 17*3600);   // 伦敦收盘
    datetime newyork_open = today_start + (newyork_dst ? 13*3600 + 30*60 : 14*3600 + 30*60); // 纽约开盘
    datetime newyork_close = today_start + 22*3600;                           // 纽约收盘（固定22:00 GMT）
    datetime asia_start = today_start - 2*3600;                               // 亚洲开始（前一天22:00）
    
    // 打印各个时段的GMT时间和对应的北京时间
    Print("========== 交易时段详细信息", (context != "" ? " (" + context + ")" : ""), " ==========");
    Print("【亚洲时段】");
    Print("  GMT时间: ", TimeToString(asia_start, TIME_MINUTES), " - ", TimeToString(london_open, TIME_MINUTES));
    Print("  北京时间: ", TimeToString(asia_start + 8*3600, TIME_MINUTES), " - ", TimeToString(london_open + 8*3600, TIME_MINUTES));
    
    Print("【伦敦时段】");
    Print("  GMT时间: ", TimeToString(london_open, TIME_MINUTES), " - ", TimeToString(newyork_open, TIME_MINUTES));
    Print("  北京时间: ", TimeToString(london_open + 8*3600, TIME_MINUTES), " - ", TimeToString(newyork_open + 8*3600, TIME_MINUTES));
    
    Print("【重叠时段(多方角力)】");
    Print("  GMT时间: ", TimeToString(newyork_open, TIME_MINUTES), " - ", TimeToString(london_close, TIME_MINUTES));
    Print("  北京时间: ", TimeToString(newyork_open + 8*3600, TIME_MINUTES), " - ", TimeToString(london_close + 8*3600, TIME_MINUTES));
    
    Print("【纽约后半段(一方主导)】");
    Print("  GMT时间: ", TimeToString(london_close, TIME_MINUTES), " - ", TimeToString(newyork_close, TIME_MINUTES));
    Print("  北京时间: ", TimeToString(london_close + 8*3600, TIME_MINUTES), " - ", TimeToString(newyork_close + 8*3600, TIME_MINUTES));
    
    Print("【夏令时状态】");
    Print("  伦敦夏令时: ", london_dst ? "是 (GMT+1)" : "否 (GMT+0)");
    Print("  纽约夏令时: ", newyork_dst ? "是 (GMT-4)" : "否 (GMT-5)");
    Print("=====================================");
}

//+------------------------------------------------------------------+
//| 每日时段蓝图计算器 - v12重构版（纽约时段拆分）                   |
//+------------------------------------------------------------------+
void CalculateDailySessionBlueprint()
{
    datetime current_time = TimeCurrent();
    
    // 获取当天的夏令时状态
    bool london_dst = IsLondonInDST(current_time);
    bool newyork_dst = IsNewYorkInDST(current_time);
    
    Print("【v12蓝图计算】伦敦夏令时:", london_dst ? "是" : "否", " 纽约夏令时:", newyork_dst ? "是" : "否");
    
    // 获取当天0点GMT时间作为基准
    datetime today_start = (datetime)(current_time / 86400) * 86400;
    
    // v12关键时间点计算
    datetime london_open = today_start + (london_dst ? 7*3600 : 8*3600);      // 伦敦开盘
    datetime london_close = today_start + (london_dst ? 16*3600 : 17*3600);   // 伦敦收盘
    datetime newyork_open = today_start + (newyork_dst ? 13*3600 + 30*60 : 14*3600 + 30*60); // 纽约开盘
    datetime newyork_close = today_start + 22*3600;                           // 纽约收盘（固定22:00 GMT）
    datetime asia_start = today_start - 2*3600;                               // 亚洲开始（前一天22:00）
    
    Print("【v12关键时间】伦敦: ", TimeToString(london_open, TIME_MINUTES), "-", TimeToString(london_close, TIME_MINUTES));
    Print("【v12关键时间】纽约: ", TimeToString(newyork_open, TIME_MINUTES), "-", TimeToString(newyork_close, TIME_MINUTES));
    Print("【v12重叠时段】", TimeToString(newyork_open, TIME_MINUTES), "-", TimeToString(london_close, TIME_MINUTES));
    
    // 调用独立的打印函数
    PrintSessionDetailInfo("蓝图计算");
    
    // v12新的时段定义：
    // 1. 亚洲时段：前一天22:00 GMT 到伦敦开盘
    g_session_blueprint[0].name = "亚洲";
    g_session_blueprint[0].startTimeGMT = today_start - 2*3600; // 前一天22:00 GMT
    g_session_blueprint[0].endTimeGMT = london_open;            // 伦敦开盘
    g_session_blueprint[0].rawEndTimeGMT = london_open;         // 自然结束时间
    g_session_blueprint[0].sessionColor = InpColorAsia;
    g_session_blueprint[0].sessionEnum = SESSION_ASIA;
    
    // 2. 伦敦时段：伦敦开盘到纽约开盘
    g_session_blueprint[1].name = "伦敦";
    g_session_blueprint[1].startTimeGMT = london_open;          // 伦敦开盘
    g_session_blueprint[1].endTimeGMT = newyork_open;           // 纽约开盘
    g_session_blueprint[1].rawEndTimeGMT = london_close;        // 伦敦自然收盘时间
    g_session_blueprint[1].sessionColor = InpColorLondon;
    g_session_blueprint[1].sessionEnum = SESSION_LONDON;
    
    // 3. 纽约后半段：伦敦收盘到纽约收盘（一方主导）
    g_session_blueprint[2].name = "纽约后半段";
    g_session_blueprint[2].startTimeGMT = london_close;         // 伦敦收盘
    g_session_blueprint[2].endTimeGMT = newyork_close;          // 纽约收盘
    g_session_blueprint[2].rawEndTimeGMT = newyork_close;       // 纽约自然收盘时间
    g_session_blueprint[2].sessionColor = InpColorNY;
    g_session_blueprint[2].sessionEnum = SESSION_NEWYORK;
    
    // 排序蓝图数组（按开始时间升序）
    for(int i = 0; i < 2; i++)
    {
        for(int j = i + 1; j < 3; j++)
        {
            if(g_session_blueprint[i].startTimeGMT > g_session_blueprint[j].startTimeGMT)
            {
                TradingSession temp = g_session_blueprint[i];
                g_session_blueprint[i] = g_session_blueprint[j];
                g_session_blueprint[j] = temp;
            }
        }
    }
    
    // 打印v12新蓝图信息
    Print("【v12每日蓝图】纽约时段拆分版蓝图:");
    for(int i = 0; i < 3; i++)
    {
        Print("  ", g_session_blueprint[i].name, ": ", 
              TimeToString(g_session_blueprint[i].startTimeGMT, TIME_MINUTES), 
              " - ", TimeToString(g_session_blueprint[i].endTimeGMT, TIME_MINUTES));
    }
    
    // 特别说明重叠时段的计算逻辑
    Print("【v12重叠逻辑】重叠时段将通过GetCurrentTradingSession()函数动态识别");
    Print("【v12重叠逻辑】重叠条件：纽约开盘(", TimeToString(newyork_open, TIME_MINUTES), 
          ") 到 伦敦收盘(", TimeToString(london_close, TIME_MINUTES), ")");
}

//+------------------------------------------------------------------+
//| 检查是否为新的一天（用于蓝图更新判断）                           |
//+------------------------------------------------------------------+
bool IsNewDayForBlueprint()
{
    static datetime last_blueprint_day = 0;
    datetime current_day = (datetime)(TimeCurrent() / 86400) * 86400;
    
    if(current_day != last_blueprint_day)
    {
        last_blueprint_day = current_day;
        return true;
    }
    return false;
}

//+------------------------------------------------------------------+
//| 自定义指标初始化函数                                             |
//+------------------------------------------------------------------+
int OnInit()
{
    // 初始化智能缓存结构
    InitializeSmartCache();
    
    // 首次计算每日蓝图
    CalculateDailySessionBlueprint();
    
    // 创建专业仪表盘面板
    if(InpShowDashboard)
    {
        g_dashboard.Create(ChartID(), InpXOffset, InpYOffset);
        Print("专业多行仪表盘创建完成");
    }
    
    // 一次性计算时段统计数据（高成本计算）
    if(InpShowVolatility)
    {
        Print("正在计算时段统计数据，请稍候...");
        CalculateSessionStatistics();
        // 将结果存储到缓存中
        g_cache.session_stats = g_sessionStats;
        Print("时段统计数据计算完成并缓存");
    }
    
    // 启动定时器（每秒更新）
    EventSetTimer(1);
    
    Print("========================================");
    Print("交易时段实时仪表盘_终极版 v12.0 已启动");
    Print("【v12核心升级】纽约时段拆分：重叠时段 + 纽约后半段");
    Print("【v12重叠时段】纽约开盘到伦敦收盘（多方角力）");
    Print("【v12纽约时段】伦敦收盘到纽约收盘（一方主导）");
    Print("【v12架构】动态夏令时 + 精确时段划分逻辑");
    Print("========================================");
    return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| 自定义指标反初始化函数                                           |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    // 停止定时器
    EventKillTimer();
    
    // 销毁仪表盘面板
    g_dashboard.Destroy();
    
    Print("交易时段实时仪表盘_终极版 已停止");
}

//+------------------------------------------------------------------+
//| 自定义指标计算函数（必需）                                       |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[])
{
    // 指标计算函数（空实现，因为我们只需要显示仪表盘）
    return rates_total;
}

//+------------------------------------------------------------------+
//| 极简变化驱动定时器 - 核心实现（升级版）                          |
//+------------------------------------------------------------------+
void OnTimer()
{
    if(!InpShowDashboard) return;
    
    g_timer_count++;
    datetime current_time = TimeCurrent();
    
    // 【蓝图更新】检查是否需要更新每日蓝图
    if(IsNewDayForBlueprint())
    {
        Print("【蓝图更新】检测到新的一天，重新计算每日蓝图...");
        CalculateDailySessionBlueprint();
    }
    
    // 【核心逻辑】极简变化检测 - 每秒执行
    bool has_change = DetectPriceChange();
    
    // 【兜底机制】每5分钟强制更新一次（防止系统卡死）
    bool force_backup = (current_time - g_cache.last_backup_update >= 300); // 5分钟
    
    // 【日均计算】每日更新一次（低频高成本计算）
    bool need_daily_calc = IsNewDay() || g_cache.last_daily_calc == 0;
    
    // 只在有变化或兜底时才执行计算和更新
    if(has_change || force_backup || need_daily_calc || g_force_update)
    {
        // 日均波幅计算（每日一次）
        if(need_daily_calc)
        {
            g_cache.daily_avg_range = GetAverageDailyRange();
            g_cache.last_daily_calc = current_time;
            Print("【日均更新】日均波幅已更新: ", DoubleToString(g_cache.daily_avg_range, 5));
        }
        
        // 时段统计计算（首次或兜底时）
        if(g_cache.session_stats.asia_avg_range == 0 || force_backup)
        {
            CalculateSessionStatistics();
            g_cache.session_stats = g_sessionStats;
            Print("【时段更新】时段统计已更新");
        }
        
        // 实时数据计算（有变化时）
        UpdateRealTimeCalculations();
        
        // v11核心升级：更新时段波动率计算（集成高级分析）
        UpdateSessionVolatilityCalculations();
        
        // 更新显示
        UpdateDashboard();
        
        // 【新增】每次刷新时打印时段详细信息
        if(has_change || force_backup || need_daily_calc)
        {
            string refresh_context = "";
            if(has_change) refresh_context = "价格变化刷新";
            else if(force_backup) refresh_context = "兜底刷新";
            else if(need_daily_calc) refresh_context = "日均计算刷新";
            
            PrintSessionDetailInfo(refresh_context);
        }
        
        // 更新时间戳
        g_cache.last_data_update = current_time;
        if(force_backup) g_cache.last_backup_update = current_time;
        
        // 打印变化日志
        if(has_change)
        {
            Print("【变化检测】价格变化触发更新 - 今日波幅: ", DoubleToString(g_cache.today_range, 5),
                  " 完成百分比: ", DoubleToString(g_cache.percentage, 1), "%");
        }
        else if(force_backup)
        {
            Print("【兜底更新】5分钟兜底更新执行");
        }
    }
    
    // 每60秒更新心跳显示（让用户知道系统在运行）
    if(g_timer_count % 60 == 0)
    {
        UpdateHeartbeat();
    }
    
    // 重置强制更新标志
    g_force_update = false;
}

//+------------------------------------------------------------------+
//| 图表事件处理函数 - 处理按钮点击和手动刷新                        |
//+------------------------------------------------------------------+
void OnChartEvent(const int id,
                  const long &lparam,
                  const double &dparam,
                  const string &sparam)
{
    // 处理按钮点击事件
    if(id == CHARTEVENT_OBJECT_CLICK)
    {
        if(sparam == TOGGLE_BUTTON_NAME)
        {
            // 切换面板展开/收缩状态
            g_dashboard.TogglePanel();
            
            // 重置按钮状态（避免按钮保持按下状态）
            ObjectSetInteger(ChartID(), TOGGLE_BUTTON_NAME, OBJPROP_STATE, false);
            ChartRedraw();
        }
    }
    
    // 处理图表周期变化事件（强制刷新数据）
    if(id == CHARTEVENT_CHART_CHANGE)
    {
        Print("检测到图表周期变化，强制刷新所有数据...");
        ForceRefreshAllData();
    }
    
    // 处理键盘事件（F5键手动刷新）
    if(id == CHARTEVENT_KEYDOWN)
    {
        if(lparam == 116) // F5键
        {
            Print("用户按下F5键，手动刷新所有数据...");
            ForceRefreshAllData();
        }
    }
    
    // 处理鼠标双击事件（手动刷新）
    if(id == CHARTEVENT_CLICK)
    {
        static datetime last_click_time = 0;
        datetime current_time = TimeCurrent();
        
        // 检测双击（两次点击间隔小于1秒）
        if(current_time - last_click_time < 1) // 1秒内的双击
        {
            Print("检测到双击，手动刷新所有数据...");
            ForceRefreshAllData();
        }
        last_click_time = current_time;
    }
}

//+------------------------------------------------------------------+
//| 初始化极简变化驱动缓存系统                                       |
//+------------------------------------------------------------------+
void InitializeSmartCache()
{
    // 初始化价格变化检测缓存
    g_cache.cached_high = 0;
    g_cache.cached_low = 0;
    
    // 初始化计算结果缓存
    g_cache.daily_avg_range = 0;
    g_cache.today_range = 0;
    g_cache.remaining_range = 0;
    g_cache.percentage = 0;
    
    // 初始化时段信息
    g_cache.current_session = SESSION_NONE;
    g_cache.session_name = "初始化中...";
    
    // 初始化时段统计数据
    g_cache.session_stats.asia_avg_range = 0;
    g_cache.session_stats.london_avg_range = 0;
    g_cache.session_stats.overlap_avg_range = 0;
    g_cache.session_stats.newyork_avg_range = 0;
    
    // v10新增：初始化时段内波动率分析缓存
    g_cache.current_session_avg_range = 0;
    g_cache.current_session_today_range = 0;
    g_cache.current_session_remaining_range = 0;
    g_cache.current_session_percentage = 0;
    
    // 初始化时间戳
    g_cache.last_data_update = 0;
    g_cache.last_backup_update = 0;
    g_cache.last_daily_calc = 0;
    
    Print("极简变化驱动缓存系统初始化完成 - v10升级版");
}

//+------------------------------------------------------------------+
//| 强制刷新所有数据 - 极简版本                                      |
//+------------------------------------------------------------------+
void ForceRefreshAllData()
{
    Print("========================================");
    Print("【强制刷新】开始极简强制刷新...");
    Print("【强制刷新】当前图表周期: ", EnumToString(Period()));
    
    // 清空价格变化检测缓存，强制触发变化检测
    g_cache.cached_high = 0;
    g_cache.cached_low = 0;
    
    // 清空计算结果缓存
    g_cache.daily_avg_range = 0;
    g_cache.today_range = 0;
    g_cache.remaining_range = 0;
    g_cache.percentage = 0;
    
    // 重置时间戳
    g_cache.last_data_update = 0;
    g_cache.last_backup_update = 0;
    g_cache.last_daily_calc = 0;
    
    // 设置强制更新标志
    g_force_update = true;
    
    // 立即执行一次完整更新
    Print("【强制刷新】步骤1: 重新计算日均波幅...");
    g_cache.daily_avg_range = GetAverageDailyRange();
    g_cache.last_daily_calc = TimeCurrent();
    Print("【强制刷新】日均波幅: ", DoubleToString(g_cache.daily_avg_range, 5));
    
    Print("【强制刷新】步骤2: 重新计算时段统计...");
    CalculateSessionStatistics();
    g_cache.session_stats = g_sessionStats;
    Print("【强制刷新】时段统计已更新");
    
    Print("【强制刷新】步骤3: 强制触发变化检测...");
    bool change_detected = DetectPriceChange(); // 这会更新cached_high和cached_low
    Print("【强制刷新】变化检测结果: ", change_detected ? "检测到变化" : "无变化");
    
    Print("【强制刷新】步骤4: 重新计算实时数据...");
    UpdateRealTimeCalculations();
    Print("【强制刷新】今日波幅: ", DoubleToString(g_cache.today_range, 5),
          " 预估剩余: ", DoubleToString(g_cache.remaining_range, 5),
          " 完成百分比: ", DoubleToString(g_cache.percentage, 1), "%");
    
    Print("【强制刷新】步骤5: 更新显示...");
    UpdateDashboard();
    
    Print("【强制刷新】步骤6: 打印时段详细信息...");
    PrintSessionDetailInfo("强制刷新");
    
    // 更新时间戳
    g_cache.last_data_update = TimeCurrent();
    g_cache.last_backup_update = TimeCurrent();
    
    // 强制重绘图表
    ChartRedraw();
    
    Print("【强制刷新】极简强制刷新完成 - ", TimeToString(TimeCurrent()));
    Print("========================================");
}

//+------------------------------------------------------------------+
//| 【核心函数】极简价格变化检测 - 变化驱动的核心                    |
//+------------------------------------------------------------------+
bool DetectPriceChange()
{
    // 1. 数据验证：获取当前日内最高价和最低价
    double current_high = iHigh(_Symbol, PERIOD_D1, 0);
    double current_low = iLow(_Symbol, PERIOD_D1, 0);
    
    // 简单的数据验证
    if(current_high <= 0 || current_low <= 0 || current_high < current_low)
    {
        return false; // 数据异常，跳过本次更新
    }
    
    // 2. 变化检测：与缓存比较（不设置最小阈值，任何变化都算）
    bool high_changed = (MathAbs(current_high - g_cache.cached_high) > 0.000001);
    bool low_changed = (MathAbs(current_low - g_cache.cached_low) > 0.000001);
    
    // 3. 如果有变化，更新缓存并返回true
    if(high_changed || low_changed)
    {
        g_cache.cached_high = current_high;
        g_cache.cached_low = current_low;
        
        // 调试信息（每10次变化打印一次）
        static int change_count = 0;
        change_count++;
        if(change_count % 10 == 1)
        {
            Print("【变化检测 #", change_count, "】", 
                  high_changed ? "新高: " + DoubleToString(current_high, 5) : "",
                  low_changed ? " 新低: " + DoubleToString(current_low, 5) : "");
        }
        
        return true;
    }
    
    // 4. 无变化
    return false;
}

//+------------------------------------------------------------------+
//| 更新时段波动率计算（v12重构版 - 纽约时段拆分分析）               |
//+------------------------------------------------------------------+
void UpdateSessionVolatilityCalculations()
{
    Print("【v12时段波动率】开始更新时段内波动率分析（纽约时段拆分版）...");
    
    // 获取当前时段的枚举值
    ENUM_TRADING_SESSION current_session_enum = GetCurrentTradingSession();
    
    // 根据当前时段，从时段统计中查找对应的历史均幅
    switch(current_session_enum)
    {
        case SESSION_ASIA:
            g_cache.current_session_avg_range = g_cache.session_stats.asia_avg_range;
            Print("【v12时段分析】使用亚洲时段均幅: ", DoubleToString(g_cache.current_session_avg_range, 5));
            break;
        case SESSION_LONDON:
            g_cache.current_session_avg_range = g_cache.session_stats.london_avg_range;
            Print("【v12时段分析】使用伦敦时段均幅: ", DoubleToString(g_cache.current_session_avg_range, 5));
            break;
        case SESSION_OVERLAP:
            g_cache.current_session_avg_range = g_cache.session_stats.overlap_avg_range;
            Print("【v12重叠分析】使用重叠时段均幅(多方角力): ", DoubleToString(g_cache.current_session_avg_range, 5));
            break;
        case SESSION_NEWYORK:
            g_cache.current_session_avg_range = g_cache.session_stats.newyork_avg_range;
            Print("【v12纽约分析】使用纽约后半段均幅(一方主导): ", DoubleToString(g_cache.current_session_avg_range, 5));
            break;
        default:
            g_cache.current_session_avg_range = 0;
            Print("【v12时段分析】非主要时段，均幅设为0");
            break;
    }
    
    // 调用GetCurrentSessionRangeSoFar函数获取今日现幅
    g_cache.current_session_today_range = GetCurrentSessionRangeSoFar();
    
    // 计算剩余空间和完成进度
    if(g_cache.current_session_avg_range > 0)
    {
        g_cache.current_session_remaining_range = g_cache.current_session_avg_range - g_cache.current_session_today_range;
        g_cache.current_session_percentage = (g_cache.current_session_today_range / g_cache.current_session_avg_range * 100);
    }
    else
    {
        g_cache.current_session_remaining_range = 0;
        g_cache.current_session_percentage = 0;
    }
    
    // v12增强调试信息
    Print("【v12时段波动率】当前时段:", GetSessionName(current_session_enum), 
          " 历史均幅:", DoubleToString(g_cache.current_session_avg_range, 5),
          " 今日现幅:", DoubleToString(g_cache.current_session_today_range, 5),
          " 剩余空间:", DoubleToString(g_cache.current_session_remaining_range, 5),
          " 完成进度:", DoubleToString(g_cache.current_session_percentage, 1), "%");
}

//+------------------------------------------------------------------+
//| 获取当前时段从开始到现在的波幅（v12重构版本）                    |
//+------------------------------------------------------------------+
double GetCurrentSessionRangeSoFar()
{
    ENUM_TRADING_SESSION current_session = GetCurrentTradingSession();
    if(current_session == SESSION_NONE) 
    {
        Print("【v12时段波幅】当前非主要时段，返回0");
        return 0;
    }
    
    datetime current_time = TimeCurrent();
    datetime current_gmt = TimeGMT();
    
    // 获取当天的夏令时状态
    bool london_dst = IsLondonInDST(current_time);
    bool newyork_dst = IsNewYorkInDST(current_time);
    
    // 获取当天0点GMT时间作为基准
    datetime today_start = (datetime)(current_time / 86400) * 86400;
    
    // v12关键时间点计算
    datetime london_open = today_start + (london_dst ? 7*3600 : 8*3600);      // 伦敦开盘
    datetime london_close = today_start + (london_dst ? 16*3600 : 17*3600);   // 伦敦收盘
    datetime newyork_open = today_start + (newyork_dst ? 13*3600 + 30*60 : 14*3600 + 30*60); // 纽约开盘
    datetime asia_start = today_start - 2*3600;                               // 亚洲开始
    
    // 根据当前时段确定开始时间
    datetime session_start_time = 0;
    string session_name = "";
    
    switch(current_session)
    {
        case SESSION_ASIA:
            session_start_time = asia_start;
            session_name = "亚洲时段";
            break;
        case SESSION_LONDON:
            session_start_time = london_open;
            session_name = "伦敦时段";
            break;
        case SESSION_OVERLAP:
            session_start_time = newyork_open;  // v12关键：重叠时段从纽约开盘开始
            session_name = "重叠时段(多方角力)";
            break;
        case SESSION_NEWYORK:
            session_start_time = london_close;  // v12关键：纽约后半段从伦敦收盘开始
            session_name = "纽约后半段(一方主导)";
            break;
        default:
            Print("【v12错误】未知时段类型");
            return 0;
    }
    
    Print("【v12时段波幅】计算", session_name, "波幅: ", 
          TimeToString(session_start_time, TIME_MINUTES), " - ", 
          TimeToString(current_gmt, TIME_MINUTES));
    
    // 性能优化：使用CopyHigh和CopyLow函数批量获取M1周期数据
    int start_bar = iBarShift(_Symbol, PERIOD_M1, session_start_time);
    int end_bar = iBarShift(_Symbol, PERIOD_M1, current_gmt);
    
    if(start_bar < 0 || end_bar < 0 || start_bar <= end_bar) 
    {
        Print("【v12错误】K线索引异常: start_bar=", start_bar, " end_bar=", end_bar);
        return 0;
    }
    
    int bars_count = start_bar - end_bar + 1;
    double highs[], lows[];
    
    // 批量获取高低点数据
    if(CopyHigh(_Symbol, PERIOD_M1, end_bar, bars_count, highs) != bars_count ||
       CopyLow(_Symbol, PERIOD_M1, end_bar, bars_count, lows) != bars_count)
    {
        Print("【v12错误】获取时段K线数据失败，尝试备用方法...");
        
        // 备用方法：逐个获取数据
        double session_high = 0, session_low = DBL_MAX;
        bool found_data = false;
        
        for(int i = end_bar; i <= start_bar; i++)
        {
            double high = iHigh(_Symbol, PERIOD_M1, i);
            double low = iLow(_Symbol, PERIOD_M1, i);
            
            if(high > 0 && low > 0)
            {
                if(high > session_high) session_high = high;
                if(low < session_low) session_low = low;
                found_data = true;
            }
        }
        
        if(found_data)
        {
            double result = session_high - session_low;
            Print("【v12备用方法】", session_name, "波幅: ", DoubleToString(result, 5));
            return result;
        }
        else
        {
            Print("【v12错误】备用方法也失败");
            return 0;
        }
    }
    
    // 遍历获取到的高低点数组，找出最大值和最小值
    double session_high = highs[0];
    double session_low = lows[0];
    
    for(int i = 1; i < bars_count; i++)
    {
        if(highs[i] > session_high) session_high = highs[i];
        if(lows[i] < session_low) session_low = lows[i];
    }
    
    double result = session_high - session_low;
    Print("【v12时段波幅】", session_name, "当前波幅: ", DoubleToString(result, 5), 
          " (检查了", bars_count, "根K线)");
    
    return result;
}

//+------------------------------------------------------------------+
//| 实时计算更新函数（仅在有变化时调用）                             |
//+------------------------------------------------------------------+
void UpdateRealTimeCalculations()
{
    // 1. 更新时段信息
    ENUM_TRADING_SESSION new_session = GetCurrentTradingSession();
    string new_session_name = GetSessionName(new_session);
    
    if(new_session != g_cache.current_session || new_session_name != g_cache.session_name)
    {
        Print("【时段变化】从 '", g_cache.session_name, "' 切换到 '", new_session_name, "'");
    }
    
    g_cache.current_session = new_session;
    g_cache.session_name = new_session_name;
    
    // 2. 重新计算今日波幅
    g_cache.today_range = GetTodayRange();
    
    // 3. 重新计算预估剩余和百分比
    if(g_cache.daily_avg_range > 0)
    {
        g_cache.remaining_range = g_cache.daily_avg_range - g_cache.today_range;
        g_cache.percentage = (g_cache.today_range / g_cache.daily_avg_range * 100);
    }
    else
    {
        g_cache.remaining_range = 0;
        g_cache.percentage = 0;
    }
}

//+------------------------------------------------------------------+
//| 心跳显示更新（让用户知道系统在运行）                             |
//+------------------------------------------------------------------+
void UpdateHeartbeat()
{
    // 如果超过5分钟没有价格变化，在界面显示最后更新时间
    datetime current_time = TimeCurrent();
    if(current_time - g_cache.last_data_update > 300) // 5分钟
    {
        // 这里可以在界面显示"最后更新: XX:XX"的提示
        // 暂时通过日志输出
        Print("【心跳】系统正常运行 - 最后数据更新: ", TimeToString(g_cache.last_data_update, TIME_MINUTES));
    }
}


//+------------------------------------------------------------------+
//| 检查是否为新的一天                                               |
//+------------------------------------------------------------------+
bool IsNewDay()
{
    datetime current_day = (datetime)(TimeCurrent() / 86400) * 86400;
    datetime cached_day = (datetime)(g_cache.last_daily_calc / 86400) * 86400;
    return (current_day != cached_day);
}

//+------------------------------------------------------------------+
//| 优化的仪表盘显示更新函数（v11升级版）                            |
//+------------------------------------------------------------------+
void UpdateDashboard()
{
    if(!InpShowDashboard) return;
    
    Print("【v11仪表盘更新】开始更新显示数据...");
    
    // 使用缓存的数据更新显示（v11集成时段波动率分析）
    g_dashboard.UpdateDisplay(g_cache.current_session, 
                             g_cache.session_name, 
                             g_cache.daily_avg_range, 
                             g_cache.today_range, 
                             g_cache.remaining_range, 
                             g_cache.percentage, 
                             g_cache.session_stats,
                             g_cache.current_session_avg_range,      // v11: 当前时段历史均幅
                             g_cache.current_session_today_range,    // v11: 当前时段今日现幅
                             g_cache.current_session_remaining_range,// v11: 当前时段剩余空间
                             g_cache.current_session_percentage);    // v11: 当前时段完成进度
    
    Print("【v11仪表盘更新】显示数据更新完成");
}

//+------------------------------------------------------------------+
//| 获取当前交易时段（v12重构版本 - 纽约时段拆分逻辑）               |
//+------------------------------------------------------------------+
ENUM_TRADING_SESSION GetCurrentTradingSession()
{
    datetime current_gmt = TimeGMT();
    datetime current_time = TimeCurrent();
    
    // 获取当天的夏令时状态
    bool london_dst = IsLondonInDST(current_time);
    bool newyork_dst = IsNewYorkInDST(current_time);
    
    // 获取当天0点GMT时间作为基准
    datetime today_start = (datetime)(current_time / 86400) * 86400;
    
    // v12关键时间点计算
    datetime london_open = today_start + (london_dst ? 7*3600 : 8*3600);      // 伦敦开盘
    datetime london_close = today_start + (london_dst ? 16*3600 : 17*3600);   // 伦敦收盘
    datetime newyork_open = today_start + (newyork_dst ? 13*3600 + 30*60 : 14*3600 + 30*60); // 纽约开盘
    datetime newyork_close = today_start + 22*3600;                           // 纽约收盘
    datetime asia_start = today_start - 2*3600;                               // 亚洲开始（前一天22:00）
    
    Print("【v12时段判断】当前GMT: ", TimeToString(current_gmt, TIME_MINUTES));
    
    // v12核心逻辑：重叠时段优先判断
    // 重叠时段 = 纽约开盘到伦敦收盘（多方角力）
    if(current_gmt >= newyork_open && current_gmt < london_close)
    {
        Print("【v12重叠时段】检测到伦敦/纽约重叠时段！(", 
              TimeToString(newyork_open, TIME_MINUTES), " - ", 
              TimeToString(london_close, TIME_MINUTES), ")");
        return SESSION_OVERLAP;
    }
    
    // 亚洲时段：前一天22:00到伦敦开盘
    if(current_gmt >= asia_start && current_gmt < london_open)
    {
        Print("【v12时段判断】当前处于亚洲时段");
        return SESSION_ASIA;
    }
    
    // 伦敦时段：伦敦开盘到纽约开盘
    if(current_gmt >= london_open && current_gmt < newyork_open)
    {
        Print("【v12时段判断】当前处于伦敦时段");
        return SESSION_LONDON;
    }
    
    // 纽约后半段：伦敦收盘到纽约收盘（一方主导）
    if(current_gmt >= london_close && current_gmt < newyork_close)
    {
        Print("【v12时段判断】当前处于纽约后半段（一方主导）");
        return SESSION_NEWYORK;
    }
    
    // 处理跨日情况：当前时间在今天22:00之后，属于明天的亚洲时段
    if(current_gmt >= newyork_close)
    {
        Print("【v12时段判断】当前处于亚洲时段（跨日）");
        return SESSION_ASIA;
    }
    
    // 其他情况：非主要时段
    Print("【v12时段判断】当前处于非主要时段");
    return SESSION_NONE;
}

//+------------------------------------------------------------------+
//| 获取时段名称（v12更新版本）                                      |
//+------------------------------------------------------------------+
string GetSessionName(ENUM_TRADING_SESSION session)
{
    switch(session)
    {
        case SESSION_ASIA:    return "亚洲时段";
        case SESSION_LONDON:  return "伦敦时段";
        case SESSION_OVERLAP: return "重叠时段(多方角力)";
        case SESSION_NEWYORK: return "纽约后半段(一方主导)";
        default:              return "非主要时段";
    }
}

//+------------------------------------------------------------------+
//| 获取日均真实波幅（ADTR）- 移除过度缓存，由智能缓存系统管理       |
//+------------------------------------------------------------------+
double GetDailyAverageRange()
{
    double sum = 0;
    int valid_count = 0;
    
    // 计算过去N天的真实波幅（每次都重新计算，但由缓存系统控制调用频率）
    for(int i = 1; i <= InpAvgDays; i++)
    {
        double high = iHigh(_Symbol, PERIOD_D1, i);
        double low = iLow(_Symbol, PERIOD_D1, i);
        double prev_close = iClose(_Symbol, PERIOD_D1, i + 1);
        
        if(high > 0 && low > 0 && prev_close > 0)
        {
            // 计算真实波幅：TR = Max((High-Low), abs(High-Close[1]), abs(Low-Close[1]))
            double tr1 = high - low;
            double tr2 = MathAbs(high - prev_close);
            double tr3 = MathAbs(low - prev_close);
            double true_range = MathMax(tr1, MathMax(tr2, tr3));
            
            sum += true_range;
            valid_count++;
        }
    }
    
    return (valid_count > 0) ? (sum / valid_count) : 0;
}

//+------------------------------------------------------------------+
//| 获取日均真实波幅（备用函数名）                                   |
//+------------------------------------------------------------------+
double GetAverageDailyRange()
{
    return GetDailyAverageRange();
}

//+------------------------------------------------------------------+
//| 获取今日已实现真实波幅 - 修复版本                                |
//+------------------------------------------------------------------+
double GetTodayRange()
{
    // 使用多种方式获取日线数据，确保准确性
    double high = 0, low = 0, prev_close = 0;
    
    // 方法1：直接获取日线数据
    high = iHigh(_Symbol, PERIOD_D1, 0);
    low = iLow(_Symbol, PERIOD_D1, 0);
    prev_close = iClose(_Symbol, PERIOD_D1, 1);
    
    // 方法2：如果方法1失败，使用当前价格作为备选
    if(high <= 0 || low <= 0)
    {
        MqlTick tick;
        if(SymbolInfoTick(_Symbol, tick))
        {
            // 获取今日开盘时间
            datetime today_start = (datetime)(TimeCurrent() / 86400) * 86400;
            
            // 在当前周期上查找今日的最高最低价
            int bars_today = 0;
            double temp_high = 0, temp_low = DBL_MAX;
            
            for(int i = 0; i < 1440; i++) // 最多检查1440根1分钟K线（24小时）
            {
                datetime bar_time = iTime(_Symbol, PERIOD_M1, i);
                if(bar_time < today_start) break; // 超出今日范围
                
                double bar_high = iHigh(_Symbol, PERIOD_M1, i);
                double bar_low = iLow(_Symbol, PERIOD_M1, i);
                
                if(bar_high > 0 && bar_low > 0)
                {
                    if(bar_high > temp_high) temp_high = bar_high;
                    if(bar_low < temp_low) temp_low = bar_low;
                    bars_today++;
                }
            }
            
            if(temp_high > 0 && temp_low < DBL_MAX)
            {
                high = temp_high;
                low = temp_low;
                Print("使用备选方法计算今日波幅 - 检查了", bars_today, "根K线");
            }
        }
    }
    
    // 获取昨日收盘价
    if(prev_close <= 0)
    {
        prev_close = iClose(_Symbol, PERIOD_D1, 1);
        if(prev_close <= 0)
        {
            // 如果还是获取不到，使用今日开盘价作为近似值
            prev_close = iOpen(_Symbol, PERIOD_D1, 0);
        }
    }
    
    // 增强调试信息：每次都打印（用于排查问题）
    static int debug_count = 0;
    debug_count++;
    if(debug_count % 10 == 1) // 每10次调用打印一次
    {
        Print("【增强调试】今日波幅计算 #", debug_count, 
              " 图表周期:", EnumToString(Period()),
              " 最高=", DoubleToString(high, 5), 
              " 最低=", DoubleToString(low, 5), 
              " 昨收=", DoubleToString(prev_close, 5));
    }
    
    if(high <= 0 || low <= 0 || prev_close <= 0)
    {
        Print("【错误】获取价格数据失败: 最高=", high, " 最低=", low, " 昨收=", prev_close, 
              " 当前周期=", EnumToString(Period()));
        return 0;
    }
    
    // 计算今日真实波幅
    double tr1 = high - low;
    double tr2 = MathAbs(high - prev_close);
    double tr3 = MathAbs(low - prev_close);
    double result = MathMax(tr1, MathMax(tr2, tr3));
    
    // 增强调试信息：显示计算结果
    if(debug_count % 10 == 1)
    {
        Print("【增强调试】真实波幅计算结果: TR1(H-L)=", DoubleToString(tr1, 5), 
              " TR2(|H-PC|)=", DoubleToString(tr2, 5), 
              " TR3(|L-PC|)=", DoubleToString(tr3, 5), 
              " 最终TR=", DoubleToString(result, 5));
    }
    
    return result;
}

//+------------------------------------------------------------------+
//| 计算时段统计数据（v12重构版本 - 纽约时段拆分逻辑）               |
//+------------------------------------------------------------------+
void CalculateSessionStatistics()
{
    Print("【v12核心重构】开始计算时段统计数据，实现纽约时段拆分逻辑...");
    
    // 初始化统计变量
    double asia_sum = 0, london_sum = 0, overlap_sum = 0, newyork_sum = 0;
    int valid_days = 0;
    
    // 循环过去N天
    for(int day = 1; day <= InpAvgDays; day++)
    {
        datetime day_start = iTime(_Symbol, PERIOD_D1, day);
        if(day_start == 0) continue;
        
        // 为每一天计算动态时段时间
        bool london_dst = IsLondonInDST(day_start);
        bool newyork_dst = IsNewYorkInDST(day_start);
        
        // v12关键时间点计算（小时）
        int london_open_hour = london_dst ? 7 : 8;                    // 伦敦开盘
        int london_close_hour = london_dst ? 16 : 17;                 // 伦敦收盘
        int newyork_open_hour = newyork_dst ? 13 : 14;                // 纽约开盘（简化为整点）
        int newyork_close_hour = 22;                                  // 纽约收盘
        int asia_start_hour = 22;                                     // 亚洲开始（前一天）
        
        // v12新的时段定义：
        // 1. 亚洲时段：前一天22:00到伦敦开盘
        double asia_range = CalculateSessionDailyRangeDynamic(day_start, asia_start_hour, london_open_hour);
        
        // 2. 伦敦时段：伦敦开盘到纽约开盘
        double london_range = CalculateSessionDailyRangeDynamic(day_start, london_open_hour, newyork_open_hour);
        
        // 3. 重叠时段：纽约开盘到伦敦收盘（多方角力）
        double overlap_range = 0;
        if(newyork_open_hour < london_close_hour)
        {
            overlap_range = CalculateSessionDailyRangeDynamic(day_start, newyork_open_hour, london_close_hour);
            
            // 调试信息：每5天打印一次重叠时段详情
            if(day % 5 == 1)
            {
                Print("【v12重叠调试】第", day, "天重叠时段: ", newyork_open_hour, ":00-", london_close_hour, ":00 GMT, 波幅:", DoubleToString(overlap_range, 5));
            }
        }
        else
        {
            Print("【v12警告】第", day, "天无重叠时段: 纽约", newyork_open_hour, "点 >= 伦敦收盘", london_close_hour, "点");
        }
        
        // 4. 纽约后半段：伦敦收盘到纽约收盘（一方主导）
        double newyork_range = CalculateSessionDailyRangeDynamic(day_start, london_close_hour, newyork_close_hour);
        
        // 累加有效数据
        if(asia_range > 0) asia_sum += asia_range;
        if(london_range > 0) london_sum += london_range;
        if(overlap_range > 0) overlap_sum += overlap_range;
        if(newyork_range > 0) newyork_sum += newyork_range;
        
        valid_days++;
        
        // 每5天打印一次进度
        if(day % 5 == 0)
        {
            Print("【v12重构】时段统计进度: ", day, "/", InpAvgDays, " 天 (纽约时段拆分)");
        }
    }
    
    // 计算平均值
    g_sessionStats.asia_avg_range = (valid_days > 0) ? (asia_sum / valid_days) : 0;
    g_sessionStats.london_avg_range = (valid_days > 0) ? (london_sum / valid_days) : 0;
    g_sessionStats.overlap_avg_range = (valid_days > 0) ? (overlap_sum / valid_days) : 0;
    g_sessionStats.newyork_avg_range = (valid_days > 0) ? (newyork_sum / valid_days) : 0;
    
    Print("【v12重构完成】纽约时段拆分统计数据:");
    Print("  亚洲时段: ", DoubleToString(g_sessionStats.asia_avg_range, 5));
    Print("  伦敦时段: ", DoubleToString(g_sessionStats.london_avg_range, 5));
    Print("  重叠时段(多方角力): ", DoubleToString(g_sessionStats.overlap_avg_range, 5));
    Print("  纽约后半段(一方主导): ", DoubleToString(g_sessionStats.newyork_avg_range, 5));
}

//+------------------------------------------------------------------+
//| 计算指定日期和时段的波幅（动态版本）                             |
//+------------------------------------------------------------------+
double CalculateSessionDailyRangeDynamic(datetime day_start, int start_hour, int end_hour)
{
    double session_high = 0;
    double session_low = DBL_MAX;
    bool found_data = false;
    
    // 处理跨日情况（如亚洲时段从前一天22:00开始）
    datetime session_start_time = day_start + start_hour * 3600;
    datetime session_end_time = day_start + end_hour * 3600;
    
    if(start_hour > end_hour) // 跨日情况
    {
        session_start_time = day_start - (24 - start_hour) * 3600; // 前一天的时间
    }
    
    // 在M15图表上查找该时段的所有K线
    int total_minutes = (int)((session_end_time - session_start_time) / 60);
    int bars_to_check = total_minutes / 15; // 15分钟K线数量
    
    for(int i = 0; i < bars_to_check; i++)
    {
        datetime bar_time = session_start_time + (i * 15 * 60);
        
        int shift = iBarShift(_Symbol, PERIOD_M15, bar_time);
        if(shift >= 0)
        {
            double high = iHigh(_Symbol, PERIOD_M15, shift);
            double low = iLow(_Symbol, PERIOD_M15, shift);
            
            if(high > 0 && low > 0)
            {
                if(high > session_high) session_high = high;
                if(low < session_low) session_low = low;
                found_data = true;
            }
        }
    }
    
    return found_data ? (session_high - session_low) : 0;
}
